#!/bin/bash

# Complete Deployment Setup Script
set -e

echo "🚀 Setting up ResumeAI Pro for deployment..."

# Make scripts executable
chmod +x setup-deployment.sh

echo "📋 Deployment Setup Checklist:"
echo ""

# 1. GitHub Setup
echo "1️⃣ GITHUB SETUP"
echo "   ✅ Repository structure ready"
echo "   ✅ .gitignore configured"
echo "   ✅ GitHub Actions workflows created"
echo ""

# 2. Backend Setup (Fly.io)
echo "2️⃣ BACKEND SETUP (Fly.io)"
echo "   📁 Navigate to backend directory: cd backend"
echo "   🔧 Install Fly CLI: curl -L https://fly.io/install.sh | sh"
echo "   🔐 Login to Fly: fly auth login"
echo "   📱 Create app: fly launch --no-deploy --name resume-ai-backend --region sjc"
echo "   💾 Create volume: fly volumes create resume_storage --region sjc --size 10"
echo "   🔑 Set secrets:"
echo "      fly secrets set GEMINI_API_KEY=your_actual_key"
echo "      fly secrets set GOOGLE_API_KEY=your_actual_key"
echo "      fly secrets set ALLOWED_ORIGINS=https://your-domain.vercel.app"
echo ""

# 3. Frontend Setup (Vercel)
echo "3️⃣ FRONTEND SETUP (Vercel)"
echo "   🌐 Go to vercel.com and connect your GitHub repo"
echo "   ⚙️ Set environment variables in Vercel dashboard:"
echo "      NEXT_PUBLIC_API_URL=https://resume-ai-backend.fly.dev"
echo "      NEXT_PUBLIC_MAX_FILE_SIZE_MB=10"
echo ""

# 4. GitHub Secrets
echo "4️⃣ GITHUB SECRETS (for CI/CD)"
echo "   Go to GitHub repo → Settings → Secrets and variables → Actions"
echo "   Add these secrets:"
echo "   🔑 FLY_API_TOKEN (from fly auth token)"
echo "   🔑 VERCEL_TOKEN (from vercel.com/account/tokens)"
echo "   🔑 VERCEL_ORG_ID (from vercel.com/account)"
echo "   🔑 VERCEL_PROJECT_ID (from your project settings)"
echo "   🔑 NEXT_PUBLIC_API_URL=https://resume-ai-backend.fly.dev"
echo "   🔑 NEXT_PUBLIC_MAX_FILE_SIZE_MB=10"
echo ""

# 5. Deployment Order
echo "5️⃣ DEPLOYMENT ORDER"
echo "   1. Push code to GitHub main branch"
echo "   2. Backend will auto-deploy to Fly.io (if secrets are set)"
echo "   3. Frontend will auto-deploy to Vercel (if connected)"
echo "   4. Update CORS settings in backend with your Vercel domain"
echo ""

echo "🎉 Setup complete! Follow the steps above to deploy your application."
echo ""
echo "📚 Quick Commands:"
echo "   • Test backend locally: cd backend && python main.py"
echo "   • Test frontend locally: cd frontend && npm run dev"
echo "   • Manual backend deploy: cd backend && fly deploy"
echo "   • Check backend logs: fly logs"
echo ""