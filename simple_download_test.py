#!/usr/bin/env python3
"""
Simple download functionality test using only built-in libraries
"""
import urllib.request
import urllib.parse
import json
import os
import time

def test_download_endpoints():
    """Test PDF and DOCX download functionality"""
    print("🧪 Testing Download Functionality")
    print("=" * 40)

    # Sample LaTeX content
    latex_content = """\\documentclass{article}
\\usepackage[utf8]{inputenc}
\\title{Download Test Document}
\\author{LaTeX Editor}
\\date{\\today}

\\begin{document}
\\maketitle

\\section{Introduction}
This document is used to test the PDF and DOCX download functionality.

\\section{Features}
\\begin{itemize}
\\item PDF download
\\item DOCX conversion and download
\\item File naming with timestamps
\\end{itemize}

\\end{document}"""

    base_url = "http://localhost:8000"

    print("1. Testing backend health...")
    try:
        with urllib.request.urlopen(f"{base_url}/api/health") as response:
            health_data = json.loads(response.read().decode())
            print(f"✅ Backend healthy: {health_data['status']}")
    except Exception as e:
        print(f"❌ Backend health check failed: {e}")
        return False

    print("\n2. Testing LaTeX compilation...")
    try:
        # Prepare compilation request
        compile_data = {
            "latex_content": latex_content,
            "job_id": f"download_test_{int(time.time())}"
        }

        req_data = json.dumps(compile_data).encode()
        req = urllib.request.Request(
            f"{base_url}/api/latex/compile",
            data=req_data,
            headers={'Content-Type': 'application/json'}
        )

        with urllib.request.urlopen(req) as response:
            compile_result = json.loads(response.read().decode())
            print(f"✅ Compilation started: {compile_result['status']}")
            job_id = compile_result['job_id']

            # Wait for compilation to complete
            print("   Waiting for compilation...")
            for i in range(10):  # Wait up to 10 seconds
                time.sleep(1)
                status_req = urllib.request.Request(f"{base_url}/api/latex/status/{job_id}")
                try:
                    with urllib.request.urlopen(status_req) as status_response:
                        status_data = json.loads(status_response.read().decode())
                        if status_data['status'] == 'success':
                            pdf_url = status_data['pdf_url']
                            print(f"✅ Compilation successful: {pdf_url}")
                            break
                        elif status_data['status'] == 'error':
                            print(f"❌ Compilation failed: {status_data.get('error', 'Unknown error')}")
                            return False
                except Exception as e:
                    print(f"   Status check {i+1}/10: {e}")
            else:
                print("❌ Compilation timed out")
                return False

    except Exception as e:
        print(f"❌ LaTeX compilation test failed: {e}")
        return False

    print("\n3. Testing DOCX conversion...")
    try:
        # Test DOCX conversion
        docx_data = {
            "latex_content": latex_content
        }

        req_data = json.dumps(docx_data).encode()
        req = urllib.request.Request(
            f"{base_url}/api/latex/convert-to-docx",
            data=req_data,
            headers={'Content-Type': 'application/json'}
        )

        with urllib.request.urlopen(req) as response:
            if response.headers.get('Content-Type') == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                print("✅ DOCX conversion successful (binary response received)")
            else:
                response_text = response.read().decode()
                print(f"⚠️  DOCX conversion response: {response_text[:200]}...")

    except Exception as e:
        print(f"❌ DOCX conversion test failed: {e}")

    print("\n4. Frontend Testing Instructions:")
    print("📖 Open http://localhost:3002/latex-editor in your browser")
    print("✏️  Enter LaTeX content and compile")
    print("💾 Test the download buttons:")
    print("   - 'Download PDF' should download the compiled PDF")
    print("   - 'Download DOCX' should convert and download as Word document")

    print("\n🎯 Expected Download Behavior:")
    print("✅ PDF downloads should work immediately after compilation")
    print("✅ DOCX downloads should trigger conversion and download")
    print("✅ Files should be named with timestamps (e.g., document_20250811_144500.pdf)")

    return True

if __name__ == "__main__":
    print("🚀 Download Functionality Test")
    print("=" * 40)
    test_download_endpoints()
    print("\n✅ Test completed!")
    print("🌐 Open the browser at http://localhost:3002/latex-editor to test manually")
