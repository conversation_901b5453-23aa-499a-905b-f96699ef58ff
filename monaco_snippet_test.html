<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monaco Editor Snippet Test</title>
    <script src="https://unpkg.com/monaco-editor@0.44.0/min/vs/loader.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        #container { border: 1px solid #ccc; }
        #editor { height: 300px; }
        .controls { margin: 10px 0; }
        button { margin: 5px; padding: 8px 12px; }
    </style>
</head>
<body>
    <h1>Monaco Editor Snippet Insertion Test</h1>

    <div class="controls">
        <button onclick="insertSnippet('\\\\textbf{${1:bold text}}')">Insert Bold</button>
        <button onclick="insertSnippet('\\\\textit{${1:italic text}}')">Insert Italic</button>
        <button onclick="insertSnippet('\\\\begin{itemize}\\n  \\\\item ${1:First item}\\n  \\\\item ${2:Second item}\\n\\\\end{itemize}')">Insert List</button>
    </div>

    <div id="container">
        <div id="editor"></div>
    </div>

    <div class="controls">
        <button onclick="getValue()">Get Editor Content</button>
        <button onclick="testSnippetInsertion()">Test Snippet Insertion</button>
    </div>

    <div id="output"></div>

    <script>
        let editor;

        require.config({ paths: { 'vs': 'https://unpkg.com/monaco-editor@0.44.0/min/vs' }});

        require(['vs/editor/editor.main'], function () {
            editor = monaco.editor.create(document.getElementById('editor'), {
                value: '\\\\documentclass{article}\\n\\n\\\\begin{document}\\n\\nTest content here.\\n\\n\\\\end{document}',
                language: 'latex',
                theme: 'vs-light',
                automaticLayout: true
            });

            console.log('Monaco Editor initialized successfully!');
        });

        function insertSnippet(snippet) {
            if (!editor) {
                console.error('Editor not initialized');
                return;
            }

            // Use the same approach as our fixed LaTeX editor
            const position = editor.getPosition();
            if (position) {
                editor.focus();

                // Process snippet with placeholders
                let processedSnippet = snippet;
                const placeholderRegex = /\\$\\{(\\d+):([^}]+)\\}/g;
                let firstPlaceholder = null;

                processedSnippet = processedSnippet.replace(placeholderRegex, (match, index, text) => {
                    if (!firstPlaceholder && index === '1') {
                        firstPlaceholder = { text, index: parseInt(index) };
                    }
                    return text;
                });

                // Insert the processed snippet
                editor.executeEdits('insertSnippet', [{
                    range: {
                        startLineNumber: position.lineNumber,
                        startColumn: position.column,
                        endLineNumber: position.lineNumber,
                        endColumn: position.column
                    },
                    text: processedSnippet,
                    forceMoveMarkers: true
                }]);

                console.log('Snippet inserted successfully!');

                // If there was a first placeholder, select it
                if (firstPlaceholder) {
                    setTimeout(() => {
                        const newPosition = editor.getPosition();
                        if (newPosition) {
                            const searchText = firstPlaceholder.text;
                            const snippetIndex = processedSnippet.indexOf(searchText);

                            if (snippetIndex !== -1) {
                                const beforePlaceholder = processedSnippet.substring(0, snippetIndex);
                                const newlines = beforePlaceholder.split('\\n');
                                const lineOffset = newlines.length - 1;
                                const columnOffset = lineOffset > 0 ? newlines[newlines.length - 1].length : beforePlaceholder.length;

                                const startLine = position.lineNumber + lineOffset;
                                const startCol = lineOffset > 0 ? columnOffset + 1 : position.column + columnOffset;

                                editor.setSelection({
                                    startLineNumber: startLine,
                                    startColumn: startCol,
                                    endLineNumber: startLine,
                                    endColumn: startCol + searchText.length
                                });

                                console.log('Placeholder text selected!');
                            }
                        }
                    }, 50);
                }
            }
        }

        function getValue() {
            if (editor) {
                const content = editor.getValue();
                document.getElementById('output').innerHTML = '<h3>Editor Content:</h3><pre>' + content + '</pre>';
                console.log('Editor content:', content);
            }
        }

        function testSnippetInsertion() {
            console.log('Testing snippet insertion...');
            insertSnippet('\\\\textbf{${1:test text}}');

            setTimeout(() => {
                const content = editor.getValue();
                if (content.includes('\\\\textbf{test text}')) {
                    document.getElementById('output').innerHTML = '<h3>✅ Snippet insertion test passed!</h3>';
                    console.log('✅ Test passed: Snippet inserted correctly');
                } else {
                    document.getElementById('output').innerHTML = '<h3>❌ Snippet insertion test failed!</h3>';
                    console.log('❌ Test failed: Snippet not inserted correctly');
                }
            }, 100);
        }
    </script>
</body>
</html>
