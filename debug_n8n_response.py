#!/usr/bin/env python3
"""
Detailed test script to debug the n8n workflow response
"""

import requests
import json
import re

# N8N webhook URL
WEBHOOK_URL = "https://n8n-new-k5wy.onrender.com/webhook/resume-professional"

def clean_resume_text(text):
    """Clean resume text to remove problematic Unicode characters"""
    if not text:
        return ''

    # Remove ALL control characters (including NULL and tab characters)
    cleaned = re.sub(r'[\u0000-\u001F\u007F-\u009F]', '', text)

    # Replace problematic Unicode characters
    replacements = {
        '\u2028': ' ',      # Line separator
        '\u2029': ' ',      # Paragraph separator
        '\u00A0': ' ',      # Non-breaking space
        '\uFEFF': '',       # Byte order mark
        '\u200B': '',       # Zero-width space
        '\u200C': '',       # Zero-width non-joiner
        '\u200D': '',       # Zero-width joiner
        '\u2014': ' - ',    # Em dash
        '\u2013': ' - ',    # En dash
        '\u2018': "'",      # Left single quote
        '\u2019': "'",      # Right single quote
        '\u201C': '"',      # Left double quote
        '\u201D': '"',      # Right double quote
        '\u2026': '...',    # Ellipsis
    }

    for unicode_char, replacement in replacements.items():
        cleaned = cleaned.replace(unicode_char, replacement)

    # Normalize whitespace
    cleaned = re.sub(r'\s+', ' ', cleaned).strip()

    return cleaned

def main():
    # Simple, clean resume text for testing
    clean_resume_text_sample = """Eshetu Feleke
<EMAIL>
+33 769 588 876
Station F, Paris, France

Product and AI leader with 8+ years driving the design, development, and launch of AI-powered SaaS solutions.

AI Product Builder / Co-Founder at FeedbackGPT.com (2022 - Present)
- Designed, shipped, and iterated 7+ robust AI applications for enterprise clients
- Built and managed high-performing Agile team (6 members)
- Established product strategy and OKRs

Technical Product Manager at Ezzy Feedback, Inc. (2019-2022)
- Drove the roadmap, UX, and Agile release process for 3 social and productivity platforms
- Increased MAU by 32%, reduced customer churn by 14%"""

    # Clean the text to remove any problematic characters
    cleaned_resume = clean_resume_text(clean_resume_text_sample)

    # Prepare the payload
    payload = {
        "resume_text": cleaned_resume,
        "job_description": "Product Manager position at AI FinTech startup, working on Finance and Accounting integration products.",
        "target_role": "Product Manager",
        "target_industry": "FinTech"
    }

    print("=== DETAILED N8N WORKFLOW TEST ===")
    print(f"Webhook URL: {WEBHOOK_URL}")
    print(f"Resume text length: {len(cleaned_resume)} characters")

    # Check for any remaining control characters
    import re
    control_chars = re.findall(r'[\u0000-\u001F\u007F-\u009F]', cleaned_resume)
    print(f"Control characters in cleaned text: {len(control_chars)}")

    try:
        print("\nSending request to n8n workflow...")
        response = requests.post(
            WEBHOOK_URL,
            json=payload,
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'ResumeAI-Test/1.0'
            },
            timeout=180  # 3 minute timeout for processing
        )

        print(f"Response status: {response.status_code}")
        print(f"Response content length: {len(response.content)} bytes")
        print(f"Response encoding: {response.encoding}")

        # Get raw response text
        raw_text = response.text
        print(f"Raw response length: {len(raw_text)} characters")

        # Show first 500 characters of response
        print(f"\nFirst 500 characters of response:")
        print(repr(raw_text[:500]))

        if response.status_code == 200:
            try:
                result = response.json()
                print("\n✅ VALID JSON RESPONSE RECEIVED")
                print(f"Success: {result.get('success', 'N/A')}")
                print(f"Job ID: {result.get('job_id', 'N/A')}")
                print(f"Status: {result.get('status', 'N/A')}")

                # Check if PDF was generated
                if result.get('results', {}).get('pdf_generated'):
                    print("✅ PDF GENERATION: SUCCESS")
                else:
                    print("❌ PDF GENERATION: FAILED OR UNKNOWN")

                return True

            except json.JSONDecodeError as e:
                print(f"❌ JSON DECODE ERROR: {e}")
                print("Response appears to be HTML or other format")

                # Check if it's HTML error page
                if raw_text.strip().startswith('<!DOCTYPE') or raw_text.strip().startswith('<html'):
                    print("Response appears to be HTML (likely error page)")
                    # Extract title if possible
                    import re
                    title_match = re.search(r'<title[^>]*>([^<]*)</title>', raw_text, re.IGNORECASE)
                    if title_match:
                        print(f"HTML title: {title_match.group(1)}")
                else:
                    print("Response is not HTML, checking format...")
                    print(f"Response starts with: {repr(raw_text[:50])}")

        else:
            print(f"❌ HTTP ERROR: {response.status_code}")
            print(f"Error response: {raw_text[:200]}...")

    except requests.exceptions.Timeout:
        print("❌ REQUEST TIMEOUT: Workflow taking too long")
    except requests.exceptions.RequestException as e:
        print(f"❌ REQUEST ERROR: {e}")

    return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'✅ TEST PASSED' if success else '❌ TEST FAILED'}")
