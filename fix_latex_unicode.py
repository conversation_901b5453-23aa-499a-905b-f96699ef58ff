#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to test and fix Unicode control character issues in LaTeX compilation
Addresses the ^^@9 error from tab characters (U+0009) in resume text
"""

import re
import json

# Sample problematic text from the LaTeX Sanitizer pinned data
problematic_text = """AI Product Manager & Founder with 8+ years building B2B SaaS and AI products (LLMs, RAG, APIs) for enterprise customers. Proven at leading distributed teams, defining product strategy and OKRs, and launching commercial integrations that drove up to 37% efficiency gains and EUR 240K+ recurring revenue. Experienced in discovery, user research, GTM messaging, and partnering with enterprise stakeholders \u00009— ready to own Finance & Accounting integrations and the CFO stack at a high-growth AI FinTech."""

def analyze_text(text):
    """Analyze text for problematic Unicode characters"""
    print("=== UNICODE ANALYSIS ===")
    print(f"Original text length: {len(text)}")

    # Check for control characters
    control_chars = re.findall(r'[\u0000-\u001F\u007F-\u009F]', text)
    print(f"Control characters found: {len(control_chars)}")
    if control_chars:
        for char in set(control_chars):
            print(f"  - U+{ord(char):04X} ({repr(char)}) appears {control_chars.count(char)} times")

    # Check specifically for tab character
    tab_count = text.count('\u0009')
    print(f"Tab characters (U+0009): {tab_count}")

    return control_chars

def clean_text_for_latex(text):
    """Comprehensive text cleaning for LaTeX compatibility"""
    if not text:
        return ''

    cleaned = text

    # Remove ALL control characters (U+0000 to U+001F, U+007F to U+009F)
    cleaned = re.sub(r'[\u0000-\u001F\u007F-\u009F]', '', cleaned)

    # Replace problematic Unicode characters
    replacements = {
        '\u2028': ' ',      # Line separator
        '\u2029': ' ',      # Paragraph separator
        '\u00A0': ' ',      # Non-breaking space
        '\uFEFF': '',       # Byte order mark
        '\u200B': '',       # Zero-width space
        '\u200C': '',       # Zero-width non-joiner
        '\u200D': '',       # Zero-width joiner
        '\u2014': '---',    # Em dash
        '\u2013': '--',     # En dash
        '\u2018': "'",      # Left single quote
        '\u2019': "'",      # Right single quote
        '\u201C': '"',      # Left double quote
        '\u201D': '"',      # Right double quote
        '\u2026': '...',    # Ellipsis
    }

    for unicode_char, replacement in replacements.items():
        cleaned = cleaned.replace(unicode_char, replacement)

    # Escape LaTeX special characters
    latex_escapes = {
        '&': '\\&',
        '%': '\\%',
        '#': '\\#',
        '$': '\\$',
        '^': '\\textasciicircum{}',
        '~': '\\textasciitilde{}',
        '{': '\\{',
        '}': '\\}',
    }

    for char, escape in latex_escapes.items():
        if not char == '_':  # Handle underscore separately to avoid double escaping
            cleaned = cleaned.replace(char, escape)

    # Handle underscore (but not if already escaped)
    cleaned = re.sub(r'(?<!\\)_', '\\_', cleaned)

    # Normalize whitespace
    cleaned = re.sub(r'\s+', ' ', cleaned).strip()

    return cleaned

def test_latex_compilation_fix():
    """Test the fix for LaTeX compilation issues"""
    print("=== TESTING LATEX COMPILATION FIX ===\n")

    # Analyze the problematic text
    print("1. ANALYZING PROBLEMATIC TEXT:")
    analyze_text(problematic_text)

    print(f"\n2. PROBLEMATIC TEXT SNIPPET:")
    # Find the area around the tab character
    tab_index = problematic_text.find('\u0009')
    if tab_index != -1:
        start = max(0, tab_index - 20)
        end = min(len(problematic_text), tab_index + 20)
        snippet = problematic_text[start:end]
        print(f"Around tab character: {repr(snippet)}")

    print(f"\n3. CLEANING TEXT:")
    cleaned_text = clean_text_for_latex(problematic_text)
    print(f"Cleaned text length: {len(cleaned_text)}")

    # Verify no control characters remain
    remaining_control = re.findall(r'[\u0000-\u001F\u007F-\u009F]', cleaned_text)
    print(f"Remaining control characters: {len(remaining_control)}")

    print(f"\n4. CLEANED TEXT:")
    print(cleaned_text)

    print(f"\n5. N8N JAVASCRIPT CODE:")
    print("""
// Enhanced Unicode cleaning function for n8n
function cleanTextForLatex(text) {
  if (!text) return '';

  return text
    // Remove ALL control characters (including tab U+0009 causing ^^@9 error)
    .replace(/[\\u0000-\\u001F\\u007F-\\u009F]/g, '')
    // Replace problematic Unicode characters
    .replace(/[\\u2028\\u2029]/g, ' ')     // Line/Paragraph separators
    .replace(/[\\u00A0]/g, ' ')           // Non-breaking space
    .replace(/[\\uFEFF]/g, '')            // Byte order mark
    .replace(/[\\u200B-\\u200D]/g, '')    // Zero-width characters
    .replace(/[\\u2014\\u2013]/g, '---')  // Em/en dashes
    .replace(/[\\u2018\\u2019]/g, "'")    // Smart quotes
    .replace(/[\\u201C\\u201D]/g, '"')    // Smart double quotes
    .replace(/[\\u2026]/g, '...')         // Ellipsis
    // Normalize whitespace
    .replace(/\\s+/g, ' ')
    .trim();
}
    """)

if __name__ == "__main__":
    test_latex_compilation_fix()
