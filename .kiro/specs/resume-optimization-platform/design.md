# Design Document

## Overview

The AI-Powered Resume Optimization Platform is architected as a modern, scalable web application that combines client-side processing with cloud-based AI workflows. The system uses a hybrid approach with Next.js frontend for user experience, N8N workflows for AI processing orchestration, and a FastAPI backend for LaTeX compilation and file handling. The architecture prioritizes performance, user experience, and reliability while maintaining cost-effectiveness through efficient AI API usage.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        A[Next.js Frontend]
        B[Monaco LaTeX Editor]
        C[PDF Viewer]
    end

    subgraph "API Gateway Layer"
        D[Next.js API Routes]
        E[FastAPI Backend]
    end

    subgraph "Processing Layer"
        F[N8N Workflow Engine]
        G[AI Agents Service]
        H[LaTeX Compiler Service]
    end

    subgraph "External Services"
        I[OpenAI/Gemini APIs]
        J[LaTeX Online Compiler]
        K[File Storage]
    end

    A --> D
    A --> E
    B --> E
    D --> F
    E --> G
    E --> H
    F --> I
    H --> J
    E --> K

    style A fill:#e1f5fe
    style F fill:#f3e5f5
    style I fill:#fff3e0
```

### System Components

#### Frontend Architecture (Next.js 15 + React 19)
- **App Router**: Modern Next.js routing with server components
- **State Management**: Zustand for client-side state with persistence
- **UI Framework**: Tailwind CSS with Radix UI components
- **File Processing**: Client-side PDF/DOCX text extraction
- **Real-time Updates**: WebSocket connections for progress tracking

#### Backend Services Architecture
- **API Gateway**: Next.js API routes for client-server communication
- **Processing Engine**: FastAPI backend for file handling and LaTeX compilation
- **Workflow Orchestration**: N8N for AI processing pipeline management
- **Compilation Service**: Docker-based LaTeX compilation with error handling

#### AI Processing Pipeline
- **Primary**: N8N workflow with single-call optimization (efficient)
- **Fallback**: CrewAI multi-agent system (comprehensive but slower)
- **LLM Integration**: Google Gemini 2.0 Flash with rate limiting and quota management
- **Error Recovery**: Circuit breaker pattern with exponential backoff

## Components and Interfaces

### Frontend Components

#### Core Application Components
```typescript
// Main application flow
interface AppState {
  currentStep: 'upload' | 'job-description' | 'optimization' | 'results'
  uploadedFile: File | null
  jobDescription: string
  optimizationResults: ResumeOptimizationResponse | null
  processingProgress: number
}

// File upload component
interface FileUploadProps {
  onFileUpload: (file: File) => void
  acceptedFormats: string[]
  maxFileSize: number
}

// Optimization progress component
interface OptimizationProgressProps {
  progress: number
  currentStep: string
  insights: string[]
  qualityScores: QualityMetrics | null
}
```

#### LaTeX Editor Components
```typescript
// LaTeX editor with Monaco
interface LaTeXEditorProps {
  initialContent: string
  onChange: (content: string) => void
  theme: 'light' | 'dark'
  readOnly: boolean
}

// PDF viewer with controls
interface PDFViewerProps {
  pdfUrl: string
  zoom: number
  showControls: boolean
  onZoomChange: (zoom: number) => void
}

// Editor layout with resizable panels
interface LaTeXEditorLayoutProps {
  editorProps: LaTeXEditorProps
  pdfViewerProps: PDFViewerProps
  onCompile: (latexContent: string) => Promise<void>
}
```

### Backend API Interfaces

#### Resume Processing API
```python
# Main processing request/response
class ResumeProcessingRequest(BaseModel):
    resume_text: str
    job_description: Optional[str] = None
    target_role: Optional[str] = None
    target_industry: Optional[str] = None

class ResumeOptimizationResponse(BaseModel):
    success: bool
    job_id: str
    results: ProcessingResults
    quality_metrics: QualityMetrics
    download_info: DownloadInfo
    processing_time: float
```

#### LaTeX Compilation API
```python
# LaTeX compilation service
class CompilationRequest(BaseModel):
    latex_content: str
    job_id: Optional[str] = None
    template: Optional[str] = "modern"

class CompilationJob(BaseModel):
    id: str
    status: CompilationStatus
    pdf_data: Optional[bytes] = None
    error: Optional[str] = None
    logs: Optional[str] = None
```

### N8N Workflow Interface

#### Workflow Input/Output Schema
```json
{
  "input": {
    "resume_text": "string",
    "job_description": "string",
    "target_role": "string",
    "target_industry": "string"
  },
  "output": {
    "success": "boolean",
    "job_id": "string",
    "results": {
      "parsed_resume": "object",
      "analysis": "object",
      "optimized_resume": "object",
      "latex_code": "string"
    },
    "quality_metrics": "object",
    "download_info": "object"
  }
}
```

## Data Models

### Core Data Structures

#### Resume Data Model
```typescript
interface ParsedResume {
  contact_info: {
    name: string
    email: string
    phone: string
    address: string
    linkedin: string
  }
  professional_summary: string
  experience: WorkExperience[]
  education: Education[]
  skills: {
    technical: string[]
    soft: string[]
    languages: string[]
    certifications: string[]
  }
  additional_sections: {
    projects: Project[]
    publications: Publication[]
    awards: Award[]
    volunteer: VolunteerWork[]
  }
}

interface WorkExperience {
  company: string
  position: string
  duration: string
  location: string
  achievements: string[]
}
```

#### Analysis and Optimization Models
```typescript
interface ResumeAnalysis {
  strengths: string[]
  weaknesses: string[]
  ats_compatibility_issues: string[]
  keyword_gaps: string[]
  content_recommendations: string[]
  industry_alignment: string
  role_fit_analysis: string
}

interface QualityMetrics {
  overall_score: number
  ats_compatibility: number
  job_match: number
  content_quality: number
  improvement_count: number
}
```

### Database Schema (Future Enhancement)

#### User Sessions Table
```sql
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY,
    session_token VARCHAR(255) UNIQUE,
    created_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP,
    user_data JSONB
);
```

#### Processing Jobs Table
```sql
CREATE TABLE processing_jobs (
    id UUID PRIMARY KEY,
    session_id UUID REFERENCES user_sessions(id),
    status VARCHAR(50),
    input_data JSONB,
    results JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP,
    error_message TEXT
);
```

## Error Handling

### Error Classification and Recovery

#### Client-Side Error Handling
```typescript
enum ErrorType {
  NETWORK_ERROR = 'network_error',
  VALIDATION_ERROR = 'validation_error',
  FILE_PROCESSING_ERROR = 'file_processing_error',
  COMPILATION_ERROR = 'compilation_error'
}

interface ErrorHandler {
  handleError(error: Error, context: string): void
  showUserFriendlyMessage(errorType: ErrorType): void
  attemptRecovery(error: Error): Promise<boolean>
}
```

#### Backend Error Handling Strategy
```python
class ErrorHandlingMiddleware:
    """Centralized error handling for all API endpoints"""

    def handle_ai_processing_error(self, error: Exception) -> ErrorResponse:
        if "rate limit" in str(error).lower():
            return self.create_rate_limit_response(error)
        elif "timeout" in str(error).lower():
            return self.create_timeout_response(error)
        else:
            return self.create_generic_error_response(error)

    def handle_latex_compilation_error(self, error: Exception) -> ErrorResponse:
        return self.create_compilation_error_response(error)
```

#### Circuit Breaker Pattern
```python
class CircuitBreaker:
    """Prevents cascade failures in AI processing"""

    def __init__(self, failure_threshold: int = 3, reset_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.reset_timeout = reset_timeout
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN

    def can_execute(self) -> bool:
        """Check if operation can proceed"""
        return self.state != "OPEN" or self.should_attempt_reset()
```

### Rate Limiting and Quota Management

#### AI API Rate Limiting
```python
class TokenQuotaManager:
    """Manages API quotas for Gemini 2.0 Flash (15 RPM, 1M TPM)"""

    def __init__(self, quota_limit: int = 800000):
        self.quota_limit = quota_limit  # 80% of 1M TPM limit
        self.used_tokens = 0
        self.request_count = 0
        self.max_rpm = 12  # 80% of 15 RPM limit

    def can_proceed(self, estimated_tokens: int) -> bool:
        """Check if request can proceed without exceeding quota"""
        return (self.used_tokens + estimated_tokens) <= self.quota_limit
```

## Testing Strategy

### Testing Pyramid

#### Unit Testing (70% coverage target)
```typescript
// Frontend component testing
describe('OptimizationSection', () => {
  it('should display progress updates correctly', () => {
    // Test progress bar updates
  })

  it('should handle API errors gracefully', () => {
    // Test error handling
  })
})
```

```python
# Backend service testing
class TestResumeProcessing:
    def test_resume_parsing_accuracy(self):
        """Test resume parsing with various formats"""
        pass

    def test_ai_processing_error_handling(self):
        """Test error handling in AI processing pipeline"""
        pass
```

#### Integration Testing (20% coverage target)
```python
class TestEndToEndWorkflow:
    def test_complete_resume_optimization_flow(self):
        """Test entire workflow from upload to PDF generation"""
        pass

    def test_n8n_workflow_integration(self):
        """Test N8N workflow communication"""
        pass
```

#### End-to-End Testing (10% coverage target)
```typescript
// Playwright E2E tests
test('complete resume optimization workflow', async ({ page }) => {
  await page.goto('/');
  await page.setInputFiles('input[type="file"]', 'test-resume.pdf');
  await page.fill('textarea', 'Software Engineer job description...');
  await page.click('button:has-text("Start Optimization")');
  await expect(page.locator('.progress-bar')).toBeVisible();
  await expect(page.locator('.download-button')).toBeVisible({ timeout: 60000 });
});
```

### Performance Testing

#### Load Testing Strategy
```python
# Locust load testing configuration
class ResumeOptimizationUser(HttpUser):
    wait_time = between(1, 3)

    def on_start(self):
        """Setup test data"""
        self.resume_text = load_test_resume()
        self.job_description = load_test_job_description()

    @task(3)
    def optimize_resume(self):
        """Test resume optimization endpoint"""
        response = self.client.post("/api/ai/process-resume", json={
            "resume_text": self.resume_text,
            "job_description": self.job_description
        })
        assert response.status_code == 200
```

#### Performance Benchmarks
- **Resume Processing**: < 60 seconds average completion time
- **LaTeX Compilation**: < 5 seconds for PDF generation
- **File Upload**: < 3 seconds for parsing and validation
- **Concurrent Users**: Support 100+ simultaneous optimizations
- **API Response Time**: < 500ms for non-AI endpoints

### Security Testing

#### Security Measures
```python
# Input validation and sanitization
class SecurityMiddleware:
    def validate_file_upload(self, file: UploadFile) -> bool:
        """Validate uploaded files for security"""
        allowed_types = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain']
        max_size = 10 * 1024 * 1024  # 10MB

        return file.content_type in allowed_types and file.size <= max_size

    def sanitize_latex_content(self, latex: str) -> str:
        """Remove potentially dangerous LaTeX commands"""
        dangerous_commands = ['\\write', '\\input', '\\include', '\\openout']
        for cmd in dangerous_commands:
            latex = latex.replace(cmd, f'% REMOVED: {cmd}')
        return latex
```

## Deployment Architecture

### Production Environment

#### Container Architecture
```dockerfile
# Frontend (Next.js)
FROM node:20-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]

# Backend (FastAPI)
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]

# LaTeX Compiler Service
FROM texlive/texlive:latest
WORKDIR /latex
RUN useradd -m -u 1001 latex
USER latex
CMD ["pdflatex", "--version"]
```

#### Infrastructure as Code (Terraform)
```hcl
# Vercel deployment for frontend
resource "vercel_project" "resume_ai_frontend" {
  name      = "resume-ai-frontend"
  framework = "nextjs"

  environment = [
    {
      key    = "NEXT_PUBLIC_N8N_BASE_URL"
      value  = var.n8n_base_url
      target = ["production"]
    }
  ]
}

# Render.com deployment for N8N workflow
resource "render_service" "n8n_workflow" {
  name        = "resume-ai-n8n"
  type        = "web_service"
  repo        = var.n8n_repo_url
  branch      = "main"

  environment_vars = {
    N8N_BASIC_AUTH_ACTIVE = "true"
    N8N_BASIC_AUTH_USER   = var.n8n_auth_user
    N8N_BASIC_AUTH_PASSWORD = var.n8n_auth_password
  }
}
```

### Monitoring and Observability

#### Application Monitoring
```typescript
// Frontend error tracking
import { captureException, captureMessage } from '@sentry/nextjs';

export const errorHandler = {
  logError: (error: Error, context: string) => {
    console.error(`[${context}]`, error);
    captureException(error, { tags: { context } });
  },

  logInfo: (message: string, data?: any) => {
    console.info(message, data);
    captureMessage(message, 'info');
  }
};
```

```python
# Backend monitoring
import logging
from prometheus_client import Counter, Histogram, generate_latest

# Metrics collection
REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('http_request_duration_seconds', 'HTTP request duration')

@app.middleware("http")
async def monitor_requests(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    duration = time.time() - start_time

    REQUEST_COUNT.labels(method=request.method, endpoint=request.url.path).inc()
    REQUEST_DURATION.observe(duration)

    return response
```

#### Health Checks and Alerts
```python
# Comprehensive health check endpoint
@app.get("/health")
async def health_check():
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "database": await check_database_health(),
            "n8n_workflow": await check_n8n_health(),
            "latex_compiler": await check_latex_health(),
            "ai_apis": await check_ai_apis_health()
        }
    }

    # Determine overall health
    if any(service["status"] != "healthy" for service in health_status["services"].values()):
        health_status["status"] = "degraded"

    return health_status
```

This design document provides a comprehensive technical blueprint for your AI-powered resume optimization platform, capturing the current architecture while providing clear guidance for future development and maintenance.