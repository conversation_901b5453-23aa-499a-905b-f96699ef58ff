"use client";

import { useState, useEffect, useCallback } from "react";
import { Card, CardContent } from "../ui/card";
import { Progress } from "../ui/progress";
import { Badge } from "../ui/badge";
import {
  FileText,
  Search,
  Zap,
  FileType,
  CheckCircle,
  Bot,
  Clock,
  TrendingUp,
  AlertCircle
} from "lucide-react";
import { n8nResumeAPI, ResumeOptimizationResponse } from "@/lib/n8n-api";

interface OptimizationSectionProps {
  uploadedFile: File | null;
  jobDescription: string;
  progress: number;
  onProgressUpdate: (progress: number) => void;
  onComplete: (results?: ResumeOptimizationResponse) => void;
}

const optimizationSteps = [
  {
    id: 'parsing',
    title: 'Advanced Resume Parser',
    description: 'AI-powered extraction and analysis of resume content',
    icon: FileText,
    duration: 12000
  },
  {
    id: 'analysis',
    title: 'Professional Job Analysis',
    description: 'Comprehensive analysis of job requirements and keywords',
    icon: Search,
    duration: 8000
  },
  {
    id: 'optimization',
    title: 'Elite Content Optimizer',
    description: 'Professional optimization with ATS compatibility',
    icon: Zap,
    duration: 15000
  },
  {
    id: 'latex',
    title: 'Professional LaTeX Generator',
    description: 'High-quality document formatting and layout',
    icon: FileType,
    duration: 8000
  },
  {
    id: 'finalization',
    title: 'Quality Assurance & PDF',
    description: 'Final review, scoring, and PDF compilation',
    icon: CheckCircle,
    duration: 6000
  }
];

export default function OptimizationSection({
  uploadedFile,
  jobDescription,
  progress,
  onProgressUpdate,
  onComplete
}: OptimizationSectionProps) {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [stepProgress, setStepProgress] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());
  const [insights, setInsights] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const addInsightForStep = useCallback((stepId: string) => {
    const insightMap: Record<string, string[]> = {
      parsing: [
        "Extracting contact information and personal details",
        "Identifying work experience and job titles",
        "Cataloging skills and technical competencies",
        "Analyzing education background and certifications"
      ],
      analysis: [
        "Analyzing job description for key requirements",
        "Identifying industry-specific keywords",
        "Evaluating ATS compatibility factors",
        "Assessing skill gaps and opportunities"
      ],
      optimization: [
        "Enhancing professional summary with impact metrics",
        "Optimizing experience descriptions with action verbs",
        "Integrating relevant keywords strategically",
        "Improving content flow and readability"
      ],
      latex: [
        "Generating professional LaTeX formatting",
        "Ensuring optimal typography and spacing",
        "Creating ATS-friendly document structure",
        "Optimizing for both digital and print viewing"
      ],
      finalization: [
        "Performing comprehensive quality assurance",
        "Calculating optimization scores",
        "Generating PDF document",
        "Finalizing download package"
      ]
    };

    const stepInsights = insightMap[stepId] || [];
    if (stepInsights.length > 0) {
      const randomInsight = stepInsights[Math.floor(Math.random() * stepInsights.length)];
      setInsights(prev => [...prev.slice(-3), randomInsight]); // Keep last 4 insights
    }
  }, []);

  const readFileContent = useCallback(async (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  }, []);

  const simulateProgress = useCallback(async (duration: number, stepIndex: number) => {
    const startTime = Date.now();
    const interval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const stepProgress = Math.min((elapsed / duration) * 100, 100);

      // Calculate overall progress
      const stepWeight = 100 / optimizationSteps.length;
      const overallProgress = (stepIndex * stepWeight) + (stepProgress * stepWeight / 100);

      onProgressUpdate(Math.min(overallProgress, 100));
      setStepProgress(stepProgress);

      if (stepProgress >= 100) {
        clearInterval(interval);
        setCompletedSteps(prev => new Set([...prev, optimizationSteps[stepIndex].id]));
        addInsightForStep(optimizationSteps[stepIndex].id);
      }
    }, 100);

    return new Promise<void>((resolve) => {
      setTimeout(() => {
        clearInterval(interval);
        resolve();
      }, duration);
    });
  }, [onProgressUpdate, addInsightForStep]);

  const startOptimization = useCallback(async () => {
    if (!uploadedFile) {
      setError("No file uploaded");
      return;
    }

    setIsProcessing(true);
    setError(null);
    setCurrentStepIndex(0);
    setCompletedSteps(new Set());
    setInsights([]);

    try {
      // Read file content
      const fileContent = await readFileContent(uploadedFile);

      // Start step simulations
      for (let i = 0; i < optimizationSteps.length - 1; i++) {
        setCurrentStepIndex(i);
        addInsightForStep(optimizationSteps[i].id);
        await simulateProgress(optimizationSteps[i].duration, i);
      }

      // Final step - actual processing
      setCurrentStepIndex(optimizationSteps.length - 1);
      addInsightForStep(optimizationSteps[optimizationSteps.length - 1].id);

      // Process with n8n
      const response = await n8nResumeAPI.processResumeWithN8N({
        resume_text: fileContent,
        job_description: jobDescription,
        target_role: n8nResumeAPI.extractTargetRole(jobDescription),
        target_industry: n8nResumeAPI.extractTargetIndustry(jobDescription)
      });

      // Complete final step
      await simulateProgress(optimizationSteps[optimizationSteps.length - 1].duration, optimizationSteps.length - 1);

      onProgressUpdate(100);
      setIsProcessing(false);
      onComplete(response);

    } catch (err) {
      console.error('Optimization failed:', err);
      setIsProcessing(false);
      setError(err instanceof Error ? err.message : 'Optimization failed');
    }
  }, [uploadedFile, jobDescription, readFileContent, simulateProgress, addInsightForStep, onProgressUpdate, onComplete]);

  // Start processing when component mounts
  useEffect(() => {
    if (!isProcessing && !error && uploadedFile) {
      startOptimization();
    }
  }, [uploadedFile, startOptimization, isProcessing, error]);

  const getStepStatus = (index: number) => {
    if (completedSteps.has(optimizationSteps[index].id)) return 'completed';
    if (index === currentStepIndex) return 'active';
    if (index < currentStepIndex) return 'completed';
    return 'upcoming';
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3 text-red-700">
              <AlertCircle className="h-5 w-5" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Progress Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-3">
          <Bot className="h-8 w-8 text-blue-600" />
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              AI Resume Optimization in Progress
            </h2>
            <p className="text-gray-600">
              Professional-grade enhancement using advanced AI agents
            </p>
          </div>
        </div>

        <div className="max-w-md mx-auto">
          <Progress value={progress} className="h-3" />
          <p className="text-sm text-gray-500 mt-2">
            {Math.round(progress)}% complete
          </p>
        </div>
      </div>

      {/* Steps Progress */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            {optimizationSteps.map((step, index) => {
              const Icon = step.icon;
              const status = getStepStatus(index);
              const isActive = status === 'active';
              const isCompleted = status === 'completed';

              return (
                <div
                  key={step.id}
                  className={`flex items-center space-x-4 p-4 rounded-lg transition-colors ${
                    isActive ? 'bg-blue-50 border border-blue-200' :
                    isCompleted ? 'bg-green-50 border border-green-200' :
                    'bg-gray-50 border border-gray-200'
                  }`}
                >
                  <div className={`p-2 rounded-full ${
                    isCompleted ? 'bg-green-100 text-green-600' :
                    isActive ? 'bg-blue-100 text-blue-600' :
                    'bg-gray-100 text-gray-400'
                  }`}>
                    {isCompleted ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className={`font-semibold ${
                        isActive ? 'text-blue-900' :
                        isCompleted ? 'text-green-900' :
                        'text-gray-500'
                      }`}>
                        {step.title}
                      </h3>

                      {isCompleted && (
                        <Badge variant="secondary" className="bg-green-100 text-green-700">
                          Complete
                        </Badge>
                      )}

                      {isActive && (
                        <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                          Processing...
                        </Badge>
                      )}
                    </div>

                    <p className={`text-sm ${
                      isActive ? 'text-blue-700' :
                      isCompleted ? 'text-green-700' :
                      'text-gray-500'
                    }`}>
                      {step.description}
                    </p>
                  </div>

                  {isActive && (
                    <div className="text-right">
                      <Progress
                        value={stepProgress}
                        className="w-20 h-2"
                      />
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Real-time Insights */}
      {insights.length > 0 && (
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3 mb-4">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <h3 className="font-semibold text-blue-900">Real-time Processing Insights</h3>
            </div>

            <div className="space-y-2">
              {insights.slice(-4).map((insight, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-2 text-sm text-blue-700 animate-in slide-in-from-left duration-500"
                >
                  <div className="h-1.5 w-1.5 bg-blue-400 rounded-full" />
                  <span>{insight}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Processing Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-4 text-center">
            <Clock className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">
              {Math.floor((Date.now() - performance.now()) / 1000)}s
            </div>
            <p className="text-sm text-gray-500">Processing Time</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <Bot className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">
              {completedSteps.size}/{optimizationSteps.length}
            </div>
            <p className="text-sm text-gray-500">Steps Complete</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <TrendingUp className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">
              {insights.length}
            </div>
            <p className="text-sm text-gray-500">Optimizations Applied</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
