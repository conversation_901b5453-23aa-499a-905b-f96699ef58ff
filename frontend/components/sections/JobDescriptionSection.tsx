"use client";

import { useState } from "react";
import { <PERSON>, CardContent } from "../ui/card";
import { But<PERSON> } from "../ui/button";
import { Textarea } from "../ui/textarea";
import { Label } from "../ui/label";
import { Badge } from "../ui/badge";
import { Alert } from "../ui/alert";
import { FileText, Sparkles, Globe, AlertCircle, ArrowLeft } from "lucide-react";

interface JobDescriptionSectionProps {
  jobDescription: string;
  onJobDescriptionChange: (description: string) => void;
  onComplete: () => void;
}

export default function JobDescriptionSection({
  jobDescription,
  onJobDescriptionChange,
  onComplete
}: JobDescriptionSectionProps) {
  const [inputMethod, setInputMethod] = useState<'paste' | 'url'>('paste');
  const [url, setUrl] = useState('');
  const [isExtracting, setIsExtracting] = useState(false);
  const [extractError, setExtractError] = useState<string | null>(null);

  const handleUrlExtraction = async () => {
    if (!url.trim()) return;

    setIsExtracting(true);
    setExtractError(null);

    try {
      // Simulate URL extraction
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mock extracted content
      const mockJobDescription = `Senior Software Engineer - Full Stack Development

We are seeking a highly skilled Senior Software Engineer to join our dynamic team. The ideal candidate will have extensive experience in full-stack development, with a strong background in modern web technologies.

Key Responsibilities:
• Design and develop scalable web applications using React, Node.js, and TypeScript
• Collaborate with cross-functional teams to deliver high-quality software solutions
• Mentor junior developers and contribute to technical decision-making
• Implement best practices for code quality, testing, and deployment

Required Qualifications:
• Bachelor's degree in Computer Science or related field
• 5+ years of experience in full-stack development
• Proficiency in JavaScript, TypeScript, React, and Node.js
• Experience with cloud platforms (AWS, Azure, or GCP)
• Strong understanding of database design and optimization
• Excellent problem-solving and communication skills

Preferred Qualifications:
• Experience with microservices architecture
• Knowledge of DevOps practices and CI/CD pipelines
• Previous experience in Agile/Scrum methodologies
• Contributions to open-source projects

We offer competitive compensation, comprehensive benefits, and opportunities for professional growth in a collaborative environment.`;

      onJobDescriptionChange(mockJobDescription);
      setIsExtracting(false);
    } catch {
      setIsExtracting(false);
      setExtractError("Failed to extract job description from URL. Please try again or paste the content manually.");
    }
  };

  const handlePasteDescription = (value: string) => {
    onJobDescriptionChange(value);
    setExtractError(null);
  };

  const handleContinue = () => {
    if (jobDescription.trim().length < 50) {
      setExtractError("Please provide a more detailed job description (at least 50 characters).");
      return;
    }
    onComplete();
  };

  const wordCount = jobDescription.trim().split(/\s+/).length;
  const charCount = jobDescription.length;

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          Add Job Description
        </h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Provide the job description to help our AI optimize your resume for this specific role.
        </p>
      </div>

      {/* Input Method Selection */}
      <Card>
        <CardContent className="p-6">
          <div className="mb-4">
            <Label className="text-base font-semibold text-gray-900">
              How would you like to add the job description?
            </Label>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <Card
              className={`cursor-pointer transition-all ${
                inputMethod === 'paste'
                  ? 'ring-2 ring-blue-500 bg-blue-50 border-blue-200'
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => setInputMethod('paste')}
            >
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${
                    inputMethod === 'paste' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-600'
                  }`}>
                    <FileText className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Paste Text</h3>
                    <p className="text-sm text-gray-500">Copy and paste the job description</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card
              className={`cursor-pointer transition-all ${
                inputMethod === 'url'
                  ? 'ring-2 ring-blue-500 bg-blue-50 border-blue-200'
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => setInputMethod('url')}
            >
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${
                    inputMethod === 'url' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-600'
                  }`}>
                    <Globe className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Extract from URL</h3>
                    <p className="text-sm text-gray-500">Provide a link to the job posting</p>
                    <Badge variant="secondary" className="mt-1 text-xs">
                      Coming Soon
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* URL Input Method */}
          {inputMethod === 'url' && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="job-url" className="text-sm font-medium text-gray-700 mb-2 block">
                  Job Posting URL
                </Label>
                <div className="flex space-x-2">
                  <input
                    id="job-url"
                    type="url"
                    value={url}
                    onChange={(e) => setUrl(e.target.value)}
                    placeholder="https://company.com/jobs/senior-developer"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    disabled={isExtracting}
                  />
                  <Button
                    onClick={handleUrlExtraction}
                    disabled={!url.trim() || isExtracting}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {isExtracting ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    ) : (
                      'Extract'
                    )}
                  </Button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  We&apos;ll automatically extract the job description from the URL
                </p>
              </div>
            </div>
          )}

          {/* Text Input Method */}
          {inputMethod === 'paste' && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="job-description" className="text-sm font-medium text-gray-700 mb-2 block">
                  Job Description
                </Label>
                <Textarea
                  id="job-description"
                  value={jobDescription}
                  onChange={(e) => handlePasteDescription(e.target.value)}
                  placeholder="Paste the complete job description here. Include responsibilities, requirements, qualifications, and any other relevant details..."
                  className="min-h-[300px] resize-none"
                />
                <div className="flex justify-between items-center mt-2">
                  <p className="text-xs text-gray-500">
                    {charCount} characters • {wordCount} words
                  </p>
                  {charCount > 0 && (
                    <Badge
                      variant={charCount >= 50 ? "default" : "secondary"}
                      className={charCount >= 50 ? "bg-green-100 text-green-800" : ""}
                    >
                      {charCount >= 50 ? "Good length" : "Add more details"}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Error Display */}
          {extractError && (
            <Alert className="border-red-200 bg-red-50 mt-4">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <div className="text-red-800">
                <strong>Error:</strong> {extractError}
              </div>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Sample Job Description */}
      {!jobDescription && inputMethod === 'paste' && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-6">
            <h3 className="font-semibold text-blue-900 mb-3">💡 What to include:</h3>
            <ul className="space-y-2 text-sm text-blue-800">
              <li>• Job title and company information</li>
              <li>• Key responsibilities and daily tasks</li>
              <li>• Required skills and qualifications</li>
              <li>• Preferred experience and education</li>
              <li>• Company culture and values</li>
              <li>• Technical requirements and tools</li>
            </ul>
            <div className="mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePasteDescription(`Senior Software Engineer - Full Stack Development

We are seeking a highly skilled Senior Software Engineer to join our dynamic team. The ideal candidate will have extensive experience in full-stack development, with a strong background in modern web technologies.

Key Responsibilities:
• Design and develop scalable web applications using React, Node.js, and TypeScript
• Collaborate with cross-functional teams to deliver high-quality software solutions
• Mentor junior developers and contribute to technical decision-making
• Implement best practices for code quality, testing, and deployment

Required Qualifications:
• Bachelor's degree in Computer Science or related field
• 5+ years of experience in full-stack development
• Proficiency in JavaScript, TypeScript, React, and Node.js
• Experience with cloud platforms (AWS, Azure, or GCP)
• Strong understanding of database design and optimization
• Excellent problem-solving and communication skills

Preferred Qualifications:
• Experience with microservices architecture
• Knowledge of DevOps practices and CI/CD pipelines
• Previous experience in Agile/Scrum methodologies
• Contributions to open-source projects

We offer competitive compensation, comprehensive benefits, and opportunities for professional growth in a collaborative environment.`)}
                className="text-blue-700 border-blue-300 hover:bg-blue-100"
              >
                Use Sample Job Description
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Navigation */}
      <div className="flex justify-between items-center">
        <Button variant="outline" className="flex items-center">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Upload
        </Button>

        <Button
          onClick={handleContinue}
          disabled={!jobDescription.trim() || jobDescription.trim().length < 50}
          className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg"
          size="lg"
        >
          Start AI Optimization
          <Sparkles className="ml-2 h-5 w-5" />
        </Button>
      </div>
    </div>
  );
}
