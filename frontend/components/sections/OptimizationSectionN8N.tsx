"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent } from "../ui/card";
import { Progress } from "../ui/progress";
import { Badge } from "../ui/badge";
import {
  FileText,
  Search,
  Zap,
  FileType,
  CheckCircle,
  Bot,
  Clock,
  AlertCircle,
  Award,
  RefreshCw,
  Edit
} from "lucide-react";
import { Button } from "../ui/button";
import { ResumeOptimizationResponse } from "../../lib/n8n-api";
import { extractTextFromFile } from "../../lib/pdf-extractor";
import { useResumeOptimization } from "../../lib/hooks/use-resume-api";
import { useResumeStore } from "../../lib/stores/resume-store";

interface OptimizationSectionProps {
  uploadedFile: File | null;
  jobDescription: string;
  progress: number;
  onProgressUpdate: (progress: number) => void;
  onComplete: (results?: ResumeOptimizationResponse) => void;
}

const optimizationSteps = [
  {
    id: 'parsing',
    title: 'Advanced Resume Parser',
    description: 'AI-powered extraction and analysis of resume content',
    icon: FileText,
    duration: 12000
  },
  {
    id: 'analysis',
    title: 'Intelligent Analysis',
    description: 'Deep analysis of skills, experience, and job alignment',
    icon: Search,
    duration: 10000
  },
  {
    id: 'optimization',
    title: 'AI Optimization Engine',
    description: 'Strategic enhancement of content and structure',
    icon: Zap,
    duration: 15000
  },
  {
    id: 'formatting',
    title: 'Professional Formatting',
    description: 'LaTeX-powered professional document generation',
    icon: FileType,
    duration: 8000
  },
  {
    id: 'validation',
    title: 'Quality Validation',
    description: 'ATS compatibility and quality assurance checks',
    icon: CheckCircle,
    duration: 5000
  }
];

export default function OptimizationSection({
  uploadedFile,
  jobDescription,
  progress,
  onProgressUpdate,
  onComplete
}: OptimizationSectionProps) {
  const router = useRouter();

  // Use our new hooks and store
  const { optimizeResume, isOptimizing, optimizationError, isSuccess } = useResumeOptimization();
  const {
    setUploadedFile,
    setJobDescription,
    canEditLatex,
  } = useResumeStore();

  // Local UI state for progress simulation
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [stepProgress, setStepProgress] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());
  const [insights, setInsights] = useState<string[]>([]);
  const [qualityScores, setQualityScores] = useState<Record<string, number> | null>(null);
  const [hasStarted, setHasStarted] = useState(false);

  // Prevent duplicate executions using a ref (backup to React Query deduplication)
  const executionRef = useRef<boolean>(false);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Progress simulation function
  const simulateStepProgression = useCallback(() => {
    const totalSteps = optimizationSteps.length;
    const stepDuration = 8000; // 8 seconds per step
    const updateInterval = 200; // Update every 200ms
    const updatesPerStep = stepDuration / updateInterval;

    let currentStep = 0;
    let updateCount = 0;

    const progressInterval = setInterval(() => {
      updateCount++;

      // Calculate progress within current step
      const stepProgressPercent = (updateCount % updatesPerStep) / updatesPerStep * 100;
      setStepProgress(stepProgressPercent);

      // Calculate overall progress
      const overallProgress = (currentStep / totalSteps * 100) + (stepProgressPercent / totalSteps);
      onProgressUpdate(Math.min(overallProgress, 95)); // Cap at 95% until completion

      // Move to next step
      if (updateCount % updatesPerStep === 0) {
        const currentStepData = optimizationSteps[currentStep];
        if (currentStepData) {
          setCompletedSteps(prev => new Set([...prev, currentStepData.id]));
        }
        currentStep++;

        if (currentStep >= optimizationSteps.length) {
          clearInterval(progressInterval);
          return;
        }

        setCurrentStepIndex(currentStep);
      }
    }, updateInterval);

    return progressInterval;
  }, [onProgressUpdate]);

  // Add insight messages based on step
  const addInsightForStep = useCallback((stepId: string) => {
    const stepInsights: Record<string, string[]> = {
      parsing: ['📄 Extracting resume content and structure', '🔍 Analyzing document formatting'],
      analysis: ['🎯 Identifying key skills and achievements', '📊 Evaluating job match potential'],
      optimization: ['✨ Enhancing content with AI recommendations', '🔧 Optimizing for ATS compatibility'],
      formatting: ['🎨 Applying professional LaTeX formatting', '📋 Generating structured document'],
      validation: ['✅ Running quality assurance checks', '🏆 Calculating optimization scores']
    };

    const insights = stepInsights[stepId] || [];
    insights.forEach(insight => {
      setInsights(prev => [...prev, insight]);
    });
  }, []);

  // Retry function - reset state and trigger re-optimization
  const handleRetry = useCallback(() => {
    executionRef.current = false;
    setHasStarted(false);
    setInsights([]);
    setQualityScores(null);
    setCompletedSteps(new Set());
    setCurrentStepIndex(0);
    setStepProgress(0);
    onProgressUpdate(0);
  }, [onProgressUpdate]);

  // Open LaTeX Editor with optimization results from store
  const handleOpenLaTeXEditor = useCallback(() => {
    if (canEditLatex()) {
      // Navigate to LaTeX editor (it will load from store)
      router.push('/latex-editor-from-optimization');
    }
  }, [canEditLatex, router]);

  // Simple target role extraction
  const extractTargetRole = useCallback((jobDescription: string): string => {
    if (!jobDescription) return '';
    const roleKeywords = [
      'software engineer', 'developer', 'architect', 'manager', 'director',
      'analyst', 'consultant', 'specialist', 'coordinator', 'lead',
      'senior', 'principal', 'staff', 'head of', 'vice president'
    ];
    const description = jobDescription.toLowerCase();
    for (const role of roleKeywords) {
      if (description.includes(role)) {
        return role.split(' ').map(word =>
          word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
      }
    }
    return '';
  }, []);

  // Simple target industry extraction
  const extractTargetIndustry = useCallback((jobDescription: string): string => {
    if (!jobDescription) return '';
    const industryKeywords = {
      'Technology': ['software', 'tech', 'it', 'saas', 'cloud', 'ai', 'ml'],
      'Healthcare': ['healthcare', 'medical', 'hospital', 'pharma', 'biotech'],
      'Finance': ['finance', 'banking', 'fintech', 'investment', 'insurance'],
      'Consulting': ['consulting', 'advisory', 'strategy', 'management consulting'],
      'Education': ['education', 'university', 'school', 'academic', 'learning'],
    };
    const description = jobDescription.toLowerCase();
    for (const [industry, keywords] of Object.entries(industryKeywords)) {
      if (keywords.some(keyword => description.includes(keyword))) {
        return industry;
      }
    }
    return '';
  }, []);

  // Enhanced optimization function using React Query
  const startOptimization = useCallback(async () => {
    // Prevent duplicate executions with multiple guards
    if (!uploadedFile || hasStarted || isOptimizing || executionRef.current) {
      console.log('[Optimization] Skipping - already started or in progress');
      return;
    }

    // Set execution guard
    executionRef.current = true;
    setHasStarted(true);

    console.log('[Optimization] Starting new optimization process');

    try {
      // Update store with current file and job description
      if (uploadedFile) {
        setUploadedFile({
          name: uploadedFile.name,
          size: uploadedFile.size,
          type: uploadedFile.type,
          lastModified: uploadedFile.lastModified,
        });
      }
      setJobDescription(jobDescription);

      // Start progress simulation
      progressIntervalRef.current = simulateStepProgression();

      // Add initial insights
      setInsights(['🚀 Starting AI-powered resume optimization...']);

      // Extract text content
      console.log('[Optimization] Extracting text from file:', uploadedFile.name);
      const fileContent = await extractTextFromFile(uploadedFile);
      console.log('[Optimization] Text extraction completed. Length:', fileContent.length);

      // Extract target role and industry
      const targetRole = extractTargetRole(jobDescription);
      const targetIndustry = extractTargetIndustry(jobDescription);

      console.log('[Optimization] Target role:', targetRole);
      console.log('[Optimization] Target industry:', targetIndustry);

      // Add insights about what we're optimizing for
      if (targetRole) {
        setInsights(prev => [...prev, `🎯 Optimizing for ${targetRole} role`]);
      }
      if (targetIndustry) {
        setInsights(prev => [...prev, `🏢 Focusing on ${targetIndustry} industry`]);
      }

      // Add step-based insights
      optimizationSteps.forEach((step, index) => {
        setTimeout(() => {
          addInsightForStep(step.id);
        }, index * 2000);
      });

      // Call optimization using React Query (automatic deduplication)
      const response = await optimizeResume({
        resume_text: fileContent,
        job_description: jobDescription,
        target_role: targetRole,
        target_industry: targetIndustry,
      });

      // Stop progress simulation
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }

      if (response) {
        // Complete UI state
        setCompletedSteps(new Set(optimizationSteps.map(step => step.id)));
        setCurrentStepIndex(optimizationSteps.length - 1);
        setStepProgress(100);
        onProgressUpdate(100);

        // Set quality scores for UI display
        setQualityScores({
          overall: response.quality_metrics.overall_score,
          ats: response.quality_metrics.ats_compatibility,
          job_match: response.quality_metrics.job_match,
          content: response.quality_metrics.content_quality,
        });

        // Add success insights
        setInsights(prev => [
          ...prev,
          `✅ Optimization completed successfully!`,
          `📊 Overall score: ${response.quality_metrics.overall_score}/100`,
          `🎯 ATS compatibility: ${response.quality_metrics.ats_compatibility}/100`,
          `🔍 Job match score: ${response.quality_metrics.job_match}/100`,
          `📝 ${response.quality_metrics.improvement_count} improvements made`,
          `✨ Professional PDF generated and ready for download`
        ]);

        // Brief delay to show completion state
        setTimeout(() => {
          console.log('[Optimization] Calling onComplete with response:', response);
          console.log('[Optimization] Response keys:', Object.keys(response));
          console.log('[Optimization] Results keys:', Object.keys(response.results || {}));
          console.log('[Optimization] Optimized resume keys:', Object.keys(response.results?.optimized_resume || {}));
          onComplete(response);
        }, 1000);
      }

    } catch (error) {
      console.error('[Optimization] Error:', error);

      // Stop progress simulation on error
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }

      // Error handling is managed by the React Query hook
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setInsights(prev => [...prev, `❌ Error: ${errorMessage}`]);

      onComplete();
    }
  }, [
    uploadedFile,
    jobDescription,
    hasStarted,
    isOptimizing,
    optimizeResume,
    simulateStepProgression,
    setUploadedFile,
    setJobDescription,
    extractTargetRole,
    extractTargetIndustry,
    addInsightForStep,
    onProgressUpdate,
    onComplete
  ]);

  // Start optimization when component mounts (only once)
  useEffect(() => {
    if (!hasStarted && uploadedFile && !executionRef.current) {
      startOptimization();
    }
  }, [uploadedFile, hasStarted, startOptimization]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, []);

  return (
    <div className="space-y-6">
      {/* Error Display */}
      {optimizationError && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="flex items-start space-x-3">
              <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <h3 className="text-sm font-medium text-red-800 mb-2">
                  Optimization Failed
                </h3>
                <div className="text-sm text-red-700">
                  <p className="mb-2">{optimizationError.message}</p>
                  <p className="text-xs text-red-600">
                    This may be due to network connectivity or the N8N service being unavailable.
                  </p>
                </div>
              </div>
              <Button
                onClick={handleRetry}
                variant="outline"
                size="sm"
                className="border-red-300 hover:bg-red-100"
                disabled={isOptimizing}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Overall Progress */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Overall Progress</h3>
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              {Math.round(progress)}% Complete
            </Badge>
          </div>
          <Progress value={progress} className="h-3 mb-4" />
          <div className="text-sm text-gray-600">
            {isOptimizing ? 'AI agents are analyzing and optimizing your resume...' :
             isSuccess ? 'Optimization completed successfully!' :
             'Ready to start optimization'}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Steps */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Optimization Steps</h3>
          <div className="space-y-4">
            {optimizationSteps.map((step, index) => {
              const isCompleted = completedSteps.has(step.id);
              const isCurrent = currentStepIndex === index;
              const isUpcoming = index > currentStepIndex;

              return (
                <div key={step.id} className="flex items-center space-x-4">
                  <div className={`p-2 rounded-lg ${
                    isCompleted
                      ? "bg-green-100 text-green-600"
                      : isCurrent
                      ? "bg-blue-100 text-blue-600"
                      : "bg-gray-100 text-gray-400"
                  }`}>
                    {isCompleted ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <step.icon className="h-5 w-5" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className={`font-medium ${
                      isCompleted ? "text-green-800" :
                      isCurrent ? "text-blue-800" : "text-gray-500"
                    }`}>
                      {step.title}
                    </h4>
                    <p className="text-sm text-gray-600">{step.description}</p>
                    {isCurrent && (
                      <div className="mt-2">
                        <Progress value={stepProgress} className="h-1" />
                      </div>
                    )}
                  </div>
                  <div className="text-right">
                    {isCompleted && (
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        ✓ Done
                      </Badge>
                    )}
                    {isCurrent && (
                      <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                        <Clock className="h-3 w-3 mr-1" />
                        Processing
                      </Badge>
                    )}
                    {isUpcoming && (
                      <Badge variant="secondary" className="bg-gray-100 text-gray-600">
                        Pending
                      </Badge>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Live Insights */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Bot className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">AI Insights</h3>
          </div>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {insights.map((insight, index) => (
              <div key={index} className="text-sm text-gray-700 p-2 bg-gray-50 rounded">
                {insight}
              </div>
            ))}
            {insights.length === 0 && (
              <p className="text-sm text-gray-500">AI insights will appear here as processing begins...</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Quality Scores */}
      {qualityScores && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 mb-4">
              <Award className="h-5 w-5 text-yellow-600" />
              <h3 className="text-lg font-semibold text-gray-900">Quality Metrics</h3>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-3 bg-gradient-to-br from-green-50 to-green-100 rounded-lg">
                <div className="text-2xl font-bold text-green-700">{qualityScores.overall}</div>
                <div className="text-sm text-green-600">Overall Score</div>
              </div>
              <div className="text-center p-3 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg">
                <div className="text-2xl font-bold text-blue-700">{qualityScores.ats}</div>
                <div className="text-sm text-blue-600">ATS Compatible</div>
              </div>
              <div className="text-center p-3 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg">
                <div className="text-2xl font-bold text-purple-700">{qualityScores.job_match}</div>
                <div className="text-sm text-purple-600">Job Match</div>
              </div>
              <div className="text-center p-3 bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg">
                <div className="text-2xl font-bold text-orange-700">{qualityScores.content}</div>
                <div className="text-sm text-orange-600">Content Quality</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* LaTeX Editor Button */}
      {isSuccess && canEditLatex() && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Edit LaTeX Source</h3>
                <p className="text-sm text-gray-600">
                  Fine-tune your optimized resume using our advanced LaTeX editor with real-time preview.
                </p>
              </div>
              <Button
                onClick={handleOpenLaTeXEditor}
                className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white"
              >
                <Edit className="h-4 w-4 mr-2" />
                Open LaTeX Editor
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
