"use client";

import { useState } from "react";
import { use<PERSON>outer } from "next/navigation";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "../ui/card";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";
import { Alert } from "../ui/alert";
import {
  Download,
  Eye,
  FileText,
  TrendingUp,
  CheckCircle,
  Star,
  ArrowRight,
  RefreshCw,
  Share2,
  Edit
} from "lucide-react";
import { ResumeOptimizationResponse } from "../../lib/n8n-api";

interface ResultsSectionProps {
  uploadedFile: File | null;
  processingResults: ResumeOptimizationResponse | null;
}

export default function ResultsSection({
  uploadedFile,
  processingResults
}: ResultsSectionProps) {
  console.log('[ResultsSection] Received processingResults:', processingResults);
  console.log('[ResultsSection] Processing results type:', typeof processingResults);
  console.log('[ResultsSection] Processing results keys:', processingResults ? Object.keys(processingResults) : 'null');

  const [activeTab, setActiveTab] = useState<'preview' | 'comparison' | 'changes'>('preview');
  const [isDownloading, setIsDownloading] = useState(false);
  const router = useRouter();

  const fileName = uploadedFile?.name || "resume";

  // Extract data from processing results
  const optimizedResume = processingResults?.results?.optimized_resume;
  const qualityMetrics = processingResults?.quality_metrics;
  const contactInfo = optimizedResume?.contact_info;
  const professionalSummary = optimizedResume?.professional_summary;
  const experience = optimizedResume?.experience;
  const skills = optimizedResume?.skills;

  // LaTeX editor navigation function
  const handleOpenLaTeXEditor = () => {
    if (processingResults?.results?.latex_code) {
      const encodedLatex = encodeURIComponent(processingResults.results.latex_code);
      router.push(`/latex-editor-from-optimization?latex=${encodedLatex}`);
    }
  };

  // Use real metrics if available, otherwise fallback
  const metrics = qualityMetrics ? {
    overallScore: qualityMetrics.overall_score,
    atsScore: qualityMetrics.ats_compatibility,
    jobMatchScore: qualityMetrics.job_match,
    contentScore: qualityMetrics.content_quality,
    keywordMatch: 85, // Default if not in response
    readabilityScore: 89 // Default if not in response
  } : {
    overallScore: 90,
    atsScore: 94,
    jobMatchScore: 87,
    contentScore: 89,
    keywordMatch: 85,
    readabilityScore: 89
  };

  const handleDownload = async (format: 'pdf' | 'latex') => {
    setIsDownloading(true);

    try {
      if (processingResults?.download_info?.download_url && format === 'pdf') {
        // Use the real download URL from the API
        window.open(processingResults.download_info.download_url, '_blank');
      } else if (processingResults?.results?.latex_code && format === 'latex') {
        // Download LaTeX code as file
        const latexContent = processingResults.results.latex_code;
        const blob = new Blob([latexContent], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `optimized-${fileName.replace(/\.[^/.]+$/, "")}.tex`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      } else {
        // Fallback for missing data
        console.warn(`No ${format} download available in processing results`);
        const blob = new Blob(['Download not available'], { type: format === 'pdf' ? 'application/pdf' : 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `optimized-${fileName.replace(/\.[^/.]+$/, "")}.${format}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Download error:', error);
    } finally {
      setIsDownloading(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600 bg-green-100";
    if (score >= 75) return "text-yellow-600 bg-yellow-100";
    return "text-red-600 bg-red-100";
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="flex items-center justify-center space-x-2 mb-4">
          <CheckCircle className="h-8 w-8 text-green-600" />
          <h2 className="text-3xl font-bold text-gray-900">
            Optimization Complete!
          </h2>
        </div>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Your resume has been successfully optimized with AI-powered improvements.
          Review the changes and download your professional PDF.
        </p>
      </div>

      {/* Success Alert */}
      <Alert className="border-green-200 bg-green-50">
        <TrendingUp className="h-4 w-4 text-green-600" />
        <div className="text-green-800">
          <strong>Great news!</strong> Your resume score improved to {metrics.overallScore}% -
          a significant boost that will help you stand out to recruiters and ATS systems.
          {processingResults?.processing_time && (
            <> Processing completed in {processingResults.processing_time}.</>
          )}
        </div>
      </Alert>

      {/* Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {[
          { label: "Overall Score", value: metrics.overallScore, icon: Star },
          { label: "ATS Score", value: metrics.atsScore, icon: CheckCircle },
          { label: "Job Match", value: metrics.jobMatchScore, icon: TrendingUp },
          { label: "Content Quality", value: metrics.contentScore, icon: FileText },
          { label: "Readability", value: metrics.readabilityScore, icon: Eye }
        ].map((metric) => (
          <Card key={metric.label}>
            <CardContent className="p-4 text-center">
              <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full mb-2 ${getScoreColor(metric.value)}`}>
                <metric.icon className="h-6 w-6" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{metric.value}%</div>
              <div className="text-sm text-gray-600">{metric.label}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Tabs */}
      <Card>
        <CardHeader>
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
            {[
              { id: 'preview', label: 'Preview', icon: Eye },
              { id: 'comparison', label: 'Before/After', icon: RefreshCw },
              { id: 'changes', label: 'Changes Made', icon: TrendingUp }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as typeof activeTab)}
                className={`flex-1 flex items-center justify-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                  activeTab === tab.id
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </CardHeader>
        <CardContent className="p-6">
          {activeTab === 'preview' && (
            <div className="space-y-4">
              <div className="bg-white border-2 border-gray-200 rounded-lg p-8 min-h-[600px]">
                <div className="space-y-6">
                  <div className="text-center border-b pb-4">
                    <h1 className="text-2xl font-bold text-gray-900">
                      {contactInfo?.name || 'Your Name'}
                    </h1>
                    <p className="text-gray-600">
                      {experience?.[0]?.position || 'Professional Title'}
                    </p>
                    <p className="text-sm text-gray-500">
                      {contactInfo?.email || '<EMAIL>'} •
                      {contactInfo?.phone ? ` ${contactInfo.phone} • ` : ' (phone) • '}
                      {contactInfo?.linkedin || 'LinkedIn Profile'}
                    </p>
                    {contactInfo?.address && (
                      <p className="text-sm text-gray-500">{contactInfo.address}</p>
                    )}
                  </div>

                  {professionalSummary && (
                    <div>
                      <h2 className="text-lg font-bold text-gray-900 mb-2">PROFESSIONAL SUMMARY</h2>
                      <p className="text-gray-700 text-sm leading-relaxed">
                        {professionalSummary}
                      </p>
                    </div>
                  )}

                  {skills && (
                    <div>
                      <h2 className="text-lg font-bold text-gray-900 mb-2">TECHNICAL SKILLS</h2>
                      <div className="text-sm text-gray-700">
                        {skills.technical && Array.isArray(skills.technical) && (
                          <p><strong>Technical:</strong> {skills.technical.join(', ')}</p>
                        )}
                        {skills.soft && Array.isArray(skills.soft) && (
                          <p><strong>Soft Skills:</strong> {skills.soft.join(', ')}</p>
                        )}
                        {skills.languages && Array.isArray(skills.languages) && (
                          <p><strong>Languages:</strong> {skills.languages.join(', ')}</p>
                        )}
                      </div>
                    </div>
                  )}

                  {experience && Array.isArray(experience) && (
                    <div>
                      <h2 className="text-lg font-bold text-gray-900 mb-2">PROFESSIONAL EXPERIENCE</h2>
                      <div className="space-y-4">
                        {experience.slice(0, 2).map((job, index) => (
                          <div key={index}>
                            <h3 className="font-semibold text-gray-900">{job.position || 'Job Title'}</h3>
                            <p className="text-sm text-gray-600">
                              {job.company || 'Company Name'} • {job.duration || 'Date Range'}
                            </p>
                            {job.location && (
                              <p className="text-sm text-gray-600">{job.location}</p>
                            )}
                            {job.achievements && Array.isArray(job.achievements) && (
                              <ul className="text-sm text-gray-700 mt-2 space-y-1 list-disc list-inside">
                                {job.achievements.slice(0, 4).map((achievement, achIndex) => (
                                  <li key={achIndex}>{achievement}</li>
                                ))}
                              </ul>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Fallback if no data available */}
                  {(!contactInfo?.name && !professionalSummary && !experience) && (
                    <div className="text-center py-8">
                      <p className="text-gray-500">Resume preview will appear here once processing is complete.</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'comparison' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                  <Badge variant="secondary" className="mr-2">Before</Badge>
                  Original Resume
                </h3>
                <div className="bg-gray-50 border rounded-lg p-4 h-96 overflow-y-auto">
                  <div className="text-sm text-gray-600 space-y-2">
                    {processingResults?.results?.parsed_resume ? (
                      <>
                        <div className="font-medium text-gray-900 mb-2">
                          {processingResults.results.parsed_resume.contact_info?.name || 'Name not available'}
                        </div>
                        <p><strong>Summary:</strong> {processingResults.results.parsed_resume.summary || 'Original summary not available'}</p>
                        <p><strong>Skills:</strong> {
                          Array.isArray(processingResults.results.parsed_resume.skills?.technical)
                            ? processingResults.results.parsed_resume.skills.technical.slice(0, 5).join(', ')
                            : 'Technical skills not available'
                        }</p>
                        <p><strong>Experience:</strong> {
                          processingResults.results.parsed_resume.experience?.[0]?.achievements?.[0] || 'Experience details not available'
                        }</p>
                      </>
                    ) : (
                      <p className="text-gray-500 text-center py-8">Original resume data not available</p>
                    )}
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                  <Badge className="mr-2 bg-green-100 text-green-800">After</Badge>
                  Optimized Resume
                </h3>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 h-96 overflow-y-auto">
                  <div className="text-sm text-gray-700 space-y-2">
                    {optimizedResume ? (
                      <>
                        <div className="font-medium text-gray-900 mb-2">
                          {contactInfo?.name || 'Name not available'}
                        </div>
                        <p><strong>Summary:</strong> {professionalSummary || 'Optimized summary not available'}</p>
                        <p><strong>Skills:</strong> {
                          Array.isArray(skills?.technical)
                            ? skills.technical.slice(0, 5).join(', ')
                            : 'Technical skills not available'
                        }</p>
                        <p><strong>Experience:</strong> {
                          experience?.[0]?.achievements?.[0] || 'Experience details not available'
                        }</p>
                      </>
                    ) : (
                      <p className="text-gray-500 text-center py-8">Optimized resume data not available</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'changes' && (
            <div className="space-y-4">
              {processingResults?.results?.improvements_made && processingResults.results.improvements_made.length > 0 ? (
                processingResults.results.improvements_made.map((improvement, index) => (
                  <Card key={index} className="border-l-4 border-l-blue-500">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-semibold text-gray-900">Improvement #{index + 1}</h3>
                        <Badge className="bg-blue-100 text-blue-800">
                          Enhanced
                        </Badge>
                      </div>
                      <p className="text-gray-600 mb-3">{improvement}</p>
                      <div className="flex items-start space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-gray-700">Applied successfully</span>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">No specific improvements data available</p>
                </div>
              )}

              {/* Keywords Added Section */}
              {processingResults?.results?.keywords_added && processingResults.results.keywords_added.length > 0 && (
                <Card className="border-l-4 border-l-green-500">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-semibold text-gray-900">Keywords Added</h3>
                      <Badge className="bg-green-100 text-green-800">
                        ATS Optimized
                      </Badge>
                    </div>
                    <p className="text-gray-600 mb-3">Added {processingResults.results.keywords_added.length} relevant keywords for better ATS compatibility</p>
                    <div className="flex flex-wrap gap-2">
                      {processingResults.results.keywords_added.slice(0, 10).map((keyword, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {keyword}
                        </Badge>
                      ))}
                      {processingResults.results.keywords_added.length > 10 && (
                        <Badge variant="outline" className="text-xs">
                          +{processingResults.results.keywords_added.length - 10} more
                        </Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Download Section */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Ready to download?</h3>
              <p className="text-gray-600">
                Your optimized resume is ready! Download as PDF for applications or LaTeX for further customization.
              </p>
            </div>
            <div className="flex space-x-3">
              <Button
                onClick={() => handleDownload('pdf')}
                disabled={isDownloading}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                {isDownloading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <Download className="h-4 w-4 mr-2" />
                )}
                Download PDF
              </Button>
              <Button
                variant="outline"
                onClick={() => handleDownload('latex')}
                disabled={isDownloading}
              >
                <FileText className="h-4 w-4 mr-2" />
                Download LaTeX
              </Button>
              {processingResults?.results?.latex_code && (
                <Button
                  variant="outline"
                  onClick={handleOpenLaTeXEditor}
                  className="border-blue-600 text-blue-600 hover:bg-blue-50"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit LaTeX Source
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Next Steps */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <ArrowRight className="h-5 w-5 text-blue-600" />
            <span>Next Steps</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Share2 className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Apply with Confidence</h3>
              <p className="text-sm text-gray-600">
                Your resume is now optimized for both ATS systems and human recruiters.
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Track Your Success</h3>
              <p className="text-sm text-gray-600">
                Monitor your application success rate and return for more optimizations.
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <RefreshCw className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Optimize for New Roles</h3>
              <p className="text-sm text-gray-600">
                Upload different job descriptions to create role-specific versions.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
