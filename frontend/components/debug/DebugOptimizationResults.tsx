/* Debug component to check optimization results structure */

import React from 'react';
import { ResumeOptimizationResponse } from '@/lib/n8n-api';

interface DebugProps {
  optimizationResults: ResumeOptimizationResponse | null;
}

const DebugOptimizationResults: React.FC<DebugProps> = ({ optimizationResults }) => {
  if (!optimizationResults) {
    return (
      <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
        <p><strong>Debug:</strong> No optimization results yet</p>
      </div>
    );
  }

  const hasLatex = Boolean(optimizationResults.results?.latex_code);
  const latexPreview = optimizationResults.results?.latex_code?.substring(0, 100);

  return (
    <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
      <p><strong>Debug Info:</strong></p>
      <p>• Has LaTeX Code: {hasLatex ? '✅ YES' : '❌ NO'}</p>
      {hasLatex && (
        <p>• LaTeX Preview: {latexPreview}...</p>
      )}
      <p>• Results Structure: {JSON.stringify(Object.keys(optimizationResults.results || {}), null, 2)}</p>
      <p>• Edit Button Should Show: {hasLatex ? '✅ YES' : '❌ NO'}</p>
    </div>
  );
};

export default DebugOptimizationResults;
