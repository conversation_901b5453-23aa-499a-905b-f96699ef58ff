/**
 * Advanced LaTeX Editor Command Palette and Keyboard Shortcuts
 * Provides VSCode-like command palette and comprehensive shortcuts
 */

import * as monaco from 'monaco-editor';

export interface EditorCommand {
  id: string;
  label: string;
  description?: string;
  category: string;
  keybinding?: string;
  icon?: string;
  action: (editor: monaco.editor.IStandaloneCodeEditor) => void | Promise<void>;
}

export interface CommandGroup {
  id: string;
  label: string;
  commands: EditorCommand[];
}

class LaTeXCommandPalette {
  private commands = new Map<string, EditorCommand>();
  private categories = new Map<string, EditorCommand[]>();
  private editor: monaco.editor.IStandaloneCodeEditor | null = null;

  constructor() {
    this.registerDefaultCommands();
  }

  /**
   * Initialize command palette with editor instance
   */
  initialize(editor: monaco.editor.IStandaloneCodeEditor): void {
    this.editor = editor;
    this.registerKeybindings();
  }

  /**
   * Register a new command
   */
  registerCommand(command: EditorCommand): void {
    this.commands.set(command.id, command);

    if (!this.categories.has(command.category)) {
      this.categories.set(command.category, []);
    }
    this.categories.get(command.category)!.push(command);
  }

  /**
   * Execute command by ID
   */
  async executeCommand(commandId: string): Promise<void> {
    const command = this.commands.get(commandId);
    if (!command || !this.editor) return;

    try {
      await command.action(this.editor);
    } catch (error) {
      console.error(`Failed to execute command ${commandId}:`, error);
    }
  }

  /**
   * Get all commands grouped by category
   */
  getCommandGroups(): CommandGroup[] {
    return Array.from(this.categories.entries()).map(([categoryId, commands]) => ({
      id: categoryId,
      label: this.formatCategoryLabel(categoryId),
      commands
    }));
  }

  /**
   * Search commands by query
   */
  searchCommands(query: string): EditorCommand[] {
    const normalizedQuery = query.toLowerCase();
    return Array.from(this.commands.values()).filter(command =>
      command.label.toLowerCase().includes(normalizedQuery) ||
      command.description?.toLowerCase().includes(normalizedQuery) ||
      command.category.toLowerCase().includes(normalizedQuery)
    );
  }

  private registerDefaultCommands(): void {
    // Text Formatting Commands
    this.registerCommand({
      id: 'latex.bold',
      label: 'Bold Text',
      description: 'Make selected text bold',
      category: 'formatting',
      keybinding: 'Ctrl+B',
      action: (editor) => this.wrapSelection(editor, '\\textbf{', '}')
    });

    this.registerCommand({
      id: 'latex.italic',
      label: 'Italic Text',
      description: 'Make selected text italic',
      category: 'formatting',
      keybinding: 'Ctrl+I',
      action: (editor) => this.wrapSelection(editor, '\\textit{', '}')
    });

    this.registerCommand({
      id: 'latex.underline',
      label: 'Underline Text',
      description: 'Underline selected text',
      category: 'formatting',
      keybinding: 'Ctrl+U',
      action: (editor) => this.wrapSelection(editor, '\\underline{', '}')
    });

    this.registerCommand({
      id: 'latex.code',
      label: 'Inline Code',
      description: 'Format selected text as code',
      category: 'formatting',
      keybinding: 'Ctrl+Shift+C',
      action: (editor) => this.wrapSelection(editor, '\\texttt{', '}')
    });

    // Structure Commands
    this.registerCommand({
      id: 'latex.section',
      label: 'Insert Section',
      description: 'Insert a new section',
      category: 'structure',
      keybinding: 'Ctrl+1',
      action: (editor) => this.insertAtCursor(editor, '\\section{Section Title}\n')
    });

    this.registerCommand({
      id: 'latex.subsection',
      label: 'Insert Subsection',
      description: 'Insert a new subsection',
      category: 'structure',
      keybinding: 'Ctrl+2',
      action: (editor) => this.insertAtCursor(editor, '\\subsection{Subsection Title}\n')
    });

    this.registerCommand({
      id: 'latex.subsubsection',
      label: 'Insert Subsubsection',
      description: 'Insert a new subsubsection',
      category: 'structure',
      keybinding: 'Ctrl+3',
      action: (editor) => this.insertAtCursor(editor, '\\subsubsection{Subsubsection Title}\n')
    });

    // List Commands
    this.registerCommand({
      id: 'latex.itemize',
      label: 'Insert Itemized List',
      description: 'Insert an itemized (bullet) list',
      category: 'lists',
      keybinding: 'Ctrl+Shift+8',
      action: (editor) => this.insertAtCursor(editor, '\\begin{itemize}\n  \\item Item 1\n  \\item Item 2\n\\end{itemize}\n')
    });

    this.registerCommand({
      id: 'latex.enumerate',
      label: 'Insert Numbered List',
      description: 'Insert a numbered list',
      category: 'lists',
      keybinding: 'Ctrl+Shift+7',
      action: (editor) => this.insertAtCursor(editor, '\\begin{enumerate}\n  \\item First item\n  \\item Second item\n\\end{enumerate}\n')
    });

    this.registerCommand({
      id: 'latex.description',
      label: 'Insert Description List',
      description: 'Insert a description list',
      category: 'lists',
      action: (editor) => this.insertAtCursor(editor, '\\begin{description}\n  \\item[Term 1] Description 1\n  \\item[Term 2] Description 2\n\\end{description}\n')
    });

    // Math Commands
    this.registerCommand({
      id: 'latex.inline-math',
      label: 'Inline Math',
      description: 'Insert inline math mode',
      category: 'math',
      keybinding: 'Ctrl+M',
      action: (editor) => this.wrapSelection(editor, '$', '$')
    });

    this.registerCommand({
      id: 'latex.display-math',
      label: 'Display Math',
      description: 'Insert display math mode',
      category: 'math',
      keybinding: 'Ctrl+Shift+M',
      action: (editor) => this.wrapSelection(editor, '\\[\n', '\n\\]')
    });

    this.registerCommand({
      id: 'latex.equation',
      label: 'Numbered Equation',
      description: 'Insert numbered equation environment',
      category: 'math',
      action: (editor) => this.insertAtCursor(editor, '\\begin{equation}\n  \n\\end{equation}\n')
    });

    this.registerCommand({
      id: 'latex.align',
      label: 'Aligned Equations',
      description: 'Insert aligned equations environment',
      category: 'math',
      action: (editor) => this.insertAtCursor(editor, '\\begin{align}\n  \n\\end{align}\n')
    });

    // Table Commands
    this.registerCommand({
      id: 'latex.table',
      label: 'Insert Table',
      description: 'Insert a basic table',
      category: 'tables',
      keybinding: 'Ctrl+Shift+T',
      action: (editor) => this.insertAtCursor(editor,
        '\\begin{table}[h]\n' +
        '  \\centering\n' +
        '  \\begin{tabular}{|c|c|c|}\n' +
        '    \\hline\n' +
        '    Header 1 & Header 2 & Header 3 \\\\\n' +
        '    \\hline\n' +
        '    Row 1 & Cell 2 & Cell 3 \\\\\n' +
        '    Row 2 & Cell 2 & Cell 3 \\\\\n' +
        '    \\hline\n' +
        '  \\end{tabular}\n' +
        '  \\caption{Table caption}\n' +
        '  \\label{tab:example}\n' +
        '\\end{table}\n'
      )
    });

    // Figure Commands
    this.registerCommand({
      id: 'latex.figure',
      label: 'Insert Figure',
      description: 'Insert a figure environment',
      category: 'figures',
      keybinding: 'Ctrl+Shift+F',
      action: (editor) => this.insertAtCursor(editor,
        '\\begin{figure}[h]\n' +
        '  \\centering\n' +
        '  \\includegraphics[width=0.8\\textwidth]{image.png}\n' +
        '  \\caption{Figure caption}\n' +
        '  \\label{fig:example}\n' +
        '\\end{figure}\n'
      )
    });

    // References
    this.registerCommand({
      id: 'latex.ref',
      label: 'Insert Reference',
      description: 'Insert a reference to a label',
      category: 'references',
      action: (editor) => this.insertAtCursor(editor, '\\ref{}')
    });

    this.registerCommand({
      id: 'latex.cite',
      label: 'Insert Citation',
      description: 'Insert a citation',
      category: 'references',
      action: (editor) => this.insertAtCursor(editor, '\\cite{}')
    });

    // Navigation Commands
    this.registerCommand({
      id: 'latex.goto-line',
      label: 'Go to Line',
      description: 'Jump to a specific line number',
      category: 'navigation',
      keybinding: 'Ctrl+G',
      action: (editor) => {
        const lineNumber = prompt('Enter line number:');
        if (lineNumber) {
          const line = parseInt(lineNumber);
          editor.setPosition({ lineNumber: line, column: 1 });
          editor.revealLineInCenter(line);
        }
      }
    });

    // Editor Commands
    this.registerCommand({
      id: 'latex.find',
      label: 'Find',
      description: 'Open find dialog',
      category: 'editor',
      keybinding: 'Ctrl+F',
      action: (editor) => editor.trigger('', 'actions.find', undefined)
    });

    this.registerCommand({
      id: 'latex.replace',
      label: 'Find and Replace',
      description: 'Open find and replace dialog',
      category: 'editor',
      keybinding: 'Ctrl+H',
      action: (editor) => editor.trigger('', 'editor.action.startFindReplaceAction', undefined)
    });

    this.registerCommand({
      id: 'latex.comment-line',
      label: 'Toggle Line Comment',
      description: 'Toggle line comment',
      category: 'editor',
      keybinding: 'Ctrl+/',
      action: (editor) => editor.trigger('', 'editor.action.commentLine', undefined)
    });

    this.registerCommand({
      id: 'latex.format',
      label: 'Format Document',
      description: 'Format the entire document',
      category: 'editor',
      keybinding: 'Shift+Alt+F',
      action: (editor) => editor.trigger('', 'editor.action.formatDocument', undefined)
    });
  }

  private registerKeybindings(): void {
    if (!this.editor) return;

    for (const command of this.commands.values()) {
      if (command.keybinding) {
        this.editor.addCommand(
          this.parseKeybinding(command.keybinding),
          () => this.executeCommand(command.id)
        );
      }
    }
  }

  private parseKeybinding(keybinding: string): number {
    const parts = keybinding.split('+');
    let keyMod = 0;

    for (let i = 0; i < parts.length - 1; i++) {
      const modifier = parts[i].toLowerCase();
      switch (modifier) {
        case 'ctrl':
          keyMod |= monaco.KeyMod.CtrlCmd;
          break;
        case 'shift':
          keyMod |= monaco.KeyMod.Shift;
          break;
        case 'alt':
          keyMod |= monaco.KeyMod.Alt;
          break;
      }
    }

    const key = parts[parts.length - 1];
    const keyCode = this.getKeyCode(key);

    return keyMod | keyCode;
  }

  private getKeyCode(key: string): number {
    const keyMap: Record<string, number> = {
      'A': monaco.KeyCode.KeyA, 'B': monaco.KeyCode.KeyB, 'C': monaco.KeyCode.KeyC,
      'D': monaco.KeyCode.KeyD, 'E': monaco.KeyCode.KeyE, 'F': monaco.KeyCode.KeyF,
      'G': monaco.KeyCode.KeyG, 'H': monaco.KeyCode.KeyH, 'I': monaco.KeyCode.KeyI,
      'J': monaco.KeyCode.KeyJ, 'K': monaco.KeyCode.KeyK, 'L': monaco.KeyCode.KeyL,
      'M': monaco.KeyCode.KeyM, 'N': monaco.KeyCode.KeyN, 'O': monaco.KeyCode.KeyO,
      'P': monaco.KeyCode.KeyP, 'Q': monaco.KeyCode.KeyQ, 'R': monaco.KeyCode.KeyR,
      'S': monaco.KeyCode.KeyS, 'T': monaco.KeyCode.KeyT, 'U': monaco.KeyCode.KeyU,
      'V': monaco.KeyCode.KeyV, 'W': monaco.KeyCode.KeyW, 'X': monaco.KeyCode.KeyX,
      'Y': monaco.KeyCode.KeyY, 'Z': monaco.KeyCode.KeyZ,
      '1': monaco.KeyCode.Digit1, '2': monaco.KeyCode.Digit2, '3': monaco.KeyCode.Digit3,
      '4': monaco.KeyCode.Digit4, '5': monaco.KeyCode.Digit5, '6': monaco.KeyCode.Digit6,
      '7': monaco.KeyCode.Digit7, '8': monaco.KeyCode.Digit8, '9': monaco.KeyCode.Digit9,
      '0': monaco.KeyCode.Digit0,
      '/': monaco.KeyCode.Slash,
      'F1': monaco.KeyCode.F1, 'F2': monaco.KeyCode.F2, 'F3': monaco.KeyCode.F3,
      'Enter': monaco.KeyCode.Enter, 'Space': monaco.KeyCode.Space,
      'Escape': monaco.KeyCode.Escape, 'Tab': monaco.KeyCode.Tab
    };

    return keyMap[key] || monaco.KeyCode.KeyA;
  }

  private wrapSelection(editor: monaco.editor.IStandaloneCodeEditor, before: string, after: string): void {
    const selection = editor.getSelection();
    if (!selection) return;

    const selectedText = editor.getModel()?.getValueInRange(selection) || '';
    const newText = before + selectedText + after;

    editor.executeEdits('', [{
      range: selection,
      text: newText,
      forceMoveMarkers: true
    }]);

    // Position cursor after the wrapped text
    if (selectedText === '') {
      const newPosition = {
        lineNumber: selection.startLineNumber,
        column: selection.startColumn + before.length
      };
      editor.setPosition(newPosition);
    }
  }

  private insertAtCursor(editor: monaco.editor.IStandaloneCodeEditor, text: string): void {
    const position = editor.getPosition();
    if (!position) return;

    editor.executeEdits('', [{
      range: new monaco.Range(position.lineNumber, position.column, position.lineNumber, position.column),
      text,
      forceMoveMarkers: true
    }]);
  }

  private formatCategoryLabel(categoryId: string): string {
    return categoryId.charAt(0).toUpperCase() + categoryId.slice(1).replace(/([A-Z])/g, ' $1');
  }
}

export const latexCommandPalette = new LaTeXCommandPalette();
export default LaTeXCommandPalette;
