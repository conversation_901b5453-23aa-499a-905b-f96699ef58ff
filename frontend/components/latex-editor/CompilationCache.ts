/**
 * LaTeX Compilation Cache Manager
 * Implements intelligent caching to avoid unnecessary recompilations
 */

interface CacheEntry {
  content: string;
  contentHash: string;
  pdfUrl: string;
  timestamp: Date;
  jobId: string;
  logs?: string;
}

interface CacheOptions {
  maxSize: number;
  maxAge: number; // in milliseconds
  enablePersistence: boolean;
}

export class CompilationCache {
  private cache = new Map<string, CacheEntry>();
  private options: CacheOptions;
  private storageKey = 'latex-compilation-cache';

  constructor(options: Partial<CacheOptions> = {}) {
    this.options = {
      maxSize: 50, // Maximum number of cached compilations
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      enablePersistence: true,
      ...options
    };

    if (this.options.enablePersistence) {
      this.loadFromStorage();
    }

    // Cleanup expired entries periodically
    setInterval(() => this.cleanup(), 60 * 1000); // Every minute
  }

  /**
   * Generate hash for LaTeX content for cache key
   */
  private generateHash(content: string): string {
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }

  /**
   * Check if content is cached and not expired
   */
  has(content: string): boolean {
    const hash = this.generateHash(content);
    const entry = this.cache.get(hash);

    if (!entry) return false;

    // Check if expired
    const now = new Date().getTime();
    const entryTime = entry.timestamp.getTime();

    if (now - entryTime > this.options.maxAge) {
      this.cache.delete(hash);
      return false;
    }

    return true;
  }

  /**
   * Get cached compilation result
   */
  get(content: string): CacheEntry | null {
    if (!this.has(content)) return null;

    const hash = this.generateHash(content);
    const entry = this.cache.get(hash);

    // Update access time (LRU-style)
    if (entry) {
      entry.timestamp = new Date();
      this.cache.set(hash, entry);
    }

    return entry || null;
  }

  /**
   * Cache compilation result
   */
  set(content: string, pdfUrl: string, jobId: string, logs?: string): void {
    const hash = this.generateHash(content);

    // Remove oldest entries if cache is full
    if (this.cache.size >= this.options.maxSize) {
      const oldestKey = this.getOldestKey();
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }

    const entry: CacheEntry = {
      content,
      contentHash: hash,
      pdfUrl,
      timestamp: new Date(),
      jobId,
      logs
    };

    this.cache.set(hash, entry);

    if (this.options.enablePersistence) {
      this.saveToStorage();
    }
  }

  /**
   * Check if content has minor changes that might not require recompilation
   */
  hasMinorChanges(oldContent: string, newContent: string): boolean {
    // Check for whitespace-only changes
    const oldNormalized = oldContent.replace(/\s+/g, ' ').trim();
    const newNormalized = newContent.replace(/\s+/g, ' ').trim();

    if (oldNormalized === newNormalized) return true;

    // Check for comment-only changes
    const oldWithoutComments = oldContent.replace(/%.*$/gm, '');
    const newWithoutComments = newContent.replace(/%.*$/gm, '');

    return oldWithoutComments.trim() === newWithoutComments.trim();
  }

  /**
   * Get statistics about cache performance
   */
  getStats(): {
    size: number;
    hitRate: number;
    oldestEntry: Date | null;
    newestEntry: Date | null;
  } {
    const entries = Array.from(this.cache.values());
    const timestamps = entries.map(e => e.timestamp);

    return {
      size: this.cache.size,
      hitRate: 0, // TODO: Implement hit rate tracking
      oldestEntry: timestamps.length > 0 ? new Date(Math.min(...timestamps.map(t => t.getTime()))) : null,
      newestEntry: timestamps.length > 0 ? new Date(Math.max(...timestamps.map(t => t.getTime()))) : null
    };
  }

  /**
   * Clear all cached entries
   */
  clear(): void {
    this.cache.clear();
    if (this.options.enablePersistence) {
      localStorage.removeItem(this.storageKey);
    }
  }

  /**
   * Remove expired entries
   */
  private cleanup(): void {
    const now = new Date().getTime();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp.getTime() > this.options.maxAge) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));

    if (keysToDelete.length > 0 && this.options.enablePersistence) {
      this.saveToStorage();
    }
  }

  /**
   * Get the key of the oldest cache entry
   */
  private getOldestKey(): string | null {
    let oldestKey: string | null = null;
    let oldestTime = Infinity;

    for (const [key, entry] of this.cache.entries()) {
      const time = entry.timestamp.getTime();
      if (time < oldestTime) {
        oldestTime = time;
        oldestKey = key;
      }
    }

    return oldestKey;
  }

  /**
   * Save cache to localStorage
   */
  private saveToStorage(): void {
    try {
      const cacheData = Array.from(this.cache.entries()).map(([key, entry]) => ({
        key,
        ...entry,
        timestamp: entry.timestamp.toISOString()
      }));

      localStorage.setItem(this.storageKey, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('Failed to save compilation cache:', error);
    }
  }

  /**
   * Load cache from localStorage
   */
  private loadFromStorage(): void {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (!stored) return;

      const cacheData = JSON.parse(stored);

      for (const item of cacheData) {
        const entry: CacheEntry = {
          content: item.content,
          contentHash: item.contentHash,
          pdfUrl: item.pdfUrl,
          timestamp: new Date(item.timestamp),
          jobId: item.jobId,
          logs: item.logs
        };

        // Only load non-expired entries
        const now = new Date().getTime();
        if (now - entry.timestamp.getTime() <= this.options.maxAge) {
          this.cache.set(item.key, entry);
        }
      }
    } catch (error) {
      console.warn('Failed to load compilation cache:', error);
    }
  }
}

// Global cache instance
export const compilationCache = new CompilationCache();

export default CompilationCache;
