/**
 * Command Palette UI Component for LaTeX Editor
 * VSCode-style command palette with fuzzy search
 */

"use client";

import React, { useState, useEffect, useRef } from 'react';
import { Dialog, DialogContent } from '../ui/dialog';
import { ScrollArea } from '../ui/scroll-area';
import { latexCommandPalette, EditorCommand, CommandGroup } from './LaTeXCommandPalette';
import * as monaco from 'monaco-editor';

interface CommandPaletteProps {
  isOpen: boolean;
  onClose: () => void;
  editor: monaco.editor.IStandaloneCodeEditor | null;
}

interface CommandItemProps {
  command: EditorCommand;
  isSelected: boolean;
  onClick: () => void;
}

const CommandItem: React.FC<CommandItemProps> = ({ command, isSelected, onClick }) => (
  <div
    className={`
      flex items-center justify-between px-3 py-2 cursor-pointer rounded-md
      ${isSelected ? 'bg-blue-500 text-white' : 'hover:bg-gray-100 dark:hover:bg-gray-700'}
      transition-colors duration-150
    `}
    onClick={onClick}
  >
    <div className="flex-1 min-w-0">
      <div className="flex items-center space-x-2">
        <span className="font-medium truncate">{command.label}</span>
        {command.keybinding && (
          <span className="text-xs bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded">
            {command.keybinding}
          </span>
        )}
      </div>
      {command.description && (
        <p className="text-sm text-gray-500 dark:text-gray-400 truncate mt-1">
          {command.description}
        </p>
      )}
    </div>
    <span className="text-xs text-gray-400 dark:text-gray-500 ml-2 capitalize">
      {command.category}
    </span>
  </div>
);

interface CommandGroupProps {
  group: CommandGroup;
  selectedIndex: number;
  groupStartIndex: number;
  onCommandClick: (command: EditorCommand) => void;
}

const CommandGroupComponent: React.FC<CommandGroupProps> = ({
  group,
  selectedIndex,
  groupStartIndex,
  onCommandClick
}) => (
  <div className="mb-4">
    <div className="px-3 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
      {group.label}
    </div>
    <div className="space-y-1">
      {group.commands.map((command, index) => (
        <CommandItem
          key={command.id}
          command={command}
          isSelected={selectedIndex === groupStartIndex + index}
          onClick={() => onCommandClick(command)}
        />
      ))}
    </div>
  </div>
);

const CommandPalette: React.FC<CommandPaletteProps> = ({
  isOpen,
  onClose,
  editor
}) => {
  const [query, setQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [commandGroups, setCommandGroups] = useState<CommandGroup[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const scrollRef = useRef<HTMLDivElement>(null);

  // Initialize command palette with editor
  useEffect(() => {
    if (editor) {
      latexCommandPalette.initialize(editor);
    }
  }, [editor]);

  // Load commands and handle search
  useEffect(() => {
    if (query.trim()) {
      const filtered = latexCommandPalette.searchCommands(query);

      // Group filtered commands by category
      const groups = new Map<string, EditorCommand[]>();
      filtered.forEach(command => {
        if (!groups.has(command.category)) {
          groups.set(command.category, []);
        }
        groups.get(command.category)!.push(command);
      });

      setCommandGroups(
        Array.from(groups.entries()).map(([categoryId, commands]) => ({
          id: categoryId,
          label: categoryId.charAt(0).toUpperCase() + categoryId.slice(1),
          commands
        }))
      );
    } else {
      setCommandGroups(latexCommandPalette.getCommandGroups());
    }

    setSelectedIndex(0);
  }, [query]);

  // Focus input when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Get all commands in current view for navigation
  const getAllCommands = (): EditorCommand[] => {
    return commandGroups.flatMap(group => group.commands);
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    const allCommands = getAllCommands();

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => Math.min(prev + 1, allCommands.length - 1));
        scrollToSelected();
        break;

      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => Math.max(prev - 1, 0));
        scrollToSelected();
        break;

      case 'Enter':
        e.preventDefault();
        const selectedCommand = allCommands[selectedIndex];
        if (selectedCommand) {
          executeCommand(selectedCommand);
        }
        break;

      case 'Escape':
        e.preventDefault();
        onClose();
        break;
    }
  };

  const scrollToSelected = () => {
    setTimeout(() => {
      const selectedElement = scrollRef.current?.querySelector(`[data-index="${selectedIndex}"]`);
      selectedElement?.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
    }, 0);
  };

  const executeCommand = async (command: EditorCommand) => {
    try {
      await latexCommandPalette.executeCommand(command.id);
      onClose();
      setQuery('');
    } catch (error) {
      console.error('Failed to execute command:', error);
    }
  };

  const getCommandIndex = (groupIndex: number, commandIndex: number): number => {
    let index = 0;
    for (let i = 0; i < groupIndex; i++) {
      index += commandGroups[i].commands.length;
    }
    return index + commandIndex;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] p-0 overflow-hidden">
        <div className="border-b">
          <input
            ref={inputRef}
            type="text"
            placeholder="Type a command..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            className="w-full px-4 py-3 text-lg bg-transparent border-none outline-none focus:ring-0"
          />
        </div>

        <ScrollArea ref={scrollRef} className="max-h-96">
          <div className="p-2">
            {commandGroups.length === 0 ? (
              <div className="px-3 py-8 text-center text-gray-500">
                {query.trim() ? 'No commands found' : 'Loading commands...'}
              </div>
            ) : (
              commandGroups.map((group, groupIndex) => {
                const groupStartIndex = getCommandIndex(groupIndex, 0);
                return (
                  <div key={group.id}>
                    <CommandGroupComponent
                      group={group}
                      selectedIndex={selectedIndex}
                      groupStartIndex={groupStartIndex}
                      onCommandClick={executeCommand}
                    />
                  </div>
                );
              })
            )}
          </div>
        </ScrollArea>

        <div className="border-t px-4 py-2 text-xs text-gray-500 bg-gray-50 dark:bg-gray-800">
          <div className="flex justify-between">
            <span>Use ↑↓ to navigate, Enter to select, Escape to close</span>
            {getAllCommands().length > 0 && (
              <span>{selectedIndex + 1} of {getAllCommands().length}</span>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CommandPalette;
