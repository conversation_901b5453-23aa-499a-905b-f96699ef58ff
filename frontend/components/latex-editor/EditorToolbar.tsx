/**
 * Advanced LaTeX Editor Toolbar with shor  const insertTextFormat = (format: 'bold' | 'italic' | 'underline' | 'code') => {
    const commands = {
      bold: '\\textbf{${1:text}}',
      italic: '\\textit{${1:text}}',
      underline: '\\underline{${1:text}}',
      code: '\\texttt{${1:text}}'
    };
    onInsertCommand(commands[format]);
  }; quick actions
 */
'use client';

import React from 'react';
import { Button } from '../ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../ui/tooltip';
import { Badge } from '../ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import {
  Bold,
  Italic,
  List,
  ListOrdered,
  Image,
  Table,
  Calculator,
  Type,
  ChevronDown,
  Zap,
  FileText,
  Link2
} from 'lucide-react';

export interface EditorToolbarProps {
  onInsertCommand: (command: string) => void;
  onInsertSnippet: (snippet: string) => void;
  className?: string;
}

const EditorToolbar: React.FC<EditorToolbarProps> = ({
  onInsertCommand,
  onInsertSnippet,
  className = ''
}) => {
  const insertTextFormat = (format: 'bold' | 'italic' | 'underline' | 'code') => {
    const commands = {
      bold: '\\textbf{${1:text}}',
      italic: '\\textit{${1:text}}',
      underline: '\\underline{${1:text}}',
      code: '\\texttt{${1:code}}'
    };
    onInsertSnippet(commands[format]);
  };

  const insertList = (type: 'itemize' | 'enumerate') => {
    const snippets = {
      itemize: `\\begin{itemize}
  \\item \${1:First item}
  \\item \${2:Second item}
\\end{itemize}`,
      enumerate: `\\begin{enumerate}
  \\item \${1:First item}
  \\item \${2:Second item}
\\end{enumerate}`
    };
    onInsertSnippet(snippets[type]);
  };

  const insertMath = (type: 'inline' | 'display' | 'equation' | 'align') => {
    const snippets = {
      inline: '$\${1:equation}$',
      display: `$$
\${1:equation}
$$`,
      equation: `\\begin{equation}
  \${1:equation}
\\end{equation}`,
      align: `\\begin{align}
  \${1:equation} &= \${2:result} \\\\
  \${3:equation} &= \${4:result}
\\end{align}`
    };
    onInsertSnippet(snippets[type]);
  };

  const insertStructure = (type: 'section' | 'subsection' | 'subsubsection' | 'paragraph') => {
    const commands = {
      section: '\\section{${1:Section Title}}',
      subsection: '\\subsection{${1:Subsection Title}}',
      subsubsection: '\\subsubsection{${1:Subsubsection Title}}',
      paragraph: '\\paragraph{${1:Paragraph Title}}'
    };
    onInsertSnippet(commands[type]);
  };

  const insertTable = () => {
    const tableSnippet = `\\begin{table}[h]
  \\centering
  \\begin{tabular}{|\${1:c|c|c}|}
    \\hline
    \${2:Header 1} & \${3:Header 2} & \${4:Header 3} \\\\
    \\hline
    \${5:Row 1 Col 1} & \${6:Row 1 Col 2} & \${7:Row 1 Col 3} \\\\
    \${8:Row 2 Col 1} & \${9:Row 2 Col 2} & \${10:Row 2 Col 3} \\\\
    \\hline
  \\end{tabular}
  \\caption{\${11:Table caption}}
  \\label{tab:\${12:label}}
\\end{table}`;
    onInsertSnippet(tableSnippet);
  };

  const insertFigure = () => {
    const figureSnippet = `\\begin{figure}[h]
  \\centering
  \\includegraphics[width=\${1:0.8}\\textwidth]{\${2:filename}}
  \\caption{\${3:Figure caption}}
  \\label{fig:\${4:label}}
\\end{figure}`;
    onInsertSnippet(figureSnippet);
  };

  const insertReference = () => {
    const refSnippet = '\\ref{\${1:label}}';
    onInsertSnippet(refSnippet);
  };

  return (
    <TooltipProvider>
      <div className={`flex items-center gap-1 p-2 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 ${className}`}>
        {/* Text Formatting */}
        <div className="flex items-center gap-1 pr-2 border-r border-gray-300 dark:border-gray-600">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => insertTextFormat('bold')}
                className="h-8 w-8 p-0"
              >
                <Bold className="w-4 h-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Bold text (\\textbf)</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => insertTextFormat('italic')}
                className="h-8 w-8 p-0"
              >
                <Italic className="w-4 h-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Italic text (\\textit)</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => insertTextFormat('code')}
                className="h-8 w-8 p-0"
              >
                <Type className="w-4 h-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Monospace text (\\texttt)</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Lists */}
        <div className="flex items-center gap-1 pr-2 border-r border-gray-300 dark:border-gray-600">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => insertList('itemize')}
                className="h-8 w-8 p-0"
              >
                <List className="w-4 h-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Bulleted list</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => insertList('enumerate')}
                className="h-8 w-8 p-0"
              >
                <ListOrdered className="w-4 h-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Numbered list</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Structure */}
        <div className="pr-2 border-r border-gray-300 dark:border-gray-600">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 px-2">
                <FileText className="w-4 h-4 mr-1" />
                Structure
                <ChevronDown className="w-3 h-3 ml-1" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => insertStructure('section')}>
                Section
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => insertStructure('subsection')}>
                Subsection
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => insertStructure('subsubsection')}>
                Subsubsection
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => insertStructure('paragraph')}>
                Paragraph
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Math */}
        <div className="pr-2 border-r border-gray-300 dark:border-gray-600">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 px-2">
                <Calculator className="w-4 h-4 mr-1" />
                Math
                <ChevronDown className="w-3 h-3 ml-1" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => insertMath('inline')}>
                Inline Math ($...$)
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => insertMath('display')}>
                Display Math ($$...$$)
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => insertMath('equation')}>
                Equation Environment
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => insertMath('align')}>
                Align Environment
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Insertions */}
        <div className="flex items-center gap-1 pr-2 border-r border-gray-300 dark:border-gray-600">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={insertTable}
                className="h-8 w-8 p-0"
              >
                <Table className="w-4 h-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Insert table</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={insertFigure}
                className="h-8 w-8 p-0"
              >
                <Image className="w-4 h-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Insert figure</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={insertReference}
                className="h-8 w-8 p-0"
              >
                <Link2 className="w-4 h-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Insert reference</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Quick Snippets */}
        <div className="pr-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 px-2">
                <Zap className="w-4 h-4 mr-1" />
                Snippets
                <ChevronDown className="w-3 h-3 ml-1" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => onInsertSnippet('\\resumeSubheading\n\t{${1:Company Name}}{${2:Date Range}}\n\t{${3:Position Title}}{${4:Location}}')}>
                Resume Job Entry
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onInsertSnippet('\\resumeItemListStart\n\t\\resumeItem{${1:Achievement}}\n\t\\resumeItem{${2:Another achievement}}\n\\resumeItemListEnd')}>
                Resume Item List
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onInsertSnippet('\\resumeProjectHeading\n\t{\\textbf{${1:Project Name}} $|$ \\emph{${2:Technologies}}}{${3:Date}}')}>
                Resume Project
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </TooltipProvider>
  );
};

export default EditorToolbar;
