'use client';

import React, { useRef, useEffect, useState } from 'react';
import { Editor } from '@monaco-editor/react';
import { editor } from 'monaco-editor';
import { LaTeXEditorProps } from './types';
import { registerLatexLanguage } from './latex-language';
import { LatexSyntaxValidator } from './LaTeXSyntaxValidator';

// Default LaTeX template
const DEFAULT_LATEX_CONTENT = `\\documentclass{article}
\\usepackage[utf8]{inputenc}
\\usepackage[T1]{fontenc}
\\usepackage{amsmath}
\\usepackage{amsfonts}
\\usepackage{amssymb}
\\usepackage{graphicx}

\\title{LaTeX Document}
\\author{Your Name}
\\date{\\today}

\\begin{document}

\\maketitle

\\section{Introduction}

This is a sample LaTeX document. You can edit this content and see the compiled PDF preview on the right.

\\section{Mathematical Expressions}

Here's an inline mathematical expression: $E = mc^2$.

And here's a display equation:
\\begin{equation}
    \\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}
\\end{equation}

\\section{Lists}

\\begin{itemize}
    \\item First item
    \\item Second item
    \\item Third item
\\end{itemize}

\\section{Tables}

\\begin{table}[h]
    \\centering
    \\begin{tabular}{|c|c|c|}
        \\hline
        Name & Age & City \\\\
        \\hline
        Alice & 25 & New York \\\\
        Bob & 30 & London \\\\
        Charlie & 28 & Paris \\\\
        \\hline
    \\end{tabular}
    \\caption{Sample Table}
    \\label{tab:sample}
\\end{table}

\\end{document}`;

const LaTeXEditor: React.FC<LaTeXEditorProps> = ({
  initialContent = DEFAULT_LATEX_CONTENT,
  onChange,
  onEditorReady,
  readOnly = false,
  theme = 'vs-light',
  fontSize = 14,
  wordWrap = true,
  minimap = false,
  className = '',
  height = '100%',
}) => {
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);
  const [syntaxValidator, setSyntaxValidator] = useState<LatexSyntaxValidator | null>(null);

  const handleEditorDidMount = (editorInstance: editor.IStandaloneCodeEditor, monaco: unknown) => {
    editorRef.current = editorInstance;

    // Register LaTeX language support
    registerLatexLanguage(monaco as typeof import('monaco-editor'));

    // Set up syntax validation
    const validator = new LatexSyntaxValidator(editorInstance);
    setSyntaxValidator(validator);

    // Set editor theme
    const editorTheme = theme === 'vs-dark' ? 'latex-dark-theme' : 'latex-theme';
    (monaco as typeof import('monaco-editor')).editor.setTheme(editorTheme);

    // Configure editor options
    editorInstance.updateOptions({
      fontFamily: 'JetBrains Mono, Consolas, Monaco, monospace',
      fontSize: fontSize,
      lineNumbers: 'on',
      minimap: { enabled: minimap },
      wordWrap: wordWrap ? 'on' : 'off',
      automaticLayout: true,
      scrollBeyondLastLine: false,
      renderWhitespace: 'selection',
      cursorBlinking: 'smooth',
      cursorSmoothCaretAnimation: 'on',
      smoothScrolling: true,
      contextmenu: true,
      quickSuggestions: {
        other: true,
        comments: false,
        strings: true
      },
      suggestOnTriggerCharacters: true,
      acceptSuggestionOnEnter: 'on',
      tabCompletion: 'on',
      parameterHints: {
        enabled: true
      },
    });

    // Call onEditorReady callback
    if (onEditorReady) {
      onEditorReady(editorInstance);
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (syntaxValidator) {
        syntaxValidator.dispose();
      }
    };
  }, [syntaxValidator]);

  return (
    <div className={`latex-editor h-full ${className}`}>
      <Editor
        height={height}
        defaultLanguage="latex"
        defaultValue={initialContent}
        theme={theme}
        options={{
          readOnly,
          selectOnLineNumbers: true,
          mouseWheelZoom: true,
          formatOnPaste: true,
          formatOnType: true,
        }}
        onChange={(value) => {
          if (onChange && value !== undefined) {
            onChange(value);
          }
        }}
        onMount={handleEditorDidMount}
      />
    </div>
  );
};

export default LaTeXEditor;
