'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { PanelGroup, Panel, PanelResizeHandle } from 'react-resizable-panels';
import * as monaco from 'monaco-editor';
import { editor } from 'monaco-editor';
import LaTeXEditor from './LaTeXEditor';
import PDFViewer from './PDFViewer';
import TemplateSelector from './TemplateSelector';
import EditorToolbar from './EditorToolbar';
import FileManager from './FileManager';
import CommandPalette from './CommandPalette';
import SettingsPanel, { EditorSettings, defaultSettings } from './SettingsPanel';
import { LaTeXEditorLayoutProps, CompilationJob, Template } from './types';
import { latexAPI } from '../../lib/latex-api';
import { latexWebSocket } from '../../lib/latex-websocket';
import { LatexSyntaxValidator, SyntaxError } from './LaTeXSyntaxValidator';
import { CompilationCache } from './CompilationCache';
import { LaTeXErrorAnalyzer } from './LaTeXErrorAnalyzer';
import { latexCommandPalette } from './LaTeXCommandPalette';
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";
import {
  AlertCircle,
  CheckCircle,
  Info,
  Palette,
  Eye,
  EyeOff,
  FileText,
  Command,
  Settings
} from 'lucide-react';

const LaTeXEditorLayout: React.FC<LaTeXEditorLayoutProps> = ({
  initialContent,
  editorProps,
  pdfViewerProps,
  className = '',
  orientation = 'horizontal',
  defaultSizes = [50, 50],
  minSizes = [20, 20]
}) => {
  const [latexContent, setLatexContent] = useState(initialContent || '');
  const [currentJob, setCurrentJob] = useState<CompilationJob | null>(null);
  const [pdfUrl, setPdfUrl] = useState<string | undefined>(undefined);
  const [isCompiling, setIsCompiling] = useState(false);
  const [compilationError, setCompilationError] = useState<string | undefined>(undefined);
  const [lastCompiledContent, setLastCompiledContent] = useState<string>('');
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'error'>('disconnected');
  const [useWebSocket] = useState(true);
  const [showTemplates, setShowTemplates] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [syntaxErrors, setSyntaxErrors] = useState<SyntaxError[]>([]);
  const [syntaxValidator, setSyntaxValidator] = useState<LatexSyntaxValidator | null>(null);
  const [editorTheme, setEditorTheme] = useState<'latex-theme' | 'latex-dark-theme'>('latex-theme');
  const [showErrorPanel, setShowErrorPanel] = useState(true);
  const [editorInstance, setEditorInstance] = useState<editor.IStandaloneCodeEditor | null>(null);

  // Phase 8: Performance & UX enhancements
  const [compilationCache] = useState(() => new CompilationCache());
  const [errorAnalyzer] = useState(() => new LaTeXErrorAnalyzer());
  const [showCommandPalette, setShowCommandPalette] = useState(false);
  const [showSettingsPanel, setShowSettingsPanel] = useState(false);
  const [editorSettings, setEditorSettings] = useState<EditorSettings>(defaultSettings);

  // Initialize command palette with editor
  useEffect(() => {
    if (editorInstance) {
      latexCommandPalette.initialize(editorInstance);
    }
  }, [editorInstance]);

  // Handle template selection
  const handleTemplateSelect = useCallback((template: Template) => {
    setSelectedTemplate(template);
    setLatexContent(template.content);
    setShowTemplates(false);
    // Clear current compilation
    setPdfUrl(undefined);
    setCompilationError(undefined);
    setCurrentJob(null);
  }, []);

  const compileTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize WebSocket connection
  useEffect(() => {
    if (useWebSocket) {
      latexWebSocket.setHandlers({
        onConnectionStatus: setConnectionStatus,
        onCompilationStarted: (jobId) => {
          console.log(`Compilation started: ${jobId}`);
          setIsCompiling(true);
          setCompilationError(undefined);
        },
        onCompilationProgress: (jobId, message) => {
          console.log(`Compilation progress: ${jobId} - ${message}`);
          // You could show progress in UI here
        },
        onCompilationSuccess: (jobId, pdfUrl, logs) => {
          console.log(`Compilation success: ${jobId}`);
          setIsCompiling(false);

          // Ensure PDF URL is absolute
          const absolutePdfUrl = pdfUrl.startsWith('http')
            ? pdfUrl
            : `http://localhost:8000${pdfUrl}`;

          setPdfUrl(absolutePdfUrl);
          setCompilationError(undefined);

          // Update job state
          if (currentJob && currentJob.id === jobId) {
            setCurrentJob({
              ...currentJob,
              status: 'success',
              pdfUrl: absolutePdfUrl,
              logs,
              updatedAt: new Date(),
            });
          }
        },
        onCompilationError: (jobId, error, logs) => {
          console.log(`Compilation error: ${jobId} - ${error}`);
          setIsCompiling(false);
          setCompilationError(error);

          // Update job state
          if (currentJob && currentJob.id === jobId) {
            setCurrentJob({
              ...currentJob,
              status: 'error',
              error,
              logs,
              updatedAt: new Date(),
            });
          }
        },
      });

      latexWebSocket.connect().catch(error => {
        console.error('Failed to connect WebSocket:', error);
        setConnectionStatus('error');
      });

      return () => {
        latexWebSocket.disconnect();
      };
    }
  }, [useWebSocket, currentJob]);

  // Setup syntax validation
  useEffect(() => {
    const handleSyntaxErrors = (event: CustomEvent<{ errors: SyntaxError[] }>) => {
      setSyntaxErrors(event.detail.errors);
    };

    window.addEventListener('latex-syntax-errors', handleSyntaxErrors as EventListener);

    return () => {
      window.removeEventListener('latex-syntax-errors', handleSyntaxErrors as EventListener);
      if (syntaxValidator) {
        syntaxValidator.dispose();
      }
    };
  }, [syntaxValidator]);

  // Compile LaTeX content with caching
  const handleCompile = useCallback(async (content: string) => {
    if (!content.trim() || content === lastCompiledContent) {
      return;
    }

    // Check cache first
    const cachedResult = compilationCache.get(content);
    if (cachedResult && cachedResult.pdfUrl) {
      console.log('Using cached compilation result');

      // Ensure PDF URL is absolute
      const pdfUrl = cachedResult.pdfUrl.startsWith('http')
        ? cachedResult.pdfUrl
        : `http://localhost:8000${cachedResult.pdfUrl}`;

      setPdfUrl(pdfUrl);
      setCompilationError(undefined);
      setIsCompiling(false);
      return;
    }

    setIsCompiling(true);
    setCompilationError(undefined);

    try {
      const jobId = `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Create local job state
      const job: CompilationJob = {
        id: jobId,
        status: 'pending',
        content,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      setCurrentJob(job);
      setLastCompiledContent(content);

      // Use WebSocket if connected, otherwise fallback to HTTP API
      if (useWebSocket && latexWebSocket.isConnected()) {
        // Use WebSocket for real-time updates
        latexWebSocket.subscribeToJob(jobId);
        latexWebSocket.compileLatex(content, jobId);
      } else {
        // Fallback to HTTP API
        const jobResponse = await latexAPI.compileLatex({
          latex_content: content,
          job_id: jobId
        });

        // Poll for completion
        const completedJob = await latexAPI.pollJobCompletion(jobResponse.job_id);

        // Update job state
        const updatedJob: CompilationJob = {
          id: completedJob.id,
          status: completedJob.status,
          content: completedJob.content,
          logs: completedJob.logs,
          error: completedJob.error,
          pdfUrl: completedJob.pdf_url,
          createdAt: new Date(completedJob.created_at),
          updatedAt: new Date(completedJob.updated_at),
        };

        setCurrentJob(updatedJob);

        if (completedJob.status === 'success' && completedJob.pdf_url) {
          // Ensure PDF URL is absolute
          const absolutePdfUrl = completedJob.pdf_url.startsWith('http')
            ? completedJob.pdf_url
            : `http://localhost:8000${completedJob.pdf_url}`;

          // Cache successful compilation
          compilationCache.set(content, absolutePdfUrl, jobId, completedJob.logs);

          setPdfUrl(absolutePdfUrl);
        } else if (completedJob.status === 'error') {
          // Analyze errors for quick fixes
          const errorMessage = completedJob.error || 'Compilation failed';
          const analyzedErrors = errorAnalyzer.analyzeErrors(errorMessage);
          setCompilationError(errorMessage);

          // Display quick fixes by setting Monaco editor markers
          if (analyzedErrors.length > 0 && editorInstance) {
            const model = editorInstance.getModel();
            if (model) {
              const markers = analyzedErrors.map(error => ({
                startLineNumber: error.line,
                startColumn: error.column || 1,
                endLineNumber: error.line,
                endColumn: error.column ? error.column + 10 : 1000,
                message: error.message,
                severity: error.severity === 'error' ? monaco.MarkerSeverity.Error :
                         error.severity === 'warning' ? monaco.MarkerSeverity.Warning :
                         monaco.MarkerSeverity.Info
              }));
              monaco.editor.setModelMarkers(model, 'latex-errors', markers);
            }
          }
        }

        setIsCompiling(false);
      }

    } catch (error) {
      console.error('Compilation error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown compilation error';
      setCompilationError(errorMessage);
      setIsCompiling(false);

      if (currentJob) {
        setCurrentJob({
          ...currentJob,
          status: 'error',
          error: errorMessage,
          updatedAt: new Date(),
        });
      }
    }
  }, [currentJob, lastCompiledContent, useWebSocket, compilationCache, errorAnalyzer, editorInstance]);  // Handle content changes with debounced compilation
  const handleContentChange = useCallback((content: string) => {
    setLatexContent(content);

    // Enable auto-compilation with debouncing
    // Clear existing timeout
    if (compileTimeoutRef.current) {
      clearTimeout(compileTimeoutRef.current);
    }

    // Auto-compile after 2 seconds of inactivity if content has changed significantly
    const shouldAutoCompile = content.includes('\\begin{document}') && content.trim().length > 50;

    if (shouldAutoCompile && content !== lastCompiledContent) {
      // Set up debounced compilation (2 second delay)
      compileTimeoutRef.current = setTimeout(() => {
        handleCompile(content);
      }, 2000);
    }
  }, [handleCompile, lastCompiledContent]);

    // Manual compile trigger
  const handleManualCompile = useCallback(() => {
    const content = editorInstance?.getValue() || '';
    if (content.trim()) {
      handleCompile(content);
    }
  }, [handleCompile, editorInstance]);

  // Clear cache function
  const handleClearCache = useCallback(() => {
    compilationCache.clear();
    setCurrentJob(null);
    setPdfUrl(undefined);
    setLastCompiledContent('');
    console.log('Cache cleared');
  }, [compilationCache]);

  // Download PDF function
  const handleDownloadPDF = useCallback(async () => {
    if (!pdfUrl) {
      alert('No PDF available. Please compile first.');
      return;
    }

    try {
      // Convert relative URL to absolute if needed
      const absolutePdfUrl = pdfUrl.startsWith('http') ? pdfUrl : `http://localhost:8000${pdfUrl}`;

      const response = await fetch(absolutePdfUrl);
      if (!response.ok) throw new Error('Failed to fetch PDF');

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'document.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      console.log('PDF downloaded successfully');
    } catch (error) {
      console.error('Failed to download PDF:', error);
      alert('Failed to download PDF. Please try again.');
    }
  }, [pdfUrl]);

  // Download DOCX function
  const handleDownloadDOCX = useCallback(async () => {
    const content = editorInstance?.getValue() || '';
    if (!content.trim()) {
      alert('No content to convert. Please enter LaTeX content first.');
      return;
    }

    try {
      const response = await fetch('http://localhost:8000/api/latex/convert-to-docx', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          latex_content: content,
          filename: 'document'
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to convert to DOCX: ${response.statusText}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'document.docx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      console.log('DOCX downloaded successfully');
    } catch (error) {
      console.error('Failed to download DOCX:', error);
      alert('Failed to download DOCX. Please try again.');
    }
  }, [editorInstance]);

  // Global keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Command Palette (Ctrl/Cmd + Shift + P)
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'P') {
        e.preventDefault();
        setShowCommandPalette(true);
      }

      // Settings Panel (Ctrl/Cmd + ,)
      if ((e.ctrlKey || e.metaKey) && e.key === ',') {
        e.preventDefault();
        setShowSettingsPanel(true);
      }

      // Manual Compile (Ctrl/Cmd + Enter)
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        handleManualCompile();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleManualCompile]);

  // Status display component
  const StatusBar = () => (
    <div className="status-bar bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-4 py-2 text-sm">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {/* Compilation Status */}
          <div className="flex items-center gap-2">
            <span className="text-gray-600 dark:text-gray-400">Status:</span>
            {isCompiling ? (
              <span className="flex items-center gap-1 text-blue-600">
                <div className="animate-spin w-3 h-3 border border-blue-600 border-t-transparent rounded-full"></div>
                Compiling...
              </span>
            ) : currentJob?.status === 'success' ? (
              <span className="text-green-600">✓ Compiled successfully</span>
            ) : currentJob?.status === 'error' ? (
              <span className="text-red-600">✗ Compilation failed</span>
            ) : (
              <span className="text-gray-600">Ready</span>
            )}
          </div>

          {/* Document Stats */}
          <div className="flex items-center gap-2 text-gray-500">
            <span>Lines: {latexContent.split('\\n').length}</span>
            <span>Characters: {latexContent.length}</span>
          </div>
        </div>

        {/* Manual Compile Button */}
        <button
          onClick={handleManualCompile}
          disabled={isCompiling}
          className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          {isCompiling ? 'Compiling...' : 'Compile'}
        </button>

        {/* Clear Cache Button */}
        <button
          onClick={handleClearCache}
          className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
          title="Clear compilation cache"
        >
          Clear Cache
        </button>

        {/* Download Buttons */}
        <div className="flex items-center gap-2">
          <button
            onClick={handleDownloadPDF}
            disabled={!pdfUrl}
            className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            title="Download as PDF"
          >
            📄 PDF
          </button>

          <button
            onClick={handleDownloadDOCX}
            disabled={isCompiling}
            className="px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            title="Download as DOCX"
          >
            📝 DOCX
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className={`latex-editor-layout h-full flex flex-col ${className}`}>
      {/* Header Bar */}
      <div className="header-bar bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
        <div className="flex items-center justify-between">
          <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
            LaTeX Editor
          </h1>

          <div className="flex items-center gap-2">
            {/* Connection Status */}
            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-500">Connection:</span>
              <div className={`w-2 h-2 rounded-full ${
                connectionStatus === 'connected' ? 'bg-green-500' :
                connectionStatus === 'error' ? 'bg-red-500' : 'bg-gray-400'
              }`}></div>
              <span className="text-xs text-gray-500 capitalize">{connectionStatus}</span>
            </div>

            {/* View Options */}
            <button
              onClick={() => setShowTemplates(!showTemplates)}
              className="px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors text-sm"
            >
              {showTemplates ? 'Hide Templates' : 'Templates'}
            </button>

            <button
              onClick={() => setPdfUrl(undefined)}
              className="px-2 py-1 text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
              title="Clear PDF"
            >
              Clear PDF
            </button>

            <div className="text-gray-300 dark:text-gray-600">|</div>

            <span className="text-sm text-gray-600 dark:text-gray-400">
              Real-time compilation enabled
            </span>
          </div>

          {/* Syntax Validation Status */}
          <div className="flex items-center gap-2">
            <span className="text-gray-600 dark:text-gray-400">Syntax:</span>
            {syntaxErrors.length === 0 ? (
              <Badge variant="secondary" className="flex items-center gap-1 bg-green-100 text-green-800">
                <CheckCircle className="w-3 h-3" />
                Valid
              </Badge>
            ) : (
              <div className="flex items-center gap-2">
                {syntaxErrors.some(e => e.severity === 'error') && (
                  <Badge variant="destructive" className="flex items-center gap-1">
                    <AlertCircle className="w-3 h-3" />
                    {syntaxErrors.filter(e => e.severity === 'error').length} Errors
                  </Badge>
                )}
                {syntaxErrors.some(e => e.severity === 'warning') && (
                  <Badge variant="secondary" className="flex items-center gap-1 bg-orange-100 text-orange-800">
                    <AlertCircle className="w-3 h-3" />
                    {syntaxErrors.filter(e => e.severity === 'warning').length} Warnings
                  </Badge>
                )}
                {syntaxErrors.some(e => e.severity === 'info') && (
                  <Badge variant="secondary" className="flex items-center gap-1 bg-blue-100 text-blue-800">
                    <Info className="w-3 h-3" />
                    {syntaxErrors.filter(e => e.severity === 'info').length} Info
                  </Badge>
                )}
              </div>
            )}
          </div>

          {/* Editor Theme Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setEditorTheme(editorTheme === 'latex-theme' ? 'latex-dark-theme' : 'latex-theme')}
            className="flex items-center gap-1"
          >
            <Palette className="w-4 h-4" />
            {editorTheme === 'latex-theme' ? 'Dark Theme' : 'Light Theme'}
          </Button>

          {/* Error Panel Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowErrorPanel(!showErrorPanel)}
            className="flex items-center gap-1"
          >
            {showErrorPanel ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            {showErrorPanel ? 'Hide Errors' : 'Show Errors'}
          </Button>

          {/* Command Palette Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowCommandPalette(true)}
            className="flex items-center gap-1"
            title="Command Palette (Ctrl+Shift+P)"
          >
            <Command className="w-4 h-4" />
            Commands
          </Button>

          {/* Settings Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowSettingsPanel(true)}
            className="flex items-center gap-1"
            title="Settings (Ctrl+,)"
          >
            <Settings className="w-4 h-4" />
            Settings
          </Button>
        </div>

        {/* Editor Options */}
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowTemplates(!showTemplates)}
            className="flex items-center gap-2"
          >
            <FileText className="w-4 h-4" />
            Templates
          </Button>
        </div>
      </div>

      {/* Error Panel */}
      {showErrorPanel && syntaxErrors.length > 0 && (
        <div className="bg-red-50 dark:bg-red-900/20 border-t border-red-200 dark:border-red-800 p-3">
          <div className="max-h-32 overflow-y-auto space-y-1">
            {syntaxErrors.slice(0, 5).map((error, index) => (
              <div key={index} className="flex items-center gap-2 text-sm">
                {error.severity === 'error' && <AlertCircle className="w-3 h-3 text-red-500" />}
                {error.severity === 'warning' && <AlertCircle className="w-3 h-3 text-orange-500" />}
                {error.severity === 'info' && <Info className="w-3 h-3 text-blue-500" />}
                <span className="text-gray-600 dark:text-gray-400">
                  Line {error.line}, Col {error.column}:
                </span>
                <span className="text-gray-900 dark:text-gray-100">{error.message}</span>
              </div>
            ))}
            {syntaxErrors.length > 5 && (
              <div className="text-sm text-gray-500 dark:text-gray-400">
                ... and {syntaxErrors.length - 5} more errors
              </div>
            )}
          </div>
        </div>
      )}

      {/* Template Panel */}
      {showTemplates && (
        <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 p-4 max-h-96 overflow-y-auto">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Choose a Template
            </h3>
            <button
              onClick={() => setShowTemplates(false)}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <TemplateSelector
            onTemplateSelect={handleTemplateSelect}
            selectedTemplateId={selectedTemplate?.metadata.id as string}
            className="max-h-80 overflow-y-auto"
          />
        </div>
      )}

      {/* Main Content Area */}
      <div className="flex-1 min-h-0">
        <PanelGroup direction={orientation === 'horizontal' ? 'horizontal' : 'vertical'}>
          {/* Editor Panel */}
          <Panel defaultSize={defaultSizes[0]} minSize={minSizes[0]}>
            <div className="h-full flex flex-col">
              {/* File Management Bar */}
              <div className="file-management bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-3 py-2">
                <FileManager
                  currentContent={latexContent}
                  onLoad={setLatexContent}
                  className="w-full"
                />
              </div>

              {/* Editor Toolbar */}
              <EditorToolbar
                onInsertCommand={(command) => {
                  if (editorInstance) {
                    const selection = editorInstance.getSelection();
                    if (selection) {
                      const id = { major: 1, minor: 1 };
                      const op = { identifier: id, range: selection, text: command, forceMoveMarkers: true };
                      editorInstance.executeEdits("my-source", [op]);
                    }
                  }
                }}
                onInsertSnippet={(snippet) => {
                  if (editorInstance) {
                    // Use Monaco Editor's proper snippet insertion
                    const position = editorInstance.getPosition();
                    if (position) {
                      editorInstance.focus();

                      // For now, we'll insert the snippet as plain text with placeholders replaced
                      // In the future, we can implement proper snippet support with tabstops
                      let processedSnippet = snippet;

                      // Replace ${1:text} with text and prepare for selection
                      const placeholderRegex = /\$\{(\d+):([^}]+)\}/g;
                      let firstPlaceholder: { text: string; index: number } | null = null;

                      processedSnippet = processedSnippet.replace(placeholderRegex, (match, index, text) => {
                        if (!firstPlaceholder && index === '1') {
                          firstPlaceholder = { text, index: parseInt(index) };
                        }
                        return text;
                      });

                      // Insert the processed snippet
                      editorInstance.executeEdits('insertSnippet', [{
                        range: {
                          startLineNumber: position.lineNumber,
                          startColumn: position.column,
                          endLineNumber: position.lineNumber,
                          endColumn: position.column
                        },
                        text: processedSnippet,
                        forceMoveMarkers: true
                      }]);

                      // If there was a first placeholder, select it
                      if (firstPlaceholder) {
                        const placeholder = firstPlaceholder as { text: string; index: number };
                        const newPosition = editorInstance.getPosition();
                        if (newPosition) {
                          let lineOffset = 0;
                          let columnOffset = 0;

                          // Find where the first placeholder text appears
                          const searchText = placeholder.text;
                          const snippetIndex = processedSnippet.indexOf(searchText);

                          if (snippetIndex !== -1) {
                            const beforePlaceholder = processedSnippet.substring(0, snippetIndex);
                            const newlines = beforePlaceholder.split('\n');
                            lineOffset = newlines.length - 1;
                            columnOffset = lineOffset > 0 ? newlines[newlines.length - 1].length : beforePlaceholder.length;

                            const startLine = position.lineNumber + lineOffset;
                            const startCol = lineOffset > 0 ? columnOffset + 1 : position.column + columnOffset;

                            editorInstance.setSelection({
                              startLineNumber: startLine,
                              startColumn: startCol,
                              endLineNumber: startLine,
                              endColumn: startCol + searchText.length
                            });
                          }
                        }
                      }
                    }
                  }
                }}
              />

              {/* Editor */}
              <div className="flex-1">
                <LaTeXEditor
                  initialContent={latexContent}
                  onChange={handleContentChange}
                  theme={editorTheme === 'latex-dark-theme' ? 'vs-dark' : 'vs-light'}
                  onEditorReady={(instance) => {
                    setEditorInstance(instance);
                    // Set up syntax validator for the main layout
                    if (!syntaxValidator) {
                      const validator = new LatexSyntaxValidator(instance);
                      setSyntaxValidator(validator);
                    }
                  }}
                  {...editorProps}
                />
              </div>
            </div>
          </Panel>

          {/* Resize Handle */}
          <PanelResizeHandle className="w-2 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors cursor-col-resize" />

          {/* PDF Viewer Panel */}
          <Panel defaultSize={defaultSizes[1]} minSize={minSizes[1]}>
            <div className="h-full flex flex-col">
              {/* PDF Toolbar */}
              <div className="pdf-toolbar bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-3 py-2">
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                  <span>PDF Preview</span>
                  {currentJob && (
                    <span className="ml-2">
                      Last updated: {currentJob.updatedAt.toLocaleTimeString()}
                    </span>
                  )}
                </div>
              </div>

              {/* PDF Viewer */}
              <div className="flex-1">
                <PDFViewer
                  pdfUrl={pdfUrl}
                  isLoading={isCompiling}
                  error={compilationError}
                  onPDFLoad={(numPages) => console.log(`PDF loaded with ${numPages} pages`)}
                  onPageChange={(page) => console.log(`Changed to page ${page}`)}
                  {...pdfViewerProps}
                />
              </div>
            </div>
          </Panel>
        </PanelGroup>
      </div>

      {/* Status Bar */}
      <StatusBar />

      {/* Command Palette */}
      <CommandPalette
        isOpen={showCommandPalette}
        onClose={() => setShowCommandPalette(false)}
        editor={editorInstance}
      />

      {/* Settings Panel */}
      <SettingsPanel
        isOpen={showSettingsPanel}
        onClose={() => setShowSettingsPanel(false)}
        editor={editorInstance}
        settings={editorSettings}
        onSettingsChange={setEditorSettings}
      />
    </div>
  );
};

export default LaTeXEditorLayout;
