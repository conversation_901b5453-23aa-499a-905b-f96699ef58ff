'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { PDFViewerProps } from './types';
import type { PDFDocumentProxy, PDFPageProxy } from 'pdfjs-dist';

// Dynamic import to avoid SSR issues
let pdfjs: typeof import('pdfjs-dist') | null = null;

const PDFViewer: React.FC<PDFViewerProps> = ({
  pdfUrl,
  pdfData,
  isLoading = false,
  error,
  className = '',
  height = '100%',
  showZoomControls = true,
  showNavigation = true,
  initialZoom = 1.0,
  onPDFLoad,
  onPageChange
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [pdf, setPdf] = useState<PDFDocumentProxy | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [scale, setScale] = useState(initialZoom);
  const [isRendering, setIsRendering] = useState(false);
  const [loadingError, setLoadingError] = useState<string | null>(null);

  // Initialize PDF.js
  useEffect(() => {
    const initPDFJS = async () => {
      try {
        const pdfjsLib = await import('pdfjs-dist');
        pdfjs = pdfjsLib;

        // Use local worker to avoid CORS issues
        pdfjs.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js';

        console.log('PDF.js initialized with local worker');
      } catch (err) {
        console.error('Failed to load PDF.js:', err);
        setLoadingError('Failed to load PDF viewer');
      }
    };

    initPDFJS();
  }, []);  // Load PDF
  useEffect(() => {
    const loadPDF = async () => {
      if (!pdfjs || (!pdfUrl && !pdfData) || isLoading) {
        return;
      }

      setLoadingError(null);

      try {
        let loadingTask;

        if (pdfData) {
          loadingTask = pdfjs.getDocument({ data: pdfData });
        } else if (pdfUrl) {
          loadingTask = pdfjs.getDocument(pdfUrl);
        } else {
          return;
        }

        const pdfDocument = await loadingTask.promise;
        setPdf(pdfDocument);
        setTotalPages(pdfDocument.numPages);
        setCurrentPage(1);

        if (onPDFLoad) {
          onPDFLoad(pdfDocument.numPages);
        }

      } catch (err) {
        console.error('Error loading PDF:', err);
        setLoadingError('Failed to load PDF document');
        setPdf(null);
      }
    };

    loadPDF();
  }, [pdfUrl, pdfData, isLoading, onPDFLoad]);

  // Render page
  const renderPage = useCallback(async (pageNumber: number) => {
    if (!pdf || !canvasRef.current || isRendering) {
      return;
    }

    setIsRendering(true);

    try {
      const page = await pdf.getPage(pageNumber);
      const viewport = page.getViewport({ scale });

      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');

      if (!context) {
        throw new Error('Could not get canvas context');
      }

      canvas.height = viewport.height;
      canvas.width = viewport.width;

      // Update container size
      if (containerRef.current) {
        containerRef.current.style.width = `${viewport.width}px`;
        containerRef.current.style.height = `${viewport.height}px`;
      }

      const renderContext = {
        canvasContext: context,
        viewport: viewport,
        canvas: canvas,
      };

      await page.render(renderContext).promise;

    } catch (err) {
      console.error('Error rendering page:', err);
      setLoadingError('Failed to render PDF page');
    } finally {
      setIsRendering(false);
    }
  }, [pdf, scale, isRendering]);

  // Render page when dependencies change
  useEffect(() => {
    if (pdf && currentPage) {
      renderPage(currentPage);
    }
  }, [pdf, currentPage, scale, renderPage]);

  // Navigation functions
  const goToPreviousPage = () => {
    if (currentPage > 1) {
      const newPage = currentPage - 1;
      setCurrentPage(newPage);
      if (onPageChange) {
        onPageChange(newPage);
      }
    }
  };

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      const newPage = currentPage + 1;
      setCurrentPage(newPage);
      if (onPageChange) {
        onPageChange(newPage);
      }
    }
  };

  const goToPage = (pageNumber: number) => {
    if (pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
      if (onPageChange) {
        onPageChange(pageNumber);
      }
    }
  };

  // Zoom functions
  const zoomIn = () => {
    setScale(prev => Math.min(prev * 1.25, 5.0));
  };

  const zoomOut = () => {
    setScale(prev => Math.max(prev / 1.25, 0.25));
  };

  const fitToWidth = () => {
    if (containerRef.current && pdf) {
      const containerWidth = containerRef.current.parentElement?.clientWidth || 800;
      pdf.getPage(currentPage).then((page: PDFPageProxy) => {
        const viewport = page.getViewport({ scale: 1.0 });
        const newScale = (containerWidth - 40) / viewport.width; // 40px for padding
        setScale(newScale);
      });
    }
  };

  const resetZoom = () => {
    setScale(1.0);
  };

  // Handle page input
  const handlePageInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const target = e.target as HTMLInputElement;
      const pageNumber = parseInt(target.value, 10);
      if (!isNaN(pageNumber)) {
        goToPage(pageNumber);
      }
    }
  };

  if (loadingError || error) {
    return (
      <div className={`pdf-viewer ${className} flex items-center justify-center`} style={{ height }}>
        <div className="text-center">
          <div className="text-red-500 text-lg mb-2">⚠️ PDF Viewer Error</div>
          <div className="text-gray-600">{loadingError || error}</div>
        </div>
      </div>
    );
  }

  if (isLoading || !pdf) {
    return (
      <div className={`pdf-viewer ${className} flex items-center justify-center`} style={{ height }}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
          <div className="text-gray-600">Loading PDF...</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`pdf-viewer ${className} flex flex-col`} style={{ height }}>
      {/* Toolbar */}
      <div className="pdf-toolbar bg-gray-100 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-2 flex items-center justify-between flex-wrap gap-2">
        {/* Navigation Controls */}
        {showNavigation && (
          <div className="flex items-center gap-2">
            <button
              onClick={goToPreviousPage}
              disabled={currentPage <= 1}
              className="px-3 py-1 bg-blue-500 text-white rounded disabled:bg-gray-300 disabled:cursor-not-allowed hover:bg-blue-600 transition-colors"
              title="Previous Page"
            >
              ◀
            </button>

            <div className="flex items-center gap-1">
              <input
                type="number"
                min="1"
                max={totalPages}
                value={currentPage}
                onChange={(e) => setCurrentPage(parseInt(e.target.value, 10) || 1)}
                onKeyDown={handlePageInput}
                className="w-12 px-1 py-1 text-center border border-gray-300 rounded text-sm"
              />
              <span className="text-sm text-gray-600">of {totalPages}</span>
            </div>

            <button
              onClick={goToNextPage}
              disabled={currentPage >= totalPages}
              className="px-3 py-1 bg-blue-500 text-white rounded disabled:bg-gray-300 disabled:cursor-not-allowed hover:bg-blue-600 transition-colors"
              title="Next Page"
            >
              ▶
            </button>
          </div>
        )}

        {/* Zoom Controls */}
        {showZoomControls && (
          <div className="flex items-center gap-2">
            <button
              onClick={zoomOut}
              className="px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
              title="Zoom Out"
            >
              −
            </button>

            <span className="text-sm text-gray-600 min-w-12 text-center">
              {Math.round(scale * 100)}%
            </span>

            <button
              onClick={zoomIn}
              className="px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
              title="Zoom In"
            >
              +
            </button>

            <button
              onClick={fitToWidth}
              className="px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors text-xs"
              title="Fit to Width"
            >
              Fit
            </button>

            <button
              onClick={resetZoom}
              className="px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors text-xs"
              title="Reset Zoom"
            >
              100%
            </button>
          </div>
        )}
      </div>

      {/* PDF Canvas Container */}
      <div className="pdf-canvas-container flex-1 overflow-auto bg-gray-50 dark:bg-gray-900 flex items-start justify-center p-4">
        <div
          ref={containerRef}
          className="pdf-page-container bg-white shadow-lg"
          style={{ display: isRendering ? 'none' : 'block' }}
        >
          <canvas
            ref={canvasRef}
            className="pdf-canvas"
            style={{ display: 'block', maxWidth: '100%', height: 'auto' }}
          />
        </div>

        {isRendering && (
          <div className="flex items-center justify-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PDFViewer;
