/**
 * Advanced Settings Panel for LaTeX Editor
 * Provides comprehensive customization options
 */

"use client";

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../ui/dialog';
import { ScrollArea } from '../ui/scroll-area';
import * as monaco from 'monaco-editor';

interface EditorSettings {
  // Editor behavior
  fontSize: number;
  fontFamily: string;
  tabSize: number;
  wordWrap: 'on' | 'off' | 'wordWrapColumn' | 'bounded';
  lineNumbers: 'on' | 'off' | 'relative' | 'interval';
  minimap: boolean;
  folding: boolean;

  // LaTeX-specific
  autoComplete: boolean;
  syntaxHighlighting: boolean;
  errorChecking: boolean;
  autoSave: boolean;
  autoSaveDelay: number;

  // Compilation
  autoCompile: boolean;
  compileDelay: number;
  showCompileProgress: boolean;

  // UI
  theme: 'vs' | 'vs-dark' | 'hc-black';
  showInlayHints: boolean;
  bracketPairColorization: boolean;
  renderWhitespace: 'none' | 'boundary' | 'selection' | 'trailing' | 'all';

  // Advanced
  enableExperimentalFeatures: boolean;
  cacheSize: number;
  maxHistoryItems: number;
}

interface SettingsPanelProps {
  isOpen: boolean;
  onClose: () => void;
  editor: monaco.editor.IStandaloneCodeEditor | null;
  settings: EditorSettings;
  onSettingsChange: (settings: EditorSettings) => void;
}

interface SettingGroupProps {
  title: string;
  children: React.ReactNode;
}

const SettingGroup: React.FC<SettingGroupProps> = ({ title, children }) => (
  <div className="mb-6">
    <h3 className="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
      {title}
    </h3>
    <div className="space-y-4">
      {children}
    </div>
  </div>
);

interface SettingItemProps {
  label: string;
  description?: string;
  children: React.ReactNode;
}

const SettingItem: React.FC<SettingItemProps> = ({ label, description, children }) => (
  <div className="flex items-center justify-between py-2">
    <div className="flex-1">
      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
        {label}
      </label>
      {description && (
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          {description}
        </p>
      )}
    </div>
    <div className="ml-4">
      {children}
    </div>
  </div>
);

const defaultSettings: EditorSettings = {
  fontSize: 14,
  fontFamily: "'JetBrains Mono', 'Fira Code', 'Consolas', monospace",
  tabSize: 2,
  wordWrap: 'on',
  lineNumbers: 'on',
  minimap: true,
  folding: true,
  autoComplete: true,
  syntaxHighlighting: true,
  errorChecking: true,
  autoSave: true,
  autoSaveDelay: 2000,
  autoCompile: false,
  compileDelay: 1000,
  showCompileProgress: true,
  theme: 'vs-dark',
  showInlayHints: true,
  bracketPairColorization: true,
  renderWhitespace: 'boundary',
  enableExperimentalFeatures: false,
  cacheSize: 100,
  maxHistoryItems: 50
};

const SettingsPanel: React.FC<SettingsPanelProps> = ({
  isOpen,
  onClose,
  editor,
  settings,
  onSettingsChange
}) => {
  const [localSettings, setLocalSettings] = useState<EditorSettings>(settings);
  const [hasChanges, setHasChanges] = useState(false);

  // Update local settings when props change
  useEffect(() => {
    setLocalSettings(settings);
    setHasChanges(false);
  }, [settings]);

  // Check for changes
  useEffect(() => {
    const changed = JSON.stringify(localSettings) !== JSON.stringify(settings);
    setHasChanges(changed);
  }, [localSettings, settings]);

  const updateSetting = <K extends keyof EditorSettings>(
    key: K,
    value: EditorSettings[K]
  ) => {
    setLocalSettings(prev => ({ ...prev, [key]: value }));
  };

  const applySettings = () => {
    onSettingsChange(localSettings);

    // Apply Monaco Editor settings
    if (editor) {
      editor.updateOptions({
        fontSize: localSettings.fontSize,
        fontFamily: localSettings.fontFamily,
        tabSize: localSettings.tabSize,
        wordWrap: localSettings.wordWrap,
        lineNumbers: localSettings.lineNumbers,
        minimap: { enabled: localSettings.minimap },
        folding: localSettings.folding,
        theme: localSettings.theme,
        inlayHints: { enabled: localSettings.showInlayHints ? 'on' : 'off' },
        bracketPairColorization: { enabled: localSettings.bracketPairColorization },
        renderWhitespace: localSettings.renderWhitespace
      });
    }

    onClose();
  };

  const resetToDefaults = () => {
    setLocalSettings(defaultSettings);
  };

  const exportSettings = () => {
    const dataStr = JSON.stringify(localSettings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'latex-editor-settings.json';
    link.click();
    URL.revokeObjectURL(url);
  };

  const importSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const imported = JSON.parse(e.target?.result as string);
        setLocalSettings({ ...defaultSettings, ...imported });
      } catch (error) {
        console.error('Failed to import settings:', error);
        alert('Invalid settings file');
      }
    };
    reader.readAsText(file);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">Editor Settings</DialogTitle>
        </DialogHeader>

        <ScrollArea className="flex-1 px-6">
          <div className="space-y-6">
            {/* Editor Appearance */}
            <SettingGroup title="Appearance">
              <SettingItem
                label="Theme"
                description="Choose editor color scheme"
              >
                <select
                  value={localSettings.theme}
                  onChange={(e) => updateSetting('theme', e.target.value as EditorSettings['theme'])}
                  className="px-3 py-1 border rounded dark:bg-gray-700 dark:border-gray-600"
                >
                  <option value="vs">Light</option>
                  <option value="vs-dark">Dark</option>
                  <option value="hc-black">High Contrast</option>
                </select>
              </SettingItem>

              <SettingItem
                label="Font Size"
                description="Editor font size in pixels"
              >
                <input
                  type="range"
                  min="10"
                  max="24"
                  value={localSettings.fontSize}
                  onChange={(e) => updateSetting('fontSize', parseInt(e.target.value))}
                  className="w-20"
                />
                <span className="ml-2 text-sm w-8">{localSettings.fontSize}px</span>
              </SettingItem>

              <SettingItem
                label="Font Family"
                description="Monospace font for code editing"
              >
                <input
                  type="text"
                  value={localSettings.fontFamily}
                  onChange={(e) => updateSetting('fontFamily', e.target.value)}
                  className="px-3 py-1 border rounded w-64 dark:bg-gray-700 dark:border-gray-600"
                />
              </SettingItem>

              <SettingItem
                label="Line Numbers"
                description="Show line numbers in editor"
              >
                <select
                  value={localSettings.lineNumbers}
                  onChange={(e) => updateSetting('lineNumbers', e.target.value as EditorSettings['lineNumbers'])}
                  className="px-3 py-1 border rounded dark:bg-gray-700 dark:border-gray-600"
                >
                  <option value="on">On</option>
                  <option value="off">Off</option>
                  <option value="relative">Relative</option>
                  <option value="interval">Interval</option>
                </select>
              </SettingItem>

              <SettingItem
                label="Minimap"
                description="Show minimap overview"
              >
                <input
                  type="checkbox"
                  checked={localSettings.minimap}
                  onChange={(e) => updateSetting('minimap', e.target.checked)}
                  className="h-4 w-4"
                />
              </SettingItem>

              <SettingItem
                label="Word Wrap"
                description="Wrap long lines"
              >
                <select
                  value={localSettings.wordWrap}
                  onChange={(e) => updateSetting('wordWrap', e.target.value as EditorSettings['wordWrap'])}
                  className="px-3 py-1 border rounded dark:bg-gray-700 dark:border-gray-600"
                >
                  <option value="on">On</option>
                  <option value="off">Off</option>
                  <option value="wordWrapColumn">Word Wrap Column</option>
                  <option value="bounded">Bounded</option>
                </select>
              </SettingItem>
            </SettingGroup>

            {/* Editor Behavior */}
            <SettingGroup title="Behavior">
              <SettingItem
                label="Tab Size"
                description="Number of spaces for tab indentation"
              >
                <input
                  type="range"
                  min="1"
                  max="8"
                  value={localSettings.tabSize}
                  onChange={(e) => updateSetting('tabSize', parseInt(e.target.value))}
                  className="w-20"
                />
                <span className="ml-2 text-sm w-8">{localSettings.tabSize}</span>
              </SettingItem>

              <SettingItem
                label="Code Folding"
                description="Allow collapsing code sections"
              >
                <input
                  type="checkbox"
                  checked={localSettings.folding}
                  onChange={(e) => updateSetting('folding', e.target.checked)}
                  className="h-4 w-4"
                />
              </SettingItem>

              <SettingItem
                label="Bracket Colorization"
                description="Color matching brackets"
              >
                <input
                  type="checkbox"
                  checked={localSettings.bracketPairColorization}
                  onChange={(e) => updateSetting('bracketPairColorization', e.target.checked)}
                  className="h-4 w-4"
                />
              </SettingItem>

              <SettingItem
                label="Show Whitespace"
                description="Render whitespace characters"
              >
                <select
                  value={localSettings.renderWhitespace}
                  onChange={(e) => updateSetting('renderWhitespace', e.target.value as EditorSettings['renderWhitespace'])}
                  className="px-3 py-1 border rounded dark:bg-gray-700 dark:border-gray-600"
                >
                  <option value="none">None</option>
                  <option value="boundary">Boundary</option>
                  <option value="selection">Selection</option>
                  <option value="trailing">Trailing</option>
                  <option value="all">All</option>
                </select>
              </SettingItem>
            </SettingGroup>

            {/* LaTeX Features */}
            <SettingGroup title="LaTeX Features">
              <SettingItem
                label="Auto Complete"
                description="Enable intelligent code completion"
              >
                <input
                  type="checkbox"
                  checked={localSettings.autoComplete}
                  onChange={(e) => updateSetting('autoComplete', e.target.checked)}
                  className="h-4 w-4"
                />
              </SettingItem>

              <SettingItem
                label="Syntax Highlighting"
                description="Highlight LaTeX syntax"
              >
                <input
                  type="checkbox"
                  checked={localSettings.syntaxHighlighting}
                  onChange={(e) => updateSetting('syntaxHighlighting', e.target.checked)}
                  className="h-4 w-4"
                />
              </SettingItem>

              <SettingItem
                label="Error Checking"
                description="Real-time error detection"
              >
                <input
                  type="checkbox"
                  checked={localSettings.errorChecking}
                  onChange={(e) => updateSetting('errorChecking', e.target.checked)}
                  className="h-4 w-4"
                />
              </SettingItem>

              <SettingItem
                label="Auto Save"
                description="Automatically save changes"
              >
                <input
                  type="checkbox"
                  checked={localSettings.autoSave}
                  onChange={(e) => updateSetting('autoSave', e.target.checked)}
                  className="h-4 w-4"
                />
              </SettingItem>

              {localSettings.autoSave && (
                <SettingItem
                  label="Auto Save Delay"
                  description="Delay in milliseconds"
                >
                  <input
                    type="range"
                    min="500"
                    max="10000"
                    step="500"
                    value={localSettings.autoSaveDelay}
                    onChange={(e) => updateSetting('autoSaveDelay', parseInt(e.target.value))}
                    className="w-32"
                  />
                  <span className="ml-2 text-sm w-16">{localSettings.autoSaveDelay}ms</span>
                </SettingItem>
              )}
            </SettingGroup>

            {/* Compilation */}
            <SettingGroup title="Compilation">
              <SettingItem
                label="Auto Compile"
                description="Automatically compile on changes"
              >
                <input
                  type="checkbox"
                  checked={localSettings.autoCompile}
                  onChange={(e) => updateSetting('autoCompile', e.target.checked)}
                  className="h-4 w-4"
                />
              </SettingItem>

              {localSettings.autoCompile && (
                <SettingItem
                  label="Compile Delay"
                  description="Delay before auto-compile"
                >
                  <input
                    type="range"
                    min="500"
                    max="5000"
                    step="250"
                    value={localSettings.compileDelay}
                    onChange={(e) => updateSetting('compileDelay', parseInt(e.target.value))}
                    className="w-32"
                  />
                  <span className="ml-2 text-sm w-16">{localSettings.compileDelay}ms</span>
                </SettingItem>
              )}

              <SettingItem
                label="Show Progress"
                description="Display compilation progress"
              >
                <input
                  type="checkbox"
                  checked={localSettings.showCompileProgress}
                  onChange={(e) => updateSetting('showCompileProgress', e.target.checked)}
                  className="h-4 w-4"
                />
              </SettingItem>
            </SettingGroup>

            {/* Advanced */}
            <SettingGroup title="Advanced">
              <SettingItem
                label="Cache Size"
                description="Number of compiled documents to cache"
              >
                <input
                  type="range"
                  min="10"
                  max="200"
                  step="10"
                  value={localSettings.cacheSize}
                  onChange={(e) => updateSetting('cacheSize', parseInt(e.target.value))}
                  className="w-32"
                />
                <span className="ml-2 text-sm w-12">{localSettings.cacheSize}</span>
              </SettingItem>

              <SettingItem
                label="History Items"
                description="Maximum undo/redo history"
              >
                <input
                  type="range"
                  min="10"
                  max="100"
                  step="5"
                  value={localSettings.maxHistoryItems}
                  onChange={(e) => updateSetting('maxHistoryItems', parseInt(e.target.value))}
                  className="w-32"
                />
                <span className="ml-2 text-sm w-12">{localSettings.maxHistoryItems}</span>
              </SettingItem>

              <SettingItem
                label="Experimental Features"
                description="Enable experimental features (may be unstable)"
              >
                <input
                  type="checkbox"
                  checked={localSettings.enableExperimentalFeatures}
                  onChange={(e) => updateSetting('enableExperimentalFeatures', e.target.checked)}
                  className="h-4 w-4"
                />
              </SettingItem>
            </SettingGroup>
          </div>
        </ScrollArea>

        {/* Actions */}
        <div className="flex justify-between items-center p-6 border-t">
          <div className="flex space-x-2">
            <button
              onClick={resetToDefaults}
              className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
            >
              Reset to Defaults
            </button>
            <button
              onClick={exportSettings}
              className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
            >
              Export Settings
            </button>
            <label className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 cursor-pointer">
              Import Settings
              <input
                type="file"
                accept=".json"
                onChange={importSettings}
                className="hidden"
              />
            </label>
          </div>

          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
            >
              Cancel
            </button>
            <button
              onClick={applySettings}
              disabled={!hasChanges}
              className={`px-6 py-2 rounded-md font-medium ${
                hasChanges
                  ? 'bg-blue-500 text-white hover:bg-blue-600'
                  : 'bg-gray-200 text-gray-500 cursor-not-allowed dark:bg-gray-700 dark:text-gray-400'
              }`}
            >
              Apply Settings
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export { defaultSettings, type EditorSettings };
export default SettingsPanel;
