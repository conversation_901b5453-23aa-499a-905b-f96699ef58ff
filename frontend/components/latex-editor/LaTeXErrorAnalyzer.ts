/**
 * Advanced LaTeX Error Analyzer and Quick Fix Provider
 * Provides intelligent error detection and automated fixes
 */

import * as monaco from 'monaco-editor';

export interface LaTeXError {
  line: number;
  column: number;
  message: string;
  severity: 'error' | 'warning' | 'info';
  category: string;
  quickFixes?: QuickFix[];
  relatedLines?: number[];
}

export interface QuickFix {
  title: string;
  description: string;
  edits: monaco.editor.IIdentifiedSingleEditOperation[];
  kind: 'quickfix' | 'refactor' | 'source';
}

export class LaTeXErrorAnalyzer {
  private commonErrors = [
    {
      pattern: /Missing \\begin\{([^}]+)\}/i,
      category: 'environment',
      severity: 'error' as const,
      getQuickFixes: (match: RegExpMatchArray, line: number): QuickFix[] => [{
        title: `Add \\begin{${match[1]}}`,
        description: `Insert the missing \\begin{${match[1]}} command`,
        edits: [{
          range: new monaco.Range(line, 1, line, 1),
          text: `\\begin{${match[1]}}\n`
        }],
        kind: 'quickfix'
      }]
    },
    {
      pattern: /Missing \\end\{([^}]+)\}/i,
      category: 'environment',
      severity: 'error' as const,
      getQuickFixes: (match: RegExpMatchArray, line: number): QuickFix[] => [{
        title: `Add \\end{${match[1]}}`,
        description: `Insert the missing \\end{${match[1]}} command`,
        edits: [{
          range: new monaco.Range(line, 1000, line, 1000),
          text: `\n\\end{${match[1]}}`
        }],
        kind: 'quickfix'
      }]
    },
    {
      pattern: /Undefined control sequence \\([a-zA-Z]+)/i,
      category: 'command',
      severity: 'error' as const,
      getQuickFixes: (match: RegExpMatchArray, line: number): QuickFix[] => {
        const command = match[1];
        const suggestions = this.getCommandSuggestions(command);

        return suggestions.map(suggestion => ({
          title: `Replace with \\${suggestion.command}`,
          description: suggestion.description,
          edits: [{
            range: new monaco.Range(line, 1, line, 1000),
            text: match.input!.replace(`\\${command}`, `\\${suggestion.command}`)
          }],
          kind: 'quickfix'
        }));
      }
    },
    {
      pattern: /Package ([a-zA-Z]+) Error:/i,
      category: 'package',
      severity: 'error' as const,
      getQuickFixes: (match: RegExpMatchArray): QuickFix[] => [{
        title: `Add \\usepackage{${match[1]}}`,
        description: `Add the required package to the document preamble`,
        edits: [{
          range: new monaco.Range(1, 1000, 1, 1000),
          text: `\n\\usepackage{${match[1]}}`
        }],
        kind: 'quickfix'
      }]
    },
    {
      pattern: /Missing \$ inserted/i,
      category: 'math',
      severity: 'error' as const,
      getQuickFixes: (match: RegExpMatchArray, line: number): QuickFix[] => [{
        title: 'Wrap in math mode',
        description: 'Add $ symbols to enter math mode',
        edits: [{
          range: new monaco.Range(line, 1, line, 1),
          text: '$'
        }, {
          range: new monaco.Range(line, 1000, line, 1000),
          text: '$'
        }],
        kind: 'quickfix'
      }]
    },
    {
      pattern: /Overfull \\hbox/i,
      category: 'formatting',
      severity: 'warning' as const,
      getQuickFixes: (_match: RegExpMatchArray, _line: number): QuickFix[] => [{
        title: 'Add line break',
        description: 'Insert a line break to fix overfull box',
        edits: [{
          range: new monaco.Range(_line, 1000, _line, 1000),
          text: '\n'
        }],
        kind: 'quickfix'
      }]
    }
  ];

  private commandSuggestions = new Map([
    ['textbold', { command: 'textbf', description: 'Bold text formatting' }],
    ['textitalic', { command: 'textit', description: 'Italic text formatting' }],
    ['center', { command: 'centering', description: 'Center alignment' }],
    ['begin', { command: 'begin', description: 'Begin environment' }],
    ['itemise', { command: 'itemize', description: 'Itemized list' }],
    ['equation', { command: 'equation', description: 'Numbered equation' }],
    ['figure', { command: 'figure', description: 'Figure environment' }],
    ['table', { command: 'table', description: 'Table environment' }]
  ]);

  /**
   * Analyze compilation logs for errors and generate quick fixes
   */
  analyzeErrors(logs: string): LaTeXError[] {
    const errors: LaTeXError[] = [];
    const lines = logs.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Extract line number from LaTeX error format
      const lineMatch = line.match(/l\.(\d+)/);
      const lineNumber = lineMatch ? parseInt(lineMatch[1]) : i + 1;

      // Check against known error patterns
      for (const errorPattern of this.commonErrors) {
        const match = line.match(errorPattern.pattern);
        if (match) {
          const quickFixes = errorPattern.getQuickFixes(match, lineNumber);

          errors.push({
            line: lineNumber,
            column: 1,
            message: line.trim(),
            severity: errorPattern.severity,
            category: errorPattern.category,
            quickFixes
          });
          break;
        }
      }

      // Additional specific error patterns
      this.analyzeSpecificErrors(line, lineNumber, errors);
    }

    return this.deduplicateErrors(errors);
  }

  /**
   * Analyze document structure for potential issues
   */
  analyzeStructure(content: string): LaTeXError[] {
    const errors: LaTeXError[] = [];
    const lines = content.split('\n');

    // Check for unmatched environments
    const environments = new Map<string, number[]>();

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Find \begin{env} commands
      const beginMatch = line.match(/\\begin\{([^}]+)\}/g);
      if (beginMatch) {
        beginMatch.forEach(match => {
          const env = match.match(/\\begin\{([^}]+)\}/)?.[1];
          if (env) {
            if (!environments.has(env)) {
              environments.set(env, []);
            }
            environments.get(env)!.push(i + 1);
          }
        });
      }

      // Find \end{env} commands
      const endMatch = line.match(/\\end\{([^}]+)\}/g);
      if (endMatch) {
        endMatch.forEach(match => {
          const env = match.match(/\\end\{([^}]+)\}/)?.[1];
          if (env) {
            const beginLines = environments.get(env) || [];
            if (beginLines.length === 0) {
              errors.push({
                line: i + 1,
                column: 1,
                message: `\\end{${env}} without matching \\begin{${env}}`,
                severity: 'error',
                category: 'environment',
                quickFixes: [{
                  title: `Add \\begin{${env}}`,
                  description: `Insert matching \\begin{${env}} command`,
                  edits: [{
                    range: new monaco.Range(i + 1, 1, i + 1, 1),
                    text: `\\begin{${env}}\n`
                  }],
                  kind: 'quickfix'
                }]
              });
            } else {
              beginLines.pop(); // Remove one matching begin
            }
          }
        });
      }
    }

    // Check for unmatched \begin commands
    for (const [env, beginLines] of environments.entries()) {
      for (const lineNumber of beginLines) {
        errors.push({
          line: lineNumber,
          column: 1,
          message: `\\begin{${env}} without matching \\end{${env}}`,
          severity: 'error',
          category: 'environment',
          quickFixes: [{
            title: `Add \\end{${env}}`,
            description: `Insert matching \\end{${env}} command`,
            edits: [{
              range: new monaco.Range(lines.length, 1000, lines.length, 1000),
              text: `\n\\end{${env}}`
            }],
            kind: 'quickfix'
          }]
        });
      }
    }

    return errors;
  }

  /**
   * Generate code action suggestions for a specific error
   */
  getCodeActions(
    error: LaTeXError,
    model: monaco.editor.ITextModel
  ): monaco.languages.CodeAction[] {
    if (!error.quickFixes) return [];

    return error.quickFixes.map(fix => ({
      title: fix.title,
      kind: fix.kind,
      diagnostics: [],
      edit: {
        edits: [{
          resource: model.uri,
          versionId: model.getVersionId(),
          textEdit: {
            range: new monaco.Range(error.line, 1, error.line, 1000),
            text: fix.edits[0]?.text || ''
          }
        }]
      },
      isPreferred: fix.kind === 'quickfix'
    } as monaco.languages.CodeAction));
  }

  private analyzeSpecificErrors(line: string, lineNumber: number, errors: LaTeXError[]): void {
    // Analyze specific error patterns that need custom handling

    // Missing packages based on commands
    const commandMatch = line.match(/Undefined control sequence[.\s]*\\([a-zA-Z]+)/);
    if (commandMatch) {
      const command = commandMatch[1];
      const packageSuggestion = this.getRequiredPackage(command);

      if (packageSuggestion) {
        errors.push({
          line: lineNumber,
          column: 1,
          message: `Command \\${command} requires package ${packageSuggestion}`,
          severity: 'error',
          category: 'package',
          quickFixes: [{
            title: `Add \\usepackage{${packageSuggestion}}`,
            description: `Add the required package to use \\${command}`,
            edits: [{
              range: new monaco.Range(1, 1, 1, 1),
              text: `\\usepackage{${packageSuggestion}}\n`
            }],
            kind: 'quickfix'
          }]
        });
      }
    }
  }

  private getCommandSuggestions(command: string): Array<{ command: string; description: string }> {
    const suggestions = [];

    // Check direct mapping
    if (this.commandSuggestions.has(command.toLowerCase())) {
      suggestions.push(this.commandSuggestions.get(command.toLowerCase())!);
    }

    // Fuzzy matching for similar commands
    for (const [key, value] of this.commandSuggestions) {
      if (this.calculateSimilarity(command.toLowerCase(), key) > 0.7) {
        suggestions.push(value);
      }
    }

    return suggestions.slice(0, 3); // Return top 3 suggestions
  }

  private getRequiredPackage(command: string): string | null {
    // Command-to-package mappings
    const commandPackages = new Map([
      ['includegraphics', 'graphicx'],
      ['href', 'hyperref'],
      ['textcolor', 'xcolor'],
      ['fancyhead', 'fancyhdr'],
      ['newgeometry', 'geometry'],
      ['amsmath', 'amsmath'],
      ['amssymb', 'amssymb'],
      ['color', 'xcolor'],
      ['babel', 'babel'],
      ['inputenc', 'inputenc'],
      ['fontenc', 'fontenc']
    ]);

    return commandPackages.get(command) || null;
  }

  private calculateSimilarity(str1: string, str2: string): number {
    const len1 = str1.length;
    const len2 = str2.length;
    const matrix = Array(len2 + 1).fill(null).map(() => Array(len1 + 1).fill(null));

    for (let i = 0; i <= len1; i++) matrix[0][i] = i;
    for (let j = 0; j <= len2; j++) matrix[j][0] = j;

    for (let j = 1; j <= len2; j++) {
      for (let i = 1; i <= len1; i++) {
        const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + cost
        );
      }
    }

    return 1 - matrix[len2][len1] / Math.max(len1, len2);
  }

  private deduplicateErrors(errors: LaTeXError[]): LaTeXError[] {
    const seen = new Set<string>();
    return errors.filter(error => {
      const key = `${error.line}-${error.message}`;
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  }
}

export const latexErrorAnalyzer = new LaTeXErrorAnalyzer();
export default LaTeXErrorAnalyzer;
