/**
 * Template Selector Component
 */
'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '../ui/card';
import { TemplateVariable, TemplateMetadata, Template } from './types';

interface TemplateSelectorProps {
  onTemplateSelect: (template: Template) => void;
  selectedTemplateId?: string;
  className?: string;
}

const TemplateSelector: React.FC<TemplateSelectorProps> = ({
  onTemplateSelect,
  selectedTemplateId,
  className = '',
}) => {
  const [templates, setTemplates] = useState<TemplateMetadata[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load templates and categories
  useEffect(() => {
    loadTemplates();
    loadCategories();
  }, []);

  const loadTemplates = async (category?: string) => {
    try {
      setLoading(true);
      const url = category
        ? `http://localhost:8000/api/templates?category=${encodeURIComponent(category)}`
        : 'http://localhost:8000/api/templates';

      const response = await fetch(url);
      if (!response.ok) throw new Error('Failed to load templates');

      const data = await response.json();
      setTemplates(data.templates || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load templates');
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/templates/categories');
      if (!response.ok) throw new Error('Failed to load categories');

      const data = await response.json();
      setCategories(data.categories || []);
    } catch (err) {
      console.error('Failed to load categories:', err);
    }
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    loadTemplates(category || undefined);
  };

  const handleTemplateSelect = async (templateId: string) => {
    try {
      const response = await fetch(`http://localhost:8000/api/templates/${templateId}`);
      if (!response.ok) throw new Error('Failed to load template details');

      const template: Template = await response.json();
      onTemplateSelect(template);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load template');
    }
  };

  const getCategoryColor = (category: string): string => {
    const colors = {
      'Professional': 'bg-blue-100 text-blue-800',
      'Traditional': 'bg-gray-100 text-gray-800',
      'Technology': 'bg-green-100 text-green-800',
      'Creative': 'bg-purple-100 text-purple-800',
      'Academic': 'bg-indigo-100 text-indigo-800',
    };
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  if (loading && templates.length === 0) {
    return (
      <div className={`template-selector ${className}`}>
        <div className="flex items-center justify-center p-8">
          <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full"></div>
          <span className="ml-2 text-gray-600">Loading templates...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`template-selector ${className}`}>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="text-red-600 font-medium">Error loading templates</div>
          <div className="text-red-500 text-sm mt-1">{error}</div>
          <button
            onClick={() => {
              setError(null);
              loadTemplates();
            }}
            className="mt-2 px-3 py-1 bg-red-100 text-red-700 rounded text-sm hover:bg-red-200 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`template-selector ${className}`}>
      {/* Category Filter */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Filter by Category
        </label>
        <select
          value={selectedCategory}
          onChange={(e) => handleCategoryChange(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">All Categories</option>
          {categories.map(category => (
            <option key={category} value={category}>
              {category}
            </option>
          ))}
        </select>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {templates.map(template => (
          <Card
            key={template.id}
            className={`cursor-pointer transition-all hover:shadow-lg ${
              selectedTemplateId === template.id
                ? 'ring-2 ring-blue-500 bg-blue-50'
                : 'hover:bg-gray-50'
            }`}
            onClick={() => handleTemplateSelect(template.id)}
          >
            <CardContent className="p-4">
              {/* Template Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <h3 className="font-semibold text-lg text-gray-900 mb-1">
                    {template.name}
                  </h3>
                  <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(template.category)}`}>
                    {template.category}
                  </span>
                </div>

                {selectedTemplateId === template.id && (
                  <div className="text-blue-500">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                )}
              </div>

              {/* Description */}
              <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                {template.description}
              </p>

              {/* Tags */}
              <div className="flex flex-wrap gap-1 mb-3">
                {template.tags.slice(0, 3).map(tag => (
                  <span
                    key={tag}
                    className="inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
                  >
                    {tag}
                  </span>
                ))}
                {template.tags.length > 3 && (
                  <span className="inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                    +{template.tags.length - 3}
                  </span>
                )}
              </div>

              {/* Template Stats */}
              <div className="text-xs text-gray-500 space-y-1">
                <div className="flex justify-between">
                  <span>Version:</span>
                  <span>{template.version}</span>
                </div>
                <div className="flex justify-between">
                  <span>Variables:</span>
                  <span>{template.variables.length}</span>
                </div>
                <div className="flex justify-between">
                  <span>Required:</span>
                  <span>{template.variables.filter(v => v.required).length}</span>
                </div>
              </div>

              {/* Preview Button */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  // TODO: Implement preview functionality
                  console.log('Preview template:', template.id);
                }}
                className="w-full mt-3 px-3 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors text-sm"
              >
                Preview Template
              </button>
            </CardContent>
          </Card>
        ))}
      </div>

      {templates.length === 0 && !loading && (
        <div className="text-center py-8">
          <div className="text-gray-500 mb-2">No templates found</div>
          {selectedCategory && (
            <button
              onClick={() => handleCategoryChange('')}
              className="text-blue-500 hover:text-blue-600 text-sm"
            >
              Show all templates
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default TemplateSelector;
