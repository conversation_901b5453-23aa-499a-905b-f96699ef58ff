/* LaTeX Editor Styles */
.latex-editor {
  font-family: 'Monaco', '<PERSON><PERSON>', 'Consolas', monospace;
}

.latex-editor-layout {
  background: #fafafa;
}

.dark .latex-editor-layout {
  background: #1e1e1e;
}

/* Monaco Editor Wrapper */
.latex-editor .monaco-editor {
  border: none;
  outline: none;
}

/* PDF Viewer Styles */
.pdf-viewer {
  background: #f5f5f5;
}

.dark .pdf-viewer {
  background: #1a1a1a;
}

.pdf-toolbar {
  backdrop-filter: blur(10px);
}

.pdf-canvas-container {
  position: relative;
  overflow: auto;
}

.pdf-canvas {
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark .pdf-canvas {
  border-color: #333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Status Bar Styles */
.status-bar {
  font-size: 0.875rem;
  line-height: 1.25rem;
  backdrop-filter: blur(10px);
}

/* Panel Resize Handle */
.panel-resize-handle {
  position: relative;
  transition: background-color 0.2s ease;
}

.panel-resize-handle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 3px;
  height: 20px;
  background: currentColor;
  border-radius: 1.5px;
  transform: translate(-50%, -50%);
  opacity: 0.3;
}

.panel-resize-handle:hover::before {
  opacity: 0.6;
}

/* Toolbar Button Styles */
.toolbar-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.toolbar-button:hover {
  background: rgba(0, 0, 0, 0.05);
  border-color: rgba(0, 0, 0, 0.1);
}

.dark .toolbar-button:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .latex-editor-layout {
    flex-direction: column;
  }

  .header-bar {
    padding: 0.5rem 1rem;
  }

  .header-bar h1 {
    font-size: 1rem;
  }

  .status-bar {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .pdf-toolbar {
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .panel-resize-handle {
    width: 100%;
    height: 8px;
    cursor: row-resize;
  }

  .panel-resize-handle::before {
    width: 20px;
    height: 3px;
  }
}

/* Loading Spinner */
.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error States */
.error-message {
  color: #ef4444;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin: 0.5rem;
}

.dark .error-message {
  background: #1f1a1a;
  border-color: #dc2626;
}

/* Success States */
.success-message {
  color: #10b981;
  background: #f0fdfa;
  border: 1px solid #a7f3d0;
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin: 0.5rem;
}

.dark .success-message {
  background: #1a1f1a;
  border-color: #059669;
}
