/**
 * LaTeX syntax validator with live error detection
 */
import { editor, IDisposable, MarkerSeverity } from 'monaco-editor';
import { latexDiagnosticsProvider } from './latex-language';

export interface SyntaxError {
  line: number;
  column: number;
  message: string;
  severity: 'error' | 'warning' | 'info';
  code?: string;
}

export class LatexSyntaxValidator {
  private editor: editor.IStandaloneCodeEditor;
  private disposables: IDisposable[] = [];

  constructor(editorInstance: editor.IStandaloneCodeEditor) {
    this.editor = editorInstance;
    this.setupValidation();
  }

  private setupValidation() {
    // Real-time validation on content changes
    this.disposables.push(
      this.editor.onDidChangeModelContent(() => {
        this.validateSyntax();
      })
    );

    // Initial validation
    this.validateSyntax();
  }

  private validateSyntax() {
    const model = this.editor.getModel();
    if (!model) return;

    try {
      const diagnostics = latexDiagnosticsProvider.provideDiagnostics(model);

      // Update markers in the editor
      editor.setModelMarkers(model, 'latex', diagnostics);

      // Emit custom event for error display
      this.emitSyntaxErrors(diagnostics);
    } catch (error) {
      console.warn('LaTeX syntax validation error:', error);
    }
  }

  private emitSyntaxErrors(markers: editor.IMarkerData[]) {
    const syntaxErrors: SyntaxError[] = markers.map(marker => ({
      line: marker.startLineNumber,
      column: marker.startColumn,
      message: marker.message,
      severity: this.mapSeverity(marker.severity),
      code: marker.code as string,
    }));

    // Dispatch custom event
    window.dispatchEvent(new CustomEvent('latex-syntax-errors', {
      detail: { errors: syntaxErrors }
    }));
  }

  private mapSeverity(severity: MarkerSeverity): 'error' | 'warning' | 'info' {
    switch (severity) {
      case MarkerSeverity.Error:
        return 'error';
      case MarkerSeverity.Warning:
        return 'warning';
      case MarkerSeverity.Info:
        return 'info';
      default:
        return 'info';
    }
  }

  public validateDocument(): SyntaxError[] {
    const model = this.editor.getModel();
    if (!model) return [];

    const diagnostics = latexDiagnosticsProvider.provideDiagnostics(model);
    return diagnostics.map(marker => ({
      line: marker.startLineNumber,
      column: marker.startColumn,
      message: marker.message,
      severity: this.mapSeverity(marker.severity),
      code: marker.code as string,
    }));
  }

  public getErrorCount(): { errors: number; warnings: number; info: number } {
    const model = this.editor.getModel();
    if (!model) return { errors: 0, warnings: 0, info: 0 };

    const markers = editor.getModelMarkers({ resource: model.uri });

    return markers.reduce(
      (count, marker) => {
        switch (marker.severity) {
          case MarkerSeverity.Error:
            count.errors++;
            break;
          case MarkerSeverity.Warning:
            count.warnings++;
            break;
          case MarkerSeverity.Info:
            count.info++;
            break;
        }
        return count;
      },
      { errors: 0, warnings: 0, info: 0 }
    );
  }

  public dispose() {
    this.disposables.forEach(d => d.dispose());
    this.disposables = [];
  }
}

// Advanced LaTeX validation rules
export const advancedValidationRules = {
  // Environment matching validation
  validateEnvironments: (text: string): SyntaxError[] => {
    const errors: SyntaxError[] = [];
    const lines = text.split('\n');
    const environmentStack: Array<{ name: string; line: number }> = [];

    lines.forEach((line, index) => {
      const lineNumber = index + 1;

      // Find begin environments
      const beginMatch = line.match(/\\begin\{([^}]+)\}/g);
      if (beginMatch) {
        beginMatch.forEach(match => {
          const envName = match.match(/\\begin\{([^}]+)\}/)![1];
          environmentStack.push({ name: envName, line: lineNumber });
        });
      }

      // Find end environments
      const endMatch = line.match(/\\end\{([^}]+)\}/g);
      if (endMatch) {
        endMatch.forEach(match => {
          const envName = match.match(/\\end\{([^}]+)\}/)![1];
          const lastEnv = environmentStack.pop();

          if (!lastEnv) {
            errors.push({
              line: lineNumber,
              column: line.indexOf(match) + 1,
              message: `Unexpected \\end{${envName}} - no matching \\begin`,
              severity: 'error',
              code: 'unmatched-end'
            });
          } else if (lastEnv.name !== envName) {
            errors.push({
              line: lineNumber,
              column: line.indexOf(match) + 1,
              message: `Environment mismatch: \\end{${envName}} doesn't match \\begin{${lastEnv.name}} on line ${lastEnv.line}`,
              severity: 'error',
              code: 'environment-mismatch'
            });
            // Put it back for potential later matching
            environmentStack.push(lastEnv);
          }
        });
      }
    });

    // Check for unclosed environments
    environmentStack.forEach(env => {
      errors.push({
        line: env.line,
        column: 1,
        message: `Unclosed environment: \\begin{${env.name}} has no matching \\end{${env.name}}`,
        severity: 'error',
        code: 'unclosed-environment'
      });
    });

    return errors;
  },

  // Math mode validation
  validateMathMode: (text: string): SyntaxError[] => {
    const errors: SyntaxError[] = [];
    const lines = text.split('\n');

    lines.forEach((line, index) => {
      const lineNumber = index + 1;

      // Check for unmatched $ symbols
      const dollarSigns = line.match(/\$/g) || [];
      if (dollarSigns.length % 2 !== 0) {
        const lastDollarIndex = line.lastIndexOf('$');
        errors.push({
          line: lineNumber,
          column: lastDollarIndex + 1,
          message: 'Unmatched $ in math mode',
          severity: 'error',
          code: 'unmatched-math'
        });
      }

      // Check for $$ pairs
      const displayMathMatches = line.match(/\$\$/g) || [];
      if (displayMathMatches.length % 2 !== 0) {
        errors.push({
          line: lineNumber,
          column: line.indexOf('$$') + 1,
          message: 'Unmatched $$ in display math mode',
          severity: 'error',
          code: 'unmatched-display-math'
        });
      }
    });

    return errors;
  },

  // Command validation
  validateCommands: (text: string): SyntaxError[] => {
    const errors: SyntaxError[] = [];
    const lines = text.split('\n');

    // Common LaTeX commands that require arguments
    const commandsRequiringArgs = [
      'section', 'subsection', 'subsubsection', 'chapter',
      'textbf', 'textit', 'emph', 'usepackage', 'documentclass',
      'label', 'ref', 'cite', 'includegraphics'
    ];

    lines.forEach((line, index) => {
      const lineNumber = index + 1;

      commandsRequiringArgs.forEach(cmd => {
        const regex = new RegExp(`\\\\${cmd}(?![a-zA-Z])(?!\\s*\\{)`, 'g');
        let match;
        while ((match = regex.exec(line)) !== null) {
          errors.push({
            line: lineNumber,
            column: match.index + 1,
            message: `Command \\${cmd} requires arguments in braces {}`,
            severity: 'warning',
            code: 'missing-braces'
          });
        }
      });

      // Check for commands with empty braces
      const emptyBracesMatch = line.match(/\\([a-zA-Z]+)\{\}/g);
      if (emptyBracesMatch) {
        emptyBracesMatch.forEach(match => {
          const cmdName = match.match(/\\([a-zA-Z]+)/)![1];
          if (commandsRequiringArgs.includes(cmdName)) {
            const index = line.indexOf(match);
            errors.push({
              line: lineNumber,
              column: index + 1,
              message: `Command \\${cmdName} has empty braces - content required`,
              severity: 'warning',
              code: 'empty-braces'
            });
          }
        });
      }
    });

    return errors;
  },

  // Reference validation
  validateReferences: (text: string): SyntaxError[] => {
    const errors: SyntaxError[] = [];
    const lines = text.split('\n');

    // Collect all labels
    const labels = new Set<string>();
    const labelLines = new Map<string, number>();

    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      const labelMatches = line.match(/\\label\{([^}]+)\}/g);
      if (labelMatches) {
        labelMatches.forEach(match => {
          const labelName = match.match(/\\label\{([^}]+)\}/)![1];
          if (labels.has(labelName)) {
            errors.push({
              line: lineNumber,
              column: line.indexOf(match) + 1,
              message: `Duplicate label: '${labelName}' already defined on line ${labelLines.get(labelName)}`,
              severity: 'error',
              code: 'duplicate-label'
            });
          } else {
            labels.add(labelName);
            labelLines.set(labelName, lineNumber);
          }
        });
      }
    });

    // Check references
    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      const refMatches = line.match(/\\ref\{([^}]+)\}/g);
      if (refMatches) {
        refMatches.forEach(match => {
          const refName = match.match(/\\ref\{([^}]+)\}/)![1];
          if (!labels.has(refName)) {
            errors.push({
              line: lineNumber,
              column: line.indexOf(match) + 1,
              message: `Undefined reference: label '${refName}' not found`,
              severity: 'warning',
              code: 'undefined-reference'
            });
          }
        });
      }
    });

    return errors;
  }
};
