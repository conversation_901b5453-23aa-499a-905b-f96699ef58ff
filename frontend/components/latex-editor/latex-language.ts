/**
 * Enhanced LaTeX language configuration for Monaco Editor
 */
import { editor, languages, MarkerSeverity } from 'monaco-editor';

export const latexLanguageConfig: languages.LanguageConfiguration = {
  comments: {
    lineComment: '%',
  },
  brackets: [
    ['{', '}'],
    ['[', ']'],
    ['(', ')'],
  ],
  autoClosingPairs: [
    { open: '{', close: '}' },
    { open: '[', close: ']' },
    { open: '(', close: ')' },
    { open: '$', close: '$' },
    { open: '$$', close: '$$' },
  ],
  surroundingPairs: [
    { open: '{', close: '}' },
    { open: '[', close: ']' },
    { open: '(', close: ')' },
    { open: '$', close: '$' },
  ],
  folding: {
    markers: {
      start: new RegExp('\\\\begin\\{[^}]*\\}'),
      end: new RegExp('\\\\end\\{[^}]*\\}'),
    },
  },
};

export const latexTokensProvider: languages.IMonarchLanguage = {
  // Set defaultToken to invalid to see what you do not tokenize yet
  defaultToken: '',

  // LaTeX keywords and commands
  keywords: [
    'begin', 'end', 'section', 'subsection', 'subsubsection', 'paragraph', 'subparagraph',
    'part', 'chapter', 'title', 'author', 'date', 'maketitle',
    'documentclass', 'usepackage', 'newcommand', 'renewcommand',
    'textbf', 'textit', 'texttt', 'emph', 'underline',
    'large', 'Large', 'LARGE', 'huge', 'Huge', 'small', 'footnotesize', 'scriptsize', 'tiny',
    'centering', 'raggedright', 'raggedleft',
    'item', 'enumerate', 'itemize', 'description',
    'includegraphics', 'figure', 'table', 'tabular',
    'ref', 'label', 'cite', 'bibliography', 'bibliographystyle',
    'footnote', 'marginpar',
    'hspace', 'vspace', 'pagebreak', 'newpage', 'clearpage',
  ],

  // Math keywords
  mathKeywords: [
    'frac', 'sqrt', 'sum', 'int', 'prod', 'lim', 'infty',
    'alpha', 'beta', 'gamma', 'delta', 'epsilon', 'zeta', 'eta', 'theta',
    'iota', 'kappa', 'lambda', 'mu', 'nu', 'xi', 'pi', 'rho',
    'sigma', 'tau', 'upsilon', 'phi', 'chi', 'psi', 'omega',
    'Gamma', 'Delta', 'Theta', 'Lambda', 'Xi', 'Pi', 'Sigma',
    'Upsilon', 'Phi', 'Psi', 'Omega',
    'sin', 'cos', 'tan', 'log', 'ln', 'exp', 'min', 'max',
    'leftarrow', 'rightarrow', 'Leftarrow', 'Rightarrow', 'leftrightarrow',
    'leq', 'geq', 'neq', 'approx', 'equiv', 'propto',
  ],

  // Resume-specific commands
  resumeKeywords: [
    'resumeItem', 'resumeSubheading', 'resumeSubHeadingListStart', 'resumeSubHeadingListEnd',
    'resumeItemListStart', 'resumeItemListEnd', 'resumeProjectHeading',
    'workplace', 'project', 'contact', 'header',
  ],

  // C# style strings
  escapes: /\\(?:[abfnrtv\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,

  // The main tokenizer for our languages
  tokenizer: {
    root: [
      // Comments
      [/%.*$/, 'comment'],

      // Math mode (inline)
      [/\$[^$]*\$/, 'string.math'],

      // Math mode (display)
      [/\$\$/, { token: 'string.math', next: '@displayMath' }],

      // LaTeX commands
      [/\\@?[a-zA-Z@]+/, {
        cases: {
          '@keywords': 'keyword.latex',
          '@mathKeywords': 'keyword.math',
          '@resumeKeywords': 'keyword.resume',
          '@default': 'keyword.control'
        }
      }],

      // Environment commands
      [/\\(begin|end)\s*\{([^}]+)\}/, ['keyword.latex', 'string.environment']],

      // References and labels
      [/\\(ref|label|cite)\s*\{([^}]+)\}/, ['keyword.latex', 'string.reference']],

      // Section commands with titles
      [/\\(section|subsection|subsubsection|paragraph)\*?\s*\{/, { token: 'keyword.latex', next: '@sectionTitle' }],

      // Braces
      [/[{}]/, 'delimiter.curly'],
      [/[\[\]]/, 'delimiter.square'],
      [/[()]/, 'delimiter.parenthesis'],

      // Special characters
      [/[&%#]/, 'keyword.control'],

      // Numbers
      [/\d*\.\d+([eE][\-+]?\d+)?/, 'number.float'],
      [/\d+/, 'number'],

      // Strings
      [/"([^"\\]|\\.)*$/, 'string.invalid'],
      [/"/, { token: 'string.quote', bracket: '@open', next: '@string' }],

      // Whitespace
      { include: '@whitespace' },
    ],

    comment: [
      [/[^%]+/, 'comment'],
      [/%/, 'comment']
    ],

    string: [
      [/[^\\"]+/, 'string'],
      [/@escapes/, 'string.escape'],
      [/\\./, 'string.escape.invalid'],
      [/"/, { token: 'string.quote', bracket: '@close', next: '@pop' }]
    ],

    displayMath: [
      [/[^$\\]+/, 'string.math'],
      [/\\@?[a-zA-Z@]+/, 'keyword.math'],
      [/\$\$/, { token: 'string.math', next: '@pop' }],
      [/[\$\\]/, 'string.math']
    ],

    sectionTitle: [
      [/[^}]+/, 'string.title'],
      [/\}/, { token: 'delimiter.curly', next: '@pop' }]
    ],

    whitespace: [
      [/[ \t\r\n]+/, 'white'],
      [/%.*$/, 'comment'],
    ],
  },
};

// Enhanced completion provider with LaTeX commands
export const latexCompletionProvider: languages.CompletionItemProvider = {
  provideCompletionItems: (model, position) => {
    const word = model.getWordUntilPosition(position);
    const range = {
      startLineNumber: position.lineNumber,
      endLineNumber: position.lineNumber,
      startColumn: word.startColumn,
      endColumn: word.endColumn,
    };

    const suggestions: languages.CompletionItem[] = [
      // Document structure
      {
        label: 'documentclass',
        kind: languages.CompletionItemKind.Keyword,
        insertText: 'documentclass[${1:11pt}]{${2:article}}',
        insertTextRules: languages.CompletionItemInsertTextRule.InsertAsSnippet,
        documentation: 'Define document class',
        range: range,
      },
      {
        label: 'usepackage',
        kind: languages.CompletionItemKind.Keyword,
        insertText: 'usepackage{${1:package}}',
        insertTextRules: languages.CompletionItemInsertTextRule.InsertAsSnippet,
        documentation: 'Load a package',
        range: range,
      },
      {
        label: 'begin/end environment',
        kind: languages.CompletionItemKind.Snippet,
        insertText: 'begin{${1:document}}\n\t$0\n\\end{${1:document}}',
        insertTextRules: languages.CompletionItemInsertTextRule.InsertAsSnippet,
        documentation: 'Begin/end environment',
        range: range,
      },

      // Sections
      {
        label: 'section',
        kind: languages.CompletionItemKind.Keyword,
        insertText: 'section{${1:Section Title}}',
        insertTextRules: languages.CompletionItemInsertTextRule.InsertAsSnippet,
        documentation: 'Create a section',
        range: range,
      },
      {
        label: 'subsection',
        kind: languages.CompletionItemKind.Keyword,
        insertText: 'subsection{${1:Subsection Title}}',
        insertTextRules: languages.CompletionItemInsertTextRule.InsertAsSnippet,
        documentation: 'Create a subsection',
        range: range,
      },

      // Text formatting
      {
        label: 'textbf',
        kind: languages.CompletionItemKind.Keyword,
        insertText: 'textbf{${1:bold text}}',
        insertTextRules: languages.CompletionItemInsertTextRule.InsertAsSnippet,
        documentation: 'Bold text',
        range: range,
      },
      {
        label: 'textit',
        kind: languages.CompletionItemKind.Keyword,
        insertText: 'textit{${1:italic text}}',
        insertTextRules: languages.CompletionItemInsertTextRule.InsertAsSnippet,
        documentation: 'Italic text',
        range: range,
      },
      {
        label: 'emph',
        kind: languages.CompletionItemKind.Keyword,
        insertText: 'emph{${1:emphasized text}}',
        insertTextRules: languages.CompletionItemInsertTextRule.InsertAsSnippet,
        documentation: 'Emphasized text',
        range: range,
      },

      // Resume-specific commands
      {
        label: 'resumeSubheading',
        kind: languages.CompletionItemKind.Snippet,
        insertText: 'resumeSubheading\n\t{${1:Company Name}}{${2:Date Range}}\n\t{${3:Position Title}}{${4:Location}}',
        insertTextRules: languages.CompletionItemInsertTextRule.InsertAsSnippet,
        documentation: 'Resume job entry',
        range: range,
      },
      {
        label: 'resumeItemListStart/End',
        kind: languages.CompletionItemKind.Snippet,
        insertText: 'resumeItemListStart\n\t\\resumeItem{${1:Achievement or responsibility}}\n\t\\resumeItem{${2:Another achievement}}\n\\resumeItemListEnd',
        insertTextRules: languages.CompletionItemInsertTextRule.InsertAsSnippet,
        documentation: 'Resume item list',
        range: range,
      },
      {
        label: 'resumeProjectHeading',
        kind: languages.CompletionItemKind.Snippet,
        insertText: 'resumeProjectHeading\n\t{\\textbf{${1:Project Name}} $|$ \\emph{${2:Technologies}}}{${3:Date}}',
        insertTextRules: languages.CompletionItemInsertTextRule.InsertAsSnippet,
        documentation: 'Resume project heading',
        range: range,
      },

      // Lists
      {
        label: 'itemize',
        kind: languages.CompletionItemKind.Snippet,
        insertText: 'begin{itemize}\n\t\\item ${1:First item}\n\t\\item ${2:Second item}\n\\end{itemize}',
        insertTextRules: languages.CompletionItemInsertTextRule.InsertAsSnippet,
        documentation: 'Bulleted list',
        range: range,
      },
      {
        label: 'enumerate',
        kind: languages.CompletionItemKind.Snippet,
        insertText: 'begin{enumerate}\n\t\\item ${1:First item}\n\t\\item ${2:Second item}\n\\end{enumerate}',
        insertTextRules: languages.CompletionItemInsertTextRule.InsertAsSnippet,
        documentation: 'Numbered list',
        range: range,
      },

      // Math
      {
        label: 'inline math',
        kind: languages.CompletionItemKind.Snippet,
        insertText: '$${1:equation}$$',
        insertTextRules: languages.CompletionItemInsertTextRule.InsertAsSnippet,
        documentation: 'Inline math equation',
        range: range,
      },
      {
        label: 'display math',
        kind: languages.CompletionItemKind.Snippet,
        insertText: '$$\n${1:equation}\n$$',
        insertTextRules: languages.CompletionItemInsertTextRule.InsertAsSnippet,
        documentation: 'Display math equation',
        range: range,
      },
      {
        label: 'frac',
        kind: languages.CompletionItemKind.Keyword,
        insertText: 'frac{${1:numerator}}{${2:denominator}}',
        insertTextRules: languages.CompletionItemInsertTextRule.InsertAsSnippet,
        documentation: 'Fraction',
        range: range,
      },

      // Tables
      {
        label: 'table',
        kind: languages.CompletionItemKind.Snippet,
        insertText: 'begin{table}[${1:h}]\n\t\\centering\n\t\\begin{tabular}{${2:|c|c|}}\n\t\t\\hline\n\t\t${3:Header 1} & ${4:Header 2} \\\\\\\n\t\t\\hline\n\t\t${5:Cell 1} & ${6:Cell 2} \\\\\\\n\t\t\\hline\n\t\\end{tabular}\n\t\\caption{${7:Table caption}}\n\t\\label{${8:tab:label}}\n\\end{table}',
        insertTextRules: languages.CompletionItemInsertTextRule.InsertAsSnippet,
        documentation: 'Table environment',
        range: range,
      },
    ];

    return { suggestions };
  },
};

// Hover provider for LaTeX commands
export const latexHoverProvider: languages.HoverProvider = {
  provideHover: (model, position) => {
    const word = model.getWordAtPosition(position);
    if (!word) return null;

    const commandHelp: Record<string, string> = {
      'documentclass': 'Defines the document class (article, book, report, etc.)',
      'usepackage': 'Loads a LaTeX package',
      'begin': 'Starts an environment',
      'end': 'Ends an environment',
      'section': 'Creates a section heading',
      'subsection': 'Creates a subsection heading',
      'textbf': 'Makes text bold',
      'textit': 'Makes text italic',
      'emph': 'Emphasizes text (usually italic)',
      'item': 'Creates a list item',
      'label': 'Creates a label for cross-referencing',
      'ref': 'References a labeled item',
      'cite': 'Cites a bibliography entry',
      'resumeSubheading': 'Creates a resume job/education entry with company, dates, position, and location',
      'resumeItem': 'Creates a bulleted achievement or responsibility item',
      'resumeItemListStart': 'Starts a list of resume items',
      'resumeItemListEnd': 'Ends a list of resume items',
      'resumeProjectHeading': 'Creates a project heading with name, technologies, and date',
    };

    const help = commandHelp[word.word.replace('\\', '')];
    if (help) {
      return {
        range: {
          startLineNumber: position.lineNumber,
          endLineNumber: position.lineNumber,
          startColumn: word.startColumn,
          endColumn: word.endColumn,
        },
        contents: [
          { value: `**\\${word.word.replace('\\', '')}**` },
          { value: help },
        ],
      };
    }

    return null;
  },
};

// Error/diagnostic provider
export const latexDiagnosticsProvider = {
  provideDiagnostics: (model: editor.ITextModel): editor.IMarkerData[] => {
    const text = model.getValue();
    const lines = text.split('\n');
    const markers: editor.IMarkerData[] = [];

    lines.forEach((line, lineIndex) => {
      const lineNumber = lineIndex + 1;

      // Check for common LaTeX errors

      // Unmatched braces
      const openBraces = (line.match(/\{/g) || []).length;
      const closeBraces = (line.match(/\}/g) || []).length;
      if (openBraces !== closeBraces) {
        markers.push({
          severity: MarkerSeverity.Warning,
          startLineNumber: lineNumber,
          startColumn: 1,
          endLineNumber: lineNumber,
          endColumn: line.length + 1,
          message: 'Unmatched braces on this line',
        });
      }

      // Missing backslash before commands
      const commands = line.match(/[^\\]\b(begin|end|section|subsection|textbf|textit|item)\b/g);
      if (commands) {
        commands.forEach(cmd => {
          const index = line.indexOf(cmd);
          markers.push({
            severity: MarkerSeverity.Error,
            startLineNumber: lineNumber,
            startColumn: index + 2,
            endLineNumber: lineNumber,
            endColumn: index + cmd.length + 1,
            message: `Missing backslash before command '${cmd.substring(1)}'`,
            code: 'missing-backslash',
          });
        });
      }

      // Unmatched begin/end environments
      const beginMatch = line.match(/\\begin\{([^}]+)\}/);
      const endMatch = line.match(/\\end\{([^}]+)\}/);

      if (beginMatch && !text.includes(`\\end{${beginMatch[1]}}`)) {
        const index = line.indexOf(beginMatch[0]);
        markers.push({
          severity: MarkerSeverity.Error,
          startLineNumber: lineNumber,
          startColumn: index + 1,
          endLineNumber: lineNumber,
          endColumn: index + beginMatch[0].length + 1,
          message: `Environment '${beginMatch[1]}' is not closed`,
          code: 'unclosed-environment',
        });
      }

      // Invalid characters in labels/references
      const labelMatch = line.match(/\\(label|ref)\{([^}]+)\}/);
      if (labelMatch) {
        const label = labelMatch[2];
        if (label.includes(' ') || label.includes('-')) {
          const index = line.indexOf(labelMatch[0]);
          markers.push({
            severity: MarkerSeverity.Warning,
            startLineNumber: lineNumber,
            startColumn: index + labelMatch[1].length + 2,
            endLineNumber: lineNumber,
            endColumn: index + labelMatch[0].length,
            message: 'Labels should not contain spaces or hyphens. Use underscores instead.',
            code: 'invalid-label',
          });
        }
      }

      // Double backslashes in wrong context
      if (line.includes('\\\\') && !line.includes('\\\\[') && !line.includes('tabular')) {
        const index = line.indexOf('\\\\');
        markers.push({
          severity: MarkerSeverity.Info,
          startLineNumber: lineNumber,
          startColumn: index + 1,
          endLineNumber: lineNumber,
          endColumn: index + 3,
          message: 'Consider using \\newline or paragraph breaks instead of \\\\',
          code: 'double-backslash',
        });
      }
    });

    return markers;
  },
};

export function registerLatexLanguage(monaco: typeof import('monaco-editor')) {
  // Register the language
  monaco.languages.register({ id: 'latex' });

  // Set the language configuration
  monaco.languages.setLanguageConfiguration('latex', latexLanguageConfig);

  // Set the tokens provider
  monaco.languages.setMonarchTokensProvider('latex', latexTokensProvider);

  // Register completion provider
  monaco.languages.registerCompletionItemProvider('latex', latexCompletionProvider);

  // Register hover provider
  monaco.languages.registerHoverProvider('latex', latexHoverProvider);

  // Define color theme for LaTeX
  monaco.editor.defineTheme('latex-theme', {
    base: 'vs',
    inherit: true,
    rules: [
      { token: 'comment', foreground: '008000', fontStyle: 'italic' },
      { token: 'keyword.latex', foreground: '0000ff', fontStyle: 'bold' },
      { token: 'keyword.math', foreground: '8b0000', fontStyle: 'bold' },
      { token: 'keyword.resume', foreground: '800080', fontStyle: 'bold' },
      { token: 'keyword.control', foreground: 'ff4500' },
      { token: 'string.math', foreground: '006400', background: 'f0fff0' },
      { token: 'string.environment', foreground: '4b0082' },
      { token: 'string.reference', foreground: 'dc143c' },
      { token: 'string.title', foreground: '000080', fontStyle: 'bold' },
      { token: 'delimiter.curly', foreground: 'ff6347' },
      { token: 'delimiter.square', foreground: '1e90ff' },
      { token: 'number', foreground: '8b008b' },
    ],
    colors: {
      'editor.background': '#fefefe',
      'editor.foreground': '#333333',
      'editorLineNumber.foreground': '#cccccc',
      'editorCursor.foreground': '#000000',
      'editor.selectionBackground': '#d4edfc',
      'editor.inactiveSelectionBackground': '#e5e5e5',
    }
  });

  // Define dark theme for LaTeX
  monaco.editor.defineTheme('latex-dark-theme', {
    base: 'vs-dark',
    inherit: true,
    rules: [
      { token: 'comment', foreground: '6a9955', fontStyle: 'italic' },
      { token: 'keyword.latex', foreground: '4fc3f7', fontStyle: 'bold' },
      { token: 'keyword.math', foreground: 'ff7043', fontStyle: 'bold' },
      { token: 'keyword.resume', foreground: 'ba68c8', fontStyle: 'bold' },
      { token: 'keyword.control', foreground: 'ffb74d' },
      { token: 'string.math', foreground: '81c784', background: '1e3a1e' },
      { token: 'string.environment', foreground: 'ab47bc' },
      { token: 'string.reference', foreground: 'f06292' },
      { token: 'string.title', foreground: '90caf9', fontStyle: 'bold' },
      { token: 'delimiter.curly', foreground: 'ffab40' },
      { token: 'delimiter.square', foreground: '42a5f5' },
      { token: 'number', foreground: 'ce93d8' },
    ],
    colors: {
      'editor.background': '#1e1e1e',
      'editor.foreground': '#d4d4d4',
      'editorLineNumber.foreground': '#5a5a5a',
      'editorCursor.foreground': '#ffffff',
      'editor.selectionBackground': '#264f78',
      'editor.inactiveSelectionBackground': '#3a3d41',
    }
  });
}
