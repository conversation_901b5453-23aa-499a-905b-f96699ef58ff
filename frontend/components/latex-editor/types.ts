// LaTeX Editor Types
import type { editor } from 'monaco-editor';

export interface LaTeXEditorProps {
  /** Initial LaTeX content */
  initialContent?: string;
  /** Callback when content changes */
  onChange?: (content: string) => void;
  /** Callback when editor is ready */
  onEditorReady?: (editor: editor.IStandaloneCodeEditor) => void;
  /** Editor theme */
  theme?: 'vs-light' | 'vs-dark' | 'hc-black' | 'hc-light';
  /** Whether editor is read-only */
  readOnly?: boolean;
  /** Custom CSS class */
  className?: string;
  /** Editor height */
  height?: string | number;
  /** Enable/disable minimap */
  minimap?: boolean;
  /** Enable/disable word wrap */
  wordWrap?: boolean;
  /** Font size */
  fontSize?: number;
  /** Enable automatic layout adjustment */
  automaticLayout?: boolean;
}

export interface PDFViewerProps {
  /** URL or base64 data of PDF */
  pdfUrl?: string;
  /** PDF binary data */
  pdfData?: ArrayBuffer;
  /** Loading state */
  isLoading?: boolean;
  /** Error message */
  error?: string | null;
  /** Custom CSS class */
  className?: string;
  /** PDF viewer height */
  height?: string | number;
  /** Enable/disable zoom controls */
  showZoomControls?: boolean;
  /** Enable/disable navigation controls */
  showNavigation?: boolean;
  /** Initial zoom level */
  initialZoom?: number;
  /** Callback when PDF loads */
  onPDFLoad?: (numPages: number) => void;
  /** Callback when page changes */
  onPageChange?: (pageNumber: number) => void;
}

export interface LaTeXEditorLayoutProps {
  /** Initial LaTeX content */
  initialContent?: string;
  /** Editor configuration */
  editorProps?: Partial<LaTeXEditorProps>;
  /** PDF viewer configuration */
  pdfViewerProps?: Partial<PDFViewerProps>;
  /** Custom CSS class */
  className?: string;
  /** Layout orientation */
  orientation?: 'horizontal' | 'vertical';
  /** Initial panel sizes [editor%, viewer%] */
  defaultSizes?: [number, number];
  /** Minimum panel sizes */
  minSizes?: [number, number];
  /** Enable/disable panel resizing */
  resizerStyle?: React.CSSProperties;
}

export interface CompilationJob {
  /** Unique job ID */
  id: string;
  /** Job status */
  status: 'pending' | 'compiling' | 'success' | 'error';
  /** LaTeX content being compiled */
  content: string;
  /** Compilation logs */
  logs?: string;
  /** Error message if compilation failed */
  error?: string;
  /** PDF URL if compilation succeeded */
  pdfUrl?: string;
  /** Created timestamp */
  createdAt: Date;
  /** Updated timestamp */
  updatedAt: Date;
}

export interface LaTeXLanguageConfig {
  /** Language ID for Monaco */
  id: string;
  /** File extensions */
  extensions: string[];
  /** Language aliases */
  aliases: string[];
  /** MIME types */
  mimetypes: string[];
}

export interface TokenRule {
  /** Token pattern (string or regex) */
  pattern: string | RegExp;
  /** Token action or type */
  action: string | { token: string; [key: string]: unknown };
}

export interface LaTeXTokenizer {
  /** Root tokenizer rules */
  root: Array<TokenRule>;
  /** Comment tokenizer rules */
  comment?: Array<TokenRule>;
  /** Math mode tokenizer rules */
  math?: Array<TokenRule>;
  /** Environment tokenizer rules */
  environment?: Array<TokenRule>;
}

export interface EditorError {
  /** Error message */
  message: string;
  /** Line number (1-indexed) */
  line: number;
  /** Column number (1-indexed) */
  column?: number;
  /** Error severity */
  severity: 'error' | 'warning' | 'info';
  /** Error code */
  code?: string;
  /** Quick fix suggestions */
  quickFixes?: string[];
}

export interface WebSocketMessage {
  /** Message type */
  type: 'compile_request' | 'compilation_started' | 'compilation_progress' | 'compilation_success' | 'compilation_error' | 'connection_status';
  /** Message payload */
  payload: Record<string, unknown>;
  /** Message timestamp */
  timestamp: Date;
  /** Job ID for compilation messages */
  jobId?: string;
}

export interface LaTeXEditorState {
  /** Current LaTeX content */
  content: string;
  /** Current compilation job */
  currentJob?: CompilationJob;
  /** WebSocket connection state */
  isConnected: boolean;
  /** Editor errors */
  errors: EditorError[];
  /** PDF viewer state */
  pdfState: {
    currentPage: number;
    totalPages: number;
    zoom: number;
    isLoading: boolean;
  };
  /** Editor preferences */
  preferences: {
    theme: string;
    fontSize: number;
    wordWrap: boolean;
    minimap: boolean;
    autoCompile: boolean;
    compileDelay: number;
  };
}

// Template System Types
export interface TemplateVariable {
  name: string;
  type: 'string' | 'array' | 'object' | 'number' | 'boolean';
  required: boolean;
  default?: unknown;
  description: string;
  example?: unknown;
}

export interface TemplateMetadata {
  id: string;
  name: string;
  description: string;
  category: string;
  author: string;
  version: string;
  created_at: string;
  updated_at: string;
  variables: TemplateVariable[];
  tags: string[];
  preview_image?: string;
}

export interface Template {
  metadata: TemplateMetadata;
  content: string;
  sample_data: Record<string, unknown>;
}
