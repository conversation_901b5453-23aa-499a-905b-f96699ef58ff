/**
 * File Management System for LaTeX Editor
 * Handles save/load/export functionality with local storage and cloud sync
 */
'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import { Badge } from '../ui/badge';
import { ScrollArea } from '../ui/scroll-area';
import {
  Save,
  Upload,
  Download,
  FileText,
  Trash2,
  Copy,
  Clock,
  ChevronDown,
  Plus,
  Star,
  StarOff
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

export interface SavedDocument {
  id: string;
  name: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
  size: number;
  favorite: boolean;
  tags: string[];
  version: number;
}

export interface FileManagerProps {
  currentContent: string;
  onLoad: (content: string) => void;
  onSave?: (document: SavedDocument) => void;
  className?: string;
}

const FileManager: React.FC<FileManagerProps> = ({
  currentContent,
  onLoad,
  onSave,
  className = ''
}) => {
  const [savedDocuments, setSavedDocuments] = useState<SavedDocument[]>([]);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [showLoadDialog, setShowLoadDialog] = useState(false);
  const [saveName, setSaveName] = useState('');
  const [selectedDocument, setSelectedDocument] = useState<SavedDocument | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  // Load saved documents from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem('latex-documents');
    if (saved) {
      try {
        const documents = JSON.parse(saved).map((doc: SavedDocument) => ({
          ...doc,
          createdAt: new Date(doc.createdAt),
          updatedAt: new Date(doc.updatedAt)
        }));
        setSavedDocuments(documents);
      } catch (error) {
        console.error('Failed to load saved documents:', error);
      }
    }
  }, []);

  // Save documents to localStorage whenever the list changes
  const saveToStorage = (documents: SavedDocument[]) => {
    localStorage.setItem('latex-documents', JSON.stringify(documents));
    setSavedDocuments(documents);
  };

  // Save current document
  const handleSave = (name: string) => {
    const existingDoc = savedDocuments.find(doc => doc.name === name);

    if (existingDoc) {
      // Update existing document
      const updatedDoc = {
        ...existingDoc,
        content: currentContent,
        updatedAt: new Date(),
        size: new Blob([currentContent]).size,
        version: existingDoc.version + 1
      };
      const updatedDocs = savedDocuments.map(doc =>
        doc.id === existingDoc.id ? updatedDoc : doc
      );
      saveToStorage(updatedDocs);

      if (onSave) onSave(updatedDoc);
    } else {
      // Create new document
      const newDoc: SavedDocument = {
        id: Date.now().toString(),
        name: name,
        content: currentContent,
        createdAt: new Date(),
        updatedAt: new Date(),
        size: new Blob([currentContent]).size,
        favorite: false,
        tags: [],
        version: 1
      };
      saveToStorage([...savedDocuments, newDoc]);

      if (onSave) onSave(newDoc);
    }

    setShowSaveDialog(false);
    setSaveName('');
  };

  // Load document
  const handleLoad = (document: SavedDocument) => {
    onLoad(document.content);
    setShowLoadDialog(false);
    setSelectedDocument(document);
  };

  // Delete document
  const handleDelete = (id: string) => {
    const updatedDocs = savedDocuments.filter(doc => doc.id !== id);
    saveToStorage(updatedDocs);
  };

  // Toggle favorite
  const toggleFavorite = (id: string) => {
    const updatedDocs = savedDocuments.map(doc =>
      doc.id === id ? { ...doc, favorite: !doc.favorite } : doc
    );
    saveToStorage(updatedDocs);
  };

  // Duplicate document
  const duplicateDocument = (document: SavedDocument) => {
    const newDoc: SavedDocument = {
      ...document,
      id: Date.now().toString(),
      name: `${document.name} (Copy)`,
      createdAt: new Date(),
      updatedAt: new Date(),
      version: 1
    };
    saveToStorage([...savedDocuments, newDoc]);
  };

  // Export document
  const exportDocument = (doc: SavedDocument, format: 'tex' | 'txt') => {
    const blob = new Blob([doc.content], {
      type: format === 'tex' ? 'text/x-tex' : 'text/plain'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${doc.name}.${format}`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // Import document
  const importDocument = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.tex,.txt';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target?.result as string;
          const newDoc: SavedDocument = {
            id: Date.now().toString(),
            name: file.name.replace(/\.[^/.]+$/, ''),
            content: content,
            createdAt: new Date(),
            updatedAt: new Date(),
            size: file.size,
            favorite: false,
            tags: [],
            version: 1
          };
          saveToStorage([...savedDocuments, newDoc]);
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  // Filter documents based on search
  const filteredDocuments = savedDocuments.filter(doc =>
    doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    doc.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Sort documents (favorites first, then by updated date)
  const sortedDocuments = filteredDocuments.sort((a, b) => {
    if (a.favorite && !b.favorite) return -1;
    if (!a.favorite && b.favorite) return 1;
    return b.updatedAt.getTime() - a.updatedAt.getTime();
  });

  return (
    <div className={`file-manager ${className}`}>
      {/* Main Toolbar */}
      <div className="flex items-center gap-2">
        {/* Save Button */}
        <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" className="flex items-center gap-2">
              <Save className="w-4 h-4" />
              Save
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Save Document</DialogTitle>
              <DialogDescription>
                Give your LaTeX document a name to save it locally.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="saveName">Document Name</Label>
                <Input
                  id="saveName"
                  value={saveName}
                  onChange={(e) => setSaveName(e.target.value)}
                  placeholder="Enter document name..."
                  autoFocus
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowSaveDialog(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={() => handleSave(saveName)}
                disabled={!saveName.trim()}
              >
                Save Document
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Load Button */}
        <Dialog open={showLoadDialog} onOpenChange={setShowLoadDialog}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" className="flex items-center gap-2">
              <Upload className="w-4 h-4" />
              Load
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Load Document</DialogTitle>
              <DialogDescription>
                Choose a saved document to load into the editor.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              {/* Search */}
              <Input
                placeholder="Search documents..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />

              {/* Document List */}
              <ScrollArea className="h-64">
                {sortedDocuments.length === 0 ? (
                  <div className="text-center text-gray-500 py-8">
                    {searchQuery ? 'No documents match your search' : 'No saved documents'}
                  </div>
                ) : (
                  <div className="space-y-2">
                    {sortedDocuments.map((doc) => (
                      <div
                        key={doc.id}
                        className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800"
                      >
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => toggleFavorite(doc.id)}
                              className="text-gray-400 hover:text-yellow-500"
                            >
                              {doc.favorite ? (
                                <Star className="w-4 h-4 fill-yellow-500 text-yellow-500" />
                              ) : (
                                <StarOff className="w-4 h-4" />
                              )}
                            </button>
                            <h4 className="font-medium truncate">{doc.name}</h4>
                            {doc.version > 1 && (
                              <Badge variant="secondary" className="text-xs">
                                v{doc.version}
                              </Badge>
                            )}
                          </div>
                          <div className="flex items-center gap-4 text-sm text-gray-500 mt-1">
                            <span>{formatDistanceToNow(doc.updatedAt)} ago</span>
                            <span>{(doc.size / 1024).toFixed(1)} KB</span>
                          </div>
                        </div>

                        <div className="flex items-center gap-1 ml-4">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleLoad(doc)}
                          >
                            Load
                          </Button>

                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <ChevronDown className="w-4 h-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => duplicateDocument(doc)}>
                                <Copy className="w-4 h-4 mr-2" />
                                Duplicate
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => exportDocument(doc, 'tex')}>
                                <Download className="w-4 h-4 mr-2" />
                                Export as .tex
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => exportDocument(doc, 'txt')}>
                                <Download className="w-4 h-4 mr-2" />
                                Export as .txt
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleDelete(doc.id)}
                                className="text-red-600"
                              >
                                <Trash2 className="w-4 h-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={importDocument}>
                <Plus className="w-4 h-4 mr-2" />
                Import File
              </Button>
              <Button onClick={() => setShowLoadDialog(false)}>
                Close
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Export Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-1" />
              Export
              <ChevronDown className="w-3 h-3 ml-1" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => {
              const blob = new Blob([currentContent], { type: 'text/x-tex' });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = 'document.tex';
              a.click();
              URL.revokeObjectURL(url);
            }}>
              <FileText className="w-4 h-4 mr-2" />
              Export as .tex
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => {
              const blob = new Blob([currentContent], { type: 'text/plain' });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = 'document.txt';
              a.click();
              URL.revokeObjectURL(url);
            }}>
              <FileText className="w-4 h-4 mr-2" />
              Export as .txt
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Import Button */}
        <Button variant="outline" size="sm" onClick={importDocument}>
          <Upload className="w-4 h-4 mr-1" />
          Import
        </Button>

        {/* Document Stats */}
        {savedDocuments.length > 0 && (
          <div className="flex items-center gap-2 text-sm text-gray-500 ml-4">
            <Clock className="w-4 h-4" />
            <span>{savedDocuments.length} saved document{savedDocuments.length !== 1 ? 's' : ''}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default FileManager;
