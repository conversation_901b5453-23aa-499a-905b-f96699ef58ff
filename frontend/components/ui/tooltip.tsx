'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';

// Simple Tooltip components without external dependencies
const TooltipProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <>{children}</>;
};

const Tooltip: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <div className="relative inline-block group">{children}</div>;
};

const TooltipTrigger: React.FC<{ asChild?: boolean; children: React.ReactNode }> = ({
  asChild = false,
  children,
}) => {
  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(children);
  }
  return <div>{children}</div>;
};

const TooltipContent: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className,
}) => {
  return (
    <div
      className={cn(
        'absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50',
        className
      )}
    >
      {children}
      <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
    </div>
  );
};

export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };
