import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  RotateCcw,
  FileText,
  Calendar,
  Clock,
  AlertTriangle,
  X
} from "lucide-react";
import { useResumeStore, useCurrentSession, hasResumableSession } from "@/lib/stores/resume-store";
import { useClearResumeCache } from "@/lib/hooks/use-resume-api";

interface ResetSessionPanelProps {
  onReset?: () => void;
  className?: string;
}

export default function ResetSessionPanel({ onReset, className = "" }: ResetSessionPanelProps) {
  const [isResetting, setIsResetting] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const { clearCurrentSession, startNewSession, sessionHistory } = useResumeStore();
  const currentSession = useCurrentSession();
  const clearResumeCache = useClearResumeCache();
  const hasSession = hasResumableSession();

  const handleReset = async () => {
    setIsResetting(true);

    try {
      // Clear the current session
      clearCurrentSession();

      // Clear React Query cache
      clearResumeCache();

      // Start a new session
      startNewSession();

      console.log('[Reset] Session and cache cleared successfully');

      // Call optional callback
      onReset?.();

      // Reload the page to ensure clean state
      window.location.reload();

    } catch (error) {
      console.error('[Reset] Error during reset:', error);
    } finally {
      setIsResetting(false);
      setShowConfirmDialog(false);
    }
  };

  // Don't show reset panel if there's no session data
  if (!hasSession) {
    return null;
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getSessionStatusBadge = () => {
    switch (currentSession.processingStatus) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case 'processing':
        return <Badge className="bg-blue-100 text-blue-800">Processing</Badge>;
      case 'error':
        return <Badge className="bg-red-100 text-red-800">Error</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">Idle</Badge>;
    }
  };

  // Confirmation Dialog (simple modal)
  if (showConfirmDialog) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="max-w-md w-full mx-4">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-amber-500" />
                <h3 className="font-semibold text-gray-900">Clear Resume Session?</h3>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowConfirmDialog(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-2 mb-6">
              <p className="text-sm text-gray-700">
                This will permanently delete your current resume session including:
              </p>
              <ul className="list-disc list-inside space-y-1 text-sm ml-2 text-gray-600">
                <li>Uploaded resume file</li>
                <li>Job description</li>
                <li>Optimization results</li>
                <li>Generated LaTeX code</li>
                <li>Quality scores and insights</li>
              </ul>
              <p className="text-sm font-medium text-red-600 mt-3">
                This action cannot be undone.
              </p>
            </div>

            <div className="flex space-x-3 justify-end">
              <Button
                variant="outline"
                onClick={() => setShowConfirmDialog(false)}
                disabled={isResetting}
              >
                Cancel
              </Button>
              <Button
                onClick={handleReset}
                className="bg-red-600 hover:bg-red-700 text-white"
                disabled={isResetting}
              >
                {isResetting ? 'Clearing...' : 'Clear & Start Over'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <Card className={`border-amber-200 bg-amber-50 ${className}`}>
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-2">
            <FileText className="h-5 w-5 text-amber-600" />
            <h3 className="font-semibold text-amber-900">Resume Session</h3>
            {getSessionStatusBadge()}
          </div>
          <Button
            variant="outline"
            size="sm"
            className="border-amber-300 text-amber-700 hover:bg-amber-100"
            disabled={isResetting}
            onClick={() => setShowConfirmDialog(true)}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Clear & Start Over
          </Button>
        </div>

        {/* Session Info */}
        <div className="space-y-2 text-sm">
          <div className="flex items-center justify-between">
            <span className="text-amber-700">Created:</span>
            <div className="flex items-center space-x-1 text-amber-800">
              <Calendar className="h-4 w-4" />
              <span>{formatDate(currentSession.createdAt)}</span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-amber-700">Last Updated:</span>
            <div className="flex items-center space-x-1 text-amber-800">
              <Clock className="h-4 w-4" />
              <span>{formatDate(currentSession.updatedAt)}</span>
            </div>
          </div>

          {currentSession.uploadedFile && (
            <div className="flex items-center justify-between">
              <span className="text-amber-700">Resume File:</span>
              <span className="text-amber-800 font-medium">{currentSession.uploadedFile.name}</span>
            </div>
          )}

          {currentSession.optimizationResults && (
            <div className="flex items-center justify-between">
              <span className="text-amber-700">Overall Score:</span>
              <Badge className="bg-green-100 text-green-800">
                {currentSession.optimizationResults.quality_metrics.overall_score}/100
              </Badge>
            </div>
          )}

          {sessionHistory.length > 0 && (
            <div className="flex items-center justify-between">
              <span className="text-amber-700">Previous Sessions:</span>
              <Badge className="bg-blue-100 text-blue-800">
                {sessionHistory.length} saved
              </Badge>
            </div>
          )}
        </div>

        {/* Quick actions */}
        <div className="mt-4 pt-3 border-t border-amber-200">
          <p className="text-xs text-amber-600 mb-2">Quick Actions:</p>
          <div className="flex space-x-2">
            {currentSession.processingStatus === 'completed' && (
              <Badge
                variant="outline"
                className="cursor-pointer hover:bg-amber-100 text-xs border-amber-300"
                onClick={() => window.location.hash = '#results'}
              >
                View Results
              </Badge>
            )}

            {currentSession.optimizationResults?.results?.latex_code && (
              <Badge
                variant="outline"
                className="cursor-pointer hover:bg-amber-100 text-xs border-amber-300"
                onClick={() => window.open('/latex-editor-from-optimization', '_blank')}
              >
                Open LaTeX Editor
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
