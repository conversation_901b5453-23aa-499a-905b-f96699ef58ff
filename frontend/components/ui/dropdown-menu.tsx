'use client';

import * as React from 'react';
import { useState } from 'react';
import { cn } from '@/lib/utils';

interface DropdownMenuTriggerProps {
  asChild?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
}

const DropdownMenuTrigger: React.FC<DropdownMenuTriggerProps> = ({
  asChild = false,
  children,
  onClick
}) => {
  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(children as React.ReactElement<{onClick?: () => void}>, { onClick });
  }
  return <button onClick={onClick}>{children}</button>;
};

interface DropdownMenuContentProps {
  children: React.ReactNode;
  align?: 'start' | 'center' | 'end';
  className?: string;
  isOpen?: boolean;
}

const DropdownMenuContent: React.FC<DropdownMenuContentProps> = ({
  children,
  align = 'start',
  className,
  isOpen = false
}) => {
  if (!isOpen) return null;

  const alignClasses = {
    start: 'left-0',
    center: 'left-1/2 transform -translate-x-1/2',
    end: 'right-0',
  };

  return (
    <div
      className={cn(
        'absolute top-full mt-1 min-w-[8rem] bg-white border border-gray-200 rounded-md shadow-lg z-50',
        'dark:bg-gray-800 dark:border-gray-700',
        alignClasses[align],
        className
      )}
    >
      <div className="py-1">{children}</div>
    </div>
  );
};

interface DropdownMenuItemProps {
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
}

const DropdownMenuItem: React.FC<DropdownMenuItemProps> = ({
  children,
  onClick,
  className,
  disabled = false
}) => {
  return (
    <button
      className={cn(
        'w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed',
        className
      )}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
};

interface DropdownMenuSeparatorProps {
  className?: string;
}

const DropdownMenuSeparator: React.FC<DropdownMenuSeparatorProps> = ({ className }) => {
  return (
    <div className={cn('h-px bg-gray-200 dark:bg-gray-700 my-1', className)} />
  );
};

// Enhanced version with state management
interface DropdownMenuProps {
  children: React.ReactNode;
}

const DropdownMenuWithState: React.FC<DropdownMenuProps> = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleOutsideClick = React.useCallback((event: MouseEvent) => {
    const target = event.target as Element;
    if (!target.closest('.dropdown-menu-container')) {
      setIsOpen(false);
    }
  }, []);

  React.useEffect(() => {
    if (isOpen) {
      document.addEventListener('click', handleOutsideClick);
      return () => document.removeEventListener('click', handleOutsideClick);
    }
  }, [isOpen, handleOutsideClick]);

  return (
    <div className="relative inline-block dropdown-menu-container">
      {React.Children.map(children, child => {
        if (React.isValidElement(child)) {
          if (child.type === DropdownMenuTrigger) {
            return React.cloneElement(child as React.ReactElement<DropdownMenuTriggerProps>, {
              onClick: () => setIsOpen(!isOpen)
            });
          }
          if (child.type === DropdownMenuContent) {
            return React.cloneElement(child as React.ReactElement<DropdownMenuContentProps>, {
              isOpen,
              ...(child.props as DropdownMenuContentProps)
            });
          }
          if (child.type === DropdownMenuItem) {
            const itemProps = child.props as DropdownMenuItemProps;
            return React.cloneElement(child as React.ReactElement<DropdownMenuItemProps>, {
              onClick: () => {
                if (itemProps.onClick) itemProps.onClick();
                setIsOpen(false);
              }
            });
          }
        }
        return child;
      })}
    </div>
  );
};

// Use the enhanced version as the default export
export {
  DropdownMenuWithState as DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator
};
