{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@headlessui/react": "^2.2.7", "@hookform/resolvers": "^5.2.1", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@tanstack/query-sync-storage-persister": "^5.83.1", "@tanstack/react-query": "^5.84.2", "@types/pdfjs-dist": "^2.10.377", "ai": "^5.0.8", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.535.0", "monaco-editor": "^0.52.2", "next": "15.4.5", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "pdfjs-dist": "^5.4.149", "react": "19.1.0", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.61.1", "react-pdf": "^10.1.0", "react-resizable-panels": "^3.0.4", "tailwind-merge": "^3.3.1", "zod": "^4.0.14", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}