import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import QueryProvider from "@/lib/providers/query-provider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "ResumeAI Pro - AI-Powered Resume Optimizer",
  description: "Transform your resume with AI-powered optimization and professional LaTeX formatting",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Generate fresh timestamp on each server restart/rebuild
  const buildTimestamp = new Date().toISOString();

  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning={true}
      >
        <QueryProvider>
          {/* Global Version Banner */}
          <div className="w-full bg-gradient-to-r from-purple-600 via-fuchsia-500 to-pink-500 text-white text-center text-sm py-2 font-semibold shadow-md z-50">
            ResumeAI Pro DEV BUILD – LaTeX Editor Integration Active – Build: {buildTimestamp}
          </div>
          {children}
        </QueryProvider>
      </body>
    </html>
  );
}
