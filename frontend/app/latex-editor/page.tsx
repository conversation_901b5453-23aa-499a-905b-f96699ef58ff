'use client';

import dynamic from 'next/dynamic';

// Dynamic import to prevent SSR issues with Monaco Editor
const LaTeXEditorLayout = dynamic(
  () => import('../../components/latex-editor').then((mod) => ({ default: mod.LaTeXEditorLayout })),
  {
    ssr: false,
    loading: () => (
      <div className="h-screen flex items-center justify-center">
        <div className="text-lg text-gray-600">Loading LaTeX Editor...</div>
      </div>
    )
  }
);

export default function LaTeXEditorPage() {
  return (
    <div className="h-screen">
      <LaTeXEditorLayout
        initialContent={`\\documentclass{article}
\\usepackage[utf8]{inputenc}
\\usepackage{amsmath}
\\usepackage{amsfonts}
\\usepackage{amssymb}

\\title{Resume LaTeX Editor Demo}
\\author{Resume AI}
\\date{\\today}

\\begin{document}

\\maketitle

\\section{Welcome}

Welcome to the Resume AI LaTeX Editor! This is a powerful editor with real-time PDF preview.

\\section{Features}

\\begin{itemize}
    \\item Monaco Editor with LaTeX syntax highlighting
    \\item Live PDF preview with zoom controls
    \\item Auto-compilation on content changes
    \\item Resizable panels for optimal viewing
    \\item Mobile-responsive design
\\end{itemize}

\\section{Mathematical Expressions}

Here's an inline math example: $E = mc^2$

And a display equation:
\\begin{equation}
    \\sum_{n=1}^{\\infty} \\frac{1}{n^2} = \\frac{\\pi^2}{6}
\\end{equation}

\\section{Resume Example}

\\textbf{John Doe} \\\\
Software Engineer \\\\
Email: <EMAIL> \\\\
Phone: (*************

\\subsection{Experience}

\\textbf{Senior Software Engineer} \\\\
Tech Company Inc. \\hfill 2020 - Present
\\begin{itemize}
    \\item Led development of web applications using React and Node.js
    \\item Improved system performance by 40\\% through optimization
    \\item Mentored junior developers and conducted code reviews
\\end{itemize}

\\textbf{Software Developer} \\\\
Startup LLC \\hfill 2018 - 2020
\\begin{itemize}
    \\item Built full-stack applications using modern web technologies
    \\item Collaborated with cross-functional teams in Agile environment
    \\item Implemented automated testing and deployment pipelines
\\end{itemize}

\\subsection{Education}

\\textbf{Bachelor of Science in Computer Science} \\\\
University of Technology \\hfill 2014 - 2018

\\subsection{Skills}

\\textbf{Programming Languages:} JavaScript, TypeScript, Python, Java \\\\
\\textbf{Frameworks:} React, Node.js, Express, Django \\\\
\\textbf{Tools:} Git, Docker, AWS, MongoDB, PostgreSQL

\\end{document}`}
        className="bg-gray-50 dark:bg-gray-900"
        editorProps={{
          theme: 'vs-dark',
          fontSize: 14,
          minimap: true,
          wordWrap: true,
        }}
        pdfViewerProps={{
          showZoomControls: true,
          showNavigation: true,
          initialZoom: 1.0,
        }}
      />
    </div>
  );
}
