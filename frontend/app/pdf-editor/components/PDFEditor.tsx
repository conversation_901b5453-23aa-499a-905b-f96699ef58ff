'use client';

import React, { useState, useCallback } from 'react';
import { Document, Page } from 'react-pdf';
import { Button } from "../../../components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../../../components/ui/card";
import { Input } from "../../../components/ui/input";
import { Label } from "../../../components/ui/label";
import { Textarea } from "../../../components/ui/textarea";
import { Badge } from "../../../components/ui/badge";
import { Progress } from "../../../components/ui/progress";
import {
  Upload,
  Search,
  Replace,
  Download,
  ChevronLeft,
  ChevronRight,
  AlertCircle,
  CheckCircle,
  Loader2,
  FileText
} from "lucide-react";
import { fileToArrayBuffer, findAndReplaceText, downloadPDF } from '../utils/pdf-utils';
import { <PERSON><PERSON>, AlertDescription } from "../../../components/ui/alert";

// Import required CSS for react-pdf
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';

// Import PDF worker setup
import '../utils/pdf-worker-setup';

type PDFEditorProps = Record<string, never>;

export function PDFEditor({}: PDFEditorProps) {
  // State management
  const [pdfFile, setPdfFile] = useState<File | null>(null);
  const [pdfArrayBuffer, setPdfArrayBuffer] = useState<ArrayBuffer | null>(null);
  const [editedPdfBlob, setEditedPdfBlob] = useState<Blob | null>(null);
  const [numPages, setNumPages] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.0);

  // Editing state
  const [findText, setFindText] = useState<string>('');
  const [replaceText, setReplaceText] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [progress, setProgress] = useState<number>(0);
  const [lastOperation, setLastOperation] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');

  // Clear messages after delay
  const clearMessages = useCallback(() => {
    setTimeout(() => {
      setError('');
      setSuccess('');
    }, 5000);
  }, []);

  // Handle file upload
  const handleFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.type !== 'application/pdf') {
      setError('Please select a PDF file.');
      clearMessages();
      return;
    }

    setIsProcessing(true);
    setProgress(25);

    try {
      setPdfFile(file);
      const arrayBuffer = await fileToArrayBuffer(file);
      setPdfArrayBuffer(arrayBuffer);

      // Create initial blob for viewing
      const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
      setEditedPdfBlob(blob);

      setProgress(100);
      setSuccess('PDF loaded successfully!');
      clearMessages();
    } catch {
      setError('Failed to load PDF file. Please try again.');
      clearMessages();
    } finally {
      setIsProcessing(false);
      setProgress(0);
    }
  }, [clearMessages]);

  // Handle PDF document load success
  const onDocumentLoadSuccess = (document: { numPages: number }): void => {
    setNumPages(document.numPages);
    setCurrentPage(1);
  };

  // Navigation functions
  const goToPreviousPage = () => {
    setCurrentPage(prev => Math.max(1, prev - 1));
  };

  const goToNextPage = () => {
    setCurrentPage(prev => Math.min(numPages, prev + 1));
  };

  // Handle find and replace operation
  const handleFindAndReplace = useCallback(async () => {
    if (!pdfArrayBuffer || !findText.trim() || !replaceText.trim()) {
      setError('Please enter both find and replace text.');
      clearMessages();
      return;
    }

    setIsProcessing(true);
    setProgress(10);
    setLastOperation(`Searching for "${findText}"...`);

    try {
      setProgress(30);
      setLastOperation('Processing text replacement...');

      const editedPdfBytes = await findAndReplaceText(
        pdfArrayBuffer,
        findText.trim(),
        replaceText.trim()
      );

      setProgress(70);
      setLastOperation('Creating new PDF...');

      // Create new blob with edited content
      const newBlob = new Blob([editedPdfBytes as BlobPart], { type: 'application/pdf' });
      setEditedPdfBlob(newBlob);

      // Update array buffer for future operations
      setPdfArrayBuffer(await newBlob.arrayBuffer());

      setProgress(100);
      setSuccess(`Successfully replaced "${findText}" with "${replaceText}"`);
      setFindText('');
      setReplaceText('');
      clearMessages();
    } catch (err) {
      console.error('Find and replace error:', err);
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(`Find and replace failed: ${errorMessage}`);
      clearMessages();
    } finally {
      setIsProcessing(false);
      setProgress(0);
      setLastOperation('');
    }
  }, [pdfArrayBuffer, findText, replaceText, clearMessages]);

  // Handle download
  const handleDownload = useCallback(async () => {
    if (!editedPdfBlob) {
      setError('No PDF available for download.');
      clearMessages();
      return;
    }

    try {
      const arrayBuffer = await editedPdfBlob.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      const filename = pdfFile ? `edited_${pdfFile.name}` : 'edited_document.pdf';

      downloadPDF(uint8Array, filename);
      setSuccess('PDF downloaded successfully!');
      clearMessages();
    } catch {
      setError('Failed to download PDF. Please try again.');
      clearMessages();
    }
  }, [editedPdfBlob, pdfFile, clearMessages]);

  return (
    <div className="w-full h-full">
      {/* File Upload Section */}
      {!pdfFile && (
        <div className="p-8 text-center">
          <div className="max-w-md mx-auto">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 hover:border-blue-400 transition-colors">
              <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Upload PDF File</h3>
              <p className="text-sm text-gray-500 mb-4">
                Select a PDF file to start editing
              </p>
              <input
                type="file"
                accept=".pdf"
                onChange={handleFileUpload}
                className="hidden"
                id="pdf-upload"
                disabled={isProcessing}
              />
              <Label htmlFor="pdf-upload">
                <Button variant="outline" className="cursor-pointer" disabled={isProcessing}>
                  {isProcessing ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Loading...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      Choose PDF File
                    </>
                  )}
                </Button>
              </Label>
            </div>
          </div>
        </div>
      )}

      {/* Progress Bar */}
      {isProcessing && (
        <div className="p-4 bg-blue-50">
          <div className="max-w-md mx-auto">
            <div className="flex items-center space-x-2 mb-2">
              <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
              <span className="text-sm text-blue-800">{lastOperation || 'Processing...'}</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        </div>
      )}

      {/* Messages */}
      {error && (
        <Alert className="mx-4 mb-4 border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="mx-4 mb-4 border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">{success}</AlertDescription>
        </Alert>
      )}

      {/* Main Editor Interface */}
      {pdfFile && editedPdfBlob && (
        <div className="flex h-full">
          {/* Left Panel - Editing Tools */}
          <div className="w-80 border-r border-gray-200 bg-gray-50 p-4 overflow-y-auto">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center">
                  <Search className="h-5 w-5 mr-2 text-blue-600" />
                  Find & Replace
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="find-text" className="text-sm font-medium">
                    Find Text
                  </Label>
                  <Input
                    id="find-text"
                    type="text"
                    placeholder="Enter text to find..."
                    value={findText}
                    onChange={(e) => setFindText(e.target.value)}
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="replace-text" className="text-sm font-medium">
                    Replace With
                  </Label>
                  <Textarea
                    id="replace-text"
                    placeholder="Enter replacement text..."
                    value={replaceText}
                    onChange={(e) => setReplaceText(e.target.value)}
                    className="mt-1 min-h-[80px]"
                  />
                </div>

                <Button
                  onClick={handleFindAndReplace}
                  disabled={!findText.trim() || !replaceText.trim() || isProcessing}
                  className="w-full"
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Replace className="h-4 w-4 mr-2" />
                      Find & Replace
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* File Info */}
            <Card className="mt-4">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-gray-600" />
                  Document Info
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">File:</span>
                  <span className="font-medium truncate ml-2">{pdfFile.name}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Pages:</span>
                  <Badge variant="secondary">{numPages}</Badge>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Size:</span>
                  <span className="font-medium">{Math.round(pdfFile.size / 1024)} KB</span>
                </div>
              </CardContent>
            </Card>

            {/* Download Section */}
            <Card className="mt-4">
              <CardContent className="pt-6">
                <Button
                  onClick={handleDownload}
                  variant="default"
                  className="w-full"
                  disabled={!editedPdfBlob}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download PDF
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Right Panel - PDF Viewer */}
          <div className="flex-1 flex flex-col">
            {/* PDF Navigation */}
            <div className="border-b border-gray-200 bg-white p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={goToPreviousPage}
                    disabled={currentPage <= 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  <span className="text-sm text-gray-600">
                    Page <span className="font-medium">{currentPage}</span> of <span className="font-medium">{numPages}</span>
                  </span>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={goToNextPage}
                    disabled={currentPage >= numPages}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>

                <div className="flex items-center space-x-2">
                  <Label htmlFor="scale-select" className="text-sm text-gray-600">
                    Zoom:
                  </Label>
                  <select
                    id="scale-select"
                    value={scale}
                    onChange={(e) => setScale(Number(e.target.value))}
                    className="text-sm border border-gray-300 rounded px-2 py-1"
                  >
                    <option value={0.5}>50%</option>
                    <option value={0.75}>75%</option>
                    <option value={1.0}>100%</option>
                    <option value={1.25}>125%</option>
                    <option value={1.5}>150%</option>
                    <option value={2.0}>200%</option>
                  </select>
                </div>
              </div>
            </div>

            {/* PDF Display */}
            <div className="flex-1 bg-gray-100 p-4 overflow-auto">
              <div className="flex justify-center">
                <div className="bg-white shadow-lg">
                  <Document
                    file={editedPdfBlob}
                    onLoadSuccess={onDocumentLoadSuccess}
                    className="max-w-full"
                  >
                    <Page
                      pageNumber={currentPage}
                      scale={scale}
                      className="max-w-full"
                    />
                  </Document>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default PDFEditor;