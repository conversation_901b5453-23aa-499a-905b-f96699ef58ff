'use client';

import React, { useState, useEffect } from 'react';

interface DynamicPDFViewerProps {
  file: Blob | null;
  currentPage: number;
  scale: number;
  onLoadSuccess: (document: { numPages: number }) => void;
  className?: string;
}

export function DynamicPDFViewer({
  file,
  currentPage,
  scale,
  onLoadSuccess,
  className = ''
}: DynamicPDFViewerProps) {
  const [ReactPDFComponents, setReactPDFComponents] = useState<{
    Document: React.ComponentType<{
      file: Blob | null;
      onLoadSuccess: (document: { numPages: number }) => void;
      className?: string;
      children?: React.ReactNode;
    }>;
    Page: React.ComponentType<{
      pageNumber: number;
      scale: number;
      className?: string;
    }>;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadReactPDF = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Dynamically import react-pdf components
        const reactPdf = await import('react-pdf');

        // Set up worker
        if (typeof window !== 'undefined' && !reactPdf.pdfjs.GlobalWorkerOptions.workerSrc) {
          reactPdf.pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${reactPdf.pdfjs.version}/build/pdf.worker.min.mjs`;
        }

        setReactPDFComponents({
          Document: reactPdf.Document,
          Page: reactPdf.Page
        });
      } catch (err) {
        console.error('Failed to load react-pdf:', err);
        setError('Failed to load PDF viewer');
      } finally {
        setIsLoading(false);
      }
    };

    loadReactPDF();
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-600">Loading PDF viewer...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-red-600">{error}</div>
      </div>
    );
  }

  if (!ReactPDFComponents || !file) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-600">No PDF to display</div>
      </div>
    );
  }

  const { Document, Page } = ReactPDFComponents;

  return (
    <Document
      file={file}
      onLoadSuccess={onLoadSuccess}
      className={className}
    >
      <Page
        pageNumber={currentPage}
        scale={scale}
        className="max-w-full"
      />
    </Document>
  );
}

export default DynamicPDFViewer;
