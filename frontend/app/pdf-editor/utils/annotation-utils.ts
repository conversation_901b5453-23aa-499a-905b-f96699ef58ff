import { PDFDocument, rgb } from 'pdf-lib';

export interface AnnotationData {
  pageIndex: number;
  x: number;
  y: number;
  width: number;
  height: number;
  text: string;
  color: { r: number; g: number; b: number };
}

/**
 * Add text annotation to PDF
 */
export async function addTextAnnotation(
  pdfData: ArrayBuffer,
  annotation: AnnotationData
): Promise<Uint8Array> {
  try {
    const pdfDoc = await PDFDocument.load(pdfData);
    const pages = pdfDoc.getPages();

    if (annotation.pageIndex >= pages.length) {
      throw new Error('Page index out of range');
    }

    const page = pages[annotation.pageIndex];
    const pageHeight = page.getHeight();

    // Convert coordinates (assuming Y-axis needs flipping)
    const pdfLibY = pageHeight - annotation.y - annotation.height;

    // Add background rectangle for annotation
    page.drawRectangle({
      x: annotation.x,
      y: pdfLibY,
      width: annotation.width,
      height: annotation.height,
      color: rgb(annotation.color.r, annotation.color.g, annotation.color.b),
      opacity: 0.3,
      borderColor: rgb(annotation.color.r, annotation.color.g, annotation.color.b),
      borderWidth: 1
    });

    // Add text
    page.drawText(annotation.text, {
      x: annotation.x + 5,
      y: pdfLibY + annotation.height / 2,
      size: 10,
      color: rgb(0, 0, 0)
    });

    const pdfBytes = await pdfDoc.save();
    return pdfBytes;
  } catch (error) {
    console.error('Error adding annotation:', error);
    throw error;
  }
}

/**
 * Add highlight to text
 */
export async function addHighlight(
  pdfData: ArrayBuffer,
  highlight: Omit<AnnotationData, 'text'>
): Promise<Uint8Array> {
  try {
    const pdfDoc = await PDFDocument.load(pdfData);
    const pages = pdfDoc.getPages();

    if (highlight.pageIndex >= pages.length) {
      throw new Error('Page index out of range');
    }

    const page = pages[highlight.pageIndex];
    const pageHeight = page.getHeight();

    // Convert coordinates
    const pdfLibY = pageHeight - highlight.y - highlight.height;

    // Add highlight rectangle
    page.drawRectangle({
      x: highlight.x,
      y: pdfLibY,
      width: highlight.width,
      height: highlight.height,
      color: rgb(highlight.color.r, highlight.color.g, highlight.color.b),
      opacity: 0.4
    });

    const pdfBytes = await pdfDoc.save();
    return pdfBytes;
  } catch (error) {
    console.error('Error adding highlight:', error);
    throw error;
  }
}