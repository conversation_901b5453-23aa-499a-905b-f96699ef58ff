// PDF.js worker setup - isolated to prevent webpack issues
import { pdfjs } from 'react-pdf';

// Use a stable CDN URL to avoid webpack module resolution issues
const PDFJS_WORKER_URL = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;

// Set up the worker only once
let workerSetup = false;

export function setupPDFWorker() {
  if (!workerSetup && typeof window !== 'undefined') {
    pdfjs.GlobalWorkerOptions.workerSrc = PDFJS_WORKER_URL;
    workerSetup = true;
  }
}

// Auto-setup when this module is imported
setupPDFWorker();
