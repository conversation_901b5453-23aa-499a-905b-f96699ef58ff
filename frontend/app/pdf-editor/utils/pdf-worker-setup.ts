// PDF.js worker setup - isolated to prevent webpack issues
// Only import pdfjs on client side to avoid SSR issues
let pdfjs: any = null;

// Use a stable CDN URL to avoid webpack module resolution issues
const PDFJS_WORKER_URL = '//unpkg.com/pdfjs-dist@4.4.168/build/pdf.worker.min.mjs';

// Set up the worker only once
let workerSetup = false;

export function setupPDFWorker() {
  if (!workerSetup && typeof window !== 'undefined') {
    try {
      // Dynamically import pdfjs only on client side
      if (!pdfjs) {
        const reactPdf = require('react-pdf');
        pdfjs = reactPdf.pdfjs;
      }

      if (pdfjs && pdfjs.GlobalWorkerOptions) {
        pdfjs.GlobalWorkerOptions.workerSrc = PDFJS_WORKER_URL;
        workerSetup = true;
      }
    } catch (error) {
      console.warn('Failed to setup PDF worker:', error);
    }
  }
}

// Don't auto-setup - let components call it when needed
