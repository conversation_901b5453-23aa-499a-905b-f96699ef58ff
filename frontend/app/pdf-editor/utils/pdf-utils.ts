import { PDFDocument, rgb } from 'pdf-lib';
import { getDocument } from 'pdfjs-dist';
import { PositionData } from '../types';

// Import PDF worker setup
import './pdf-worker-setup';

/**
 * Extract text positions from PDF using PDF.js
 */
export async function extractTextPositions(
  pdfData: A<PERSON>yBuffer,
  findText: string
): Promise<PositionData[]> {
  try {
    // Load the PDF document with PDF.js
    const loadingTask = getDocument({ data: pdfData });
    const pdf = await loadingTask.promise;
    const textPositions: PositionData[] = [];

    // Iterate through each page in the PDF
    for (let pageIndex = 0; pageIndex < pdf.numPages; pageIndex++) {
      const page = await pdf.getPage(pageIndex + 1);
      const textContent = await page.getTextContent();

      // Process each text item on the page
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      textContent.items.forEach((item: any) => {
        if (item.str && item.str.includes(findText)) {
          // Get the transformation matrix for positioning
          const transform = item.transform;

          // Extract position and styling information
          const x = transform[4];
          const y = transform[5];
          const fontSize = Math.abs(transform[0]); // Font size from scale
          const width = item.width || fontSize * item.str.length * 0.6; // Estimate width
          const height = item.height || fontSize;

          // Default color (black)
          const color = { r: 0, g: 0, b: 0 };

          textPositions.push({
            pageIndex,
            x,
            y,
            width,
            height,
            fontSize,
            color,
            str: item.str
          });
        }
      });
    }

    return textPositions;
  } catch (error) {
    console.error('Error extracting text positions:', error);
    throw new Error('Failed to extract text positions from PDF');
  }
}

/**
 * Apply find and replace operation to PDF using PDF-lib
 */
export async function findAndReplaceText(
  pdfData: ArrayBuffer,
  findText: string,
  replaceText: string
): Promise<Uint8Array> {
  try {
    // Load the PDF document with PDF-lib
    const pdfDoc = await PDFDocument.load(pdfData);

    // Extract text positions using PDF.js
    const textPositions = await extractTextPositions(pdfData, findText);

    if (textPositions.length === 0) {
      throw new Error(`Text "${findText}" not found in the PDF`);
    }

    const pages = pdfDoc.getPages();

    // Process each found text position
    textPositions.forEach(({ pageIndex, x, y, width, height, fontSize, str }) => {
      const page = pages[pageIndex];
      const pageHeight = page.getHeight();

      // Convert PDF.js coordinates to PDF-lib coordinates (Y-axis is flipped)
      const pdfLibY = pageHeight - y - height;

      try {
        // Step 1: Cover existing text with white rectangle
        page.drawRectangle({
          x: x,
          y: pdfLibY,
          width: width,
          height: height,
          color: rgb(1, 1, 1), // White background
          borderColor: rgb(1, 1, 1), // White border
          borderWidth: 0
        });

        // Step 2: Draw new text in the same position
        const newText = str.replace(new RegExp(findText, 'g'), replaceText);
        page.drawText(newText, {
          x: x,
          y: pdfLibY,
          size: fontSize,
          color: rgb(0, 0, 0) // Black text
        });
      } catch (error) {
        console.warn(`Failed to replace text at position (${x}, ${y}):`, error);
      }
    });

    // Save and return the modified PDF
    const pdfBytes = await pdfDoc.save();
    return pdfBytes;
  } catch (error) {
    console.error('Error in findAndReplaceText:', error);
    throw error;
  }
}

/**
 * Download PDF blob as file
 */
export function downloadPDF(pdfBytes: Uint8Array, filename: string = 'edited-document.pdf') {
  try {
    // Create blob from Uint8Array
    const blob = new Blob([pdfBytes as BlobPart], { type: 'application/pdf' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();

    // Cleanup
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading PDF:', error);
    throw new Error('Failed to download PDF');
  }
}

/**
 * Convert file to ArrayBuffer
 */
export function fileToArrayBuffer(file: File): Promise<ArrayBuffer> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        resolve(e.target.result as ArrayBuffer);
      } else {
        reject(new Error('Failed to read file'));
      }
    };
    reader.onerror = () => reject(new Error('File reading error'));
    reader.readAsArrayBuffer(file);
  });
}