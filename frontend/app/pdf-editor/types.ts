// Types for PDF editing functionality
export interface PositionData {
  pageIndex: number;
  x: number;
  y: number;
  width: number;
  height: number;
  fontSize: number;
  color: { r: number; g: number; b: number };
  str: string;
}

export interface EditOperation {
  id: string;
  type: 'find-replace' | 'annotation' | 'form-edit';
  findText?: string;
  replaceText?: string;
  positions?: PositionData[];
  timestamp: Date;
}