'use client';

import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Button } from "../../components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../../components/ui/card";
import { ArrowLeft, Home, AlertCircle, FileText, Edit3 } from "lucide-react";
import { Badge } from "../../components/ui/badge";

// Dynamic import to prevent SSR issues with PDF.js
const PDFEditor = dynamic(
  () => import('./components/PDFEditor'),
  {
    ssr: false,
    loading: () => (
      <div className="h-screen flex items-center justify-center">
        <div className="text-lg text-gray-600">Loading PDF Editor...</div>
      </div>
    )
  }
);

export default function PDFEditorPage() {
  const router = useRouter();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-lg text-gray-600">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.back()}
                className="text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>

              <div className="flex items-center space-x-2">
                <FileText className="h-6 w-6 text-blue-600" />
                <h1 className="text-2xl font-bold text-gray-900">PDF Editor</h1>
                <Badge variant="secondary" className="ml-2">
                  Free Tool
                </Badge>
              </div>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/')}
              className="text-gray-600 hover:text-gray-900"
            >
              <Home className="h-4 w-4 mr-2" />
              Home
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Info Section */}
        <Card className="mb-6 border-blue-200 bg-blue-50">
          <CardHeader className="pb-4">
            <div className="flex items-center space-x-2">
              <Edit3 className="h-5 w-5 text-blue-600" />
              <CardTitle className="text-lg text-blue-900">How to Use the PDF Editor</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="text-sm text-blue-800">
            <div className="grid md:grid-cols-3 gap-4">
              <div className="flex items-start space-x-2">
                <div className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-semibold">1</div>
                <div>
                  <p className="font-medium">Upload PDF</p>
                  <p className="text-blue-700">Select and upload your PDF file to start editing</p>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <div className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-semibold">2</div>
                <div>
                  <p className="font-medium">Find & Replace</p>
                  <p className="text-blue-700">Enter text to find and its replacement in the editing panel</p>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <div className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-semibold">3</div>
                <div>
                  <p className="font-medium">Download</p>
                  <p className="text-blue-700">Download your edited PDF with all changes applied</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* PDF Editor Component */}
        <Card className="shadow-lg border-0">
          <CardContent className="p-0">
            <PDFEditor />
          </CardContent>
        </Card>

        {/* Features Info */}
        <Card className="mt-6 border-gray-200">
          <CardHeader>
            <CardTitle className="text-lg text-gray-900 flex items-center">
              <AlertCircle className="h-5 w-5 mr-2 text-amber-500" />
              Current Features & Limitations
            </CardTitle>
          </CardHeader>
          <CardContent className="text-sm text-gray-700 space-y-3">
            <div>
              <p className="font-medium text-green-700 mb-1">✅ What you can do:</p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>Find and replace text in PDFs</li>
                <li>Edit form fields (if present)</li>
                <li>Add annotations and comments</li>
                <li>Download edited PDFs</li>
                <li>Works entirely in your browser (no data sent to servers)</li>
              </ul>
            </div>
            <div>
              <p className="font-medium text-amber-700 mb-1">⚠️ Current limitations:</p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>Cannot modify text formatting (font, size, color) of existing text</li>
                <li>Cannot insert text in middle of paragraphs (overlay-based editing)</li>
                <li>Works best with text-based PDFs (not scanned images)</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}