'use client';

import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { useEffect, useState, Suspense } from 'react';
import { Button } from "../../components/ui/button";
import { Card, CardContent } from "../../components/ui/card";
import { useLatexCode } from "../../lib/stores/resume-store";
import { ArrowLeft, Home, AlertCircle } from "lucide-react";

// Dynamic import to prevent SSR issues with Monaco Editor
const LaTeXEditorLayout = dynamic(
  () => import('../../components/latex-editor').then((mod) => ({ default: mod.LaTeXEditorLayout })),
  {
    ssr: false,
    loading: () => (
      <div className="h-screen flex items-center justify-center">
        <div className="text-lg text-gray-600">Loading LaTeX Editor...</div>
      </div>
    )
  }
);

// Default fallback LaTeX content
const DEFAULT_LATEX_CONTENT = `\\documentclass{article}
\\usepackage[utf8]{inputenc}
\\usepackage{geometry}
\\usepackage{enumitem}
\\usepackage{xcolor}

\\geometry{
    a4paper,
    left=0.75in,
    right=0.75in,
    top=1in,
    bottom=1in
}

\\title{AI-Generated Resume}
\\author{Resume AI}
\\date{\\today}

\\begin{document}

\\section{Notice}
No LaTeX content was found in your session. Please return to the optimization results and try again.

\\subsection{Troubleshooting}
\\begin{itemize}
    \\item Make sure you have completed the resume optimization process
    \\item Check that the optimization was successful
    \\item Try refreshing the page and running the optimization again
\\end{itemize}

\\end{document}`;

// Component to handle LaTeX content loading from store
function LaTeXEditorFromOptimizationContent() {
  const router = useRouter();
  const latexCodeFromStore = useLatexCode();
  const [latexContent, setLatexContent] = useState<string>(DEFAULT_LATEX_CONTENT);
  const [isLoading, setIsLoading] = useState(true);
  const [hasValidContent, setHasValidContent] = useState(false);

  useEffect(() => {
    if (latexCodeFromStore && latexCodeFromStore.length > 0) {
      console.log('[LaTeX Editor] Loading content from store:', latexCodeFromStore.length, 'characters');
      setLatexContent(latexCodeFromStore);
      setHasValidContent(true);
    } else {
      console.warn('[LaTeX Editor] No LaTeX content found in store');
      setLatexContent(DEFAULT_LATEX_CONTENT);
      setHasValidContent(false);
    }
    setIsLoading(false);
  }, [latexCodeFromStore]);

  const handleGoBack = () => {
    router.back();
  };

  const handleGoHome = () => {
    router.push('/');
  };

  const handleGoToOptimization = () => {
    router.push('/?step=optimization');
  };

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-lg text-gray-600">Loading AI-Generated LaTeX...</div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col">
      {/* Navigation Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleGoBack}
              className="text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div className="h-6 w-px bg-gray-300"></div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleGoHome}
              className="text-gray-600 hover:text-gray-900"
            >
              <Home className="h-4 w-4 mr-2" />
              Home
            </Button>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-gray-700">
              {hasValidContent ? 'AI-Generated Resume LaTeX' : 'No Content Available'}
            </span>
            {hasValidContent ? (
              <div className="h-2 w-2 bg-green-500 rounded-full"></div>
            ) : (
              <div className="h-2 w-2 bg-red-500 rounded-full"></div>
            )}
          </div>
        </div>
      </div>

      {/* Warning for missing content */}
      {!hasValidContent && (
        <div className="p-4 bg-yellow-50 border-b border-yellow-200">
          <Card className="border-yellow-300 bg-yellow-100">
            <CardContent className="p-4">
              <div className="flex items-start space-x-3">
                <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                <div className="flex-1">
                  <h3 className="font-medium text-yellow-800 mb-2">No LaTeX Content Found</h3>
                  <p className="text-sm text-yellow-700 mb-3">
                    It appears you haven&apos;t completed the resume optimization process yet, or the LaTeX content couldn&apos;t be loaded from your session.
                  </p>
                  <Button
                    onClick={handleGoToOptimization}
                    size="sm"
                    className="bg-yellow-600 hover:bg-yellow-700 text-white"
                  >
                    Go to Resume Optimization
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* LaTeX Editor */}
      <div className="flex-1">
        <LaTeXEditorLayout
          initialContent={latexContent}
          className="bg-gray-50 dark:bg-gray-900"
          editorProps={{
            theme: 'vs-dark',
            fontSize: 14,
            minimap: true,
            wordWrap: true,
            automaticLayout: true,
            readOnly: false,
          }}
        />
      </div>
    </div>
  );
}

export default function LaTeXEditorFromOptimizationPage() {
  return (
    <Suspense fallback={
      <div className="h-screen flex items-center justify-center">
        <div className="text-lg text-gray-600">Loading LaTeX Editor...</div>
      </div>
    }>
      <LaTeXEditorFromOptimizationContent />
    </Suspense>
  );
}
