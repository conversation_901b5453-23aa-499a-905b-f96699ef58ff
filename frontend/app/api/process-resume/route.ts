import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    console.log('Processing resume with data:', {
      hasResumeText: !!data.resume_text,
      hasJobDescription: !!data.job_description
    });

    // Mock response structure that matches what your frontend expects
    const mockResponse = {
      success: true,
      job_id: `job_${Date.now()}`,
      status: 'completed',
      processing_time: 45,
      results: {
        parsed_resume: {
          contact_info: {
            name: "<PERSON>",
            email: "<EMAIL>",
            phone: "+****************",
            address: "123 Main St, City, State 12345",
            linkedin: "linkedin.com/in/johndoe"
          },
          summary: "Experienced professional with expertise in software development.",
          experience: [
            {
              company: "Tech Corp",
              position: "Senior Developer",
              duration: "2021 - Present",
              location: "San Francisco, CA",
              achievements: [
                "Led development of key features",
                "Improved system performance by 40%",
                "Mentored junior developers"
              ]
            }
          ],
          education: [
            {
              institution: "University of Technology",
              degree: "BS Computer Science",
              year: "2020"
            }
          ],
          skills: ["JavaScript", "TypeScript", "React", "Node.js"]
        },
        analysis: {
          overall_score: 85,
          strengths: [
            "Strong technical background",
            "Relevant experience",
            "Good education foundation"
          ],
          weaknesses: [
            "Could add more quantified achievements",
            "Missing some industry keywords"
          ],
          recommendations: [
            "Add more metrics to achievements",
            "Include industry-specific keywords",
            "Expand on recent projects"
          ]
        },
        optimized_resume: {
          contact_info: {
            name: "John Doe",
            email: "<EMAIL>",
            phone: "+****************",
            address: "123 Main St, City, State 12345",
            linkedin: "linkedin.com/in/johndoe"
          },
          professional_summary: "Results-driven Software Engineer with 3+ years of experience developing scalable web applications. Proven track record of improving system performance by 40% and leading cross-functional teams. Expertise in modern JavaScript frameworks and cloud technologies.",
          experience: [
            {
              company: "Tech Corp",
              position: "Senior Software Engineer",
              duration: "2021 - Present",
              location: "San Francisco, CA",
              achievements: [
                "Led development of customer-facing features serving 100K+ users daily",
                "Optimized database queries resulting in 40% performance improvement",
                "Mentored 5 junior developers, improving team productivity by 25%",
                "Implemented CI/CD pipelines reducing deployment time by 60%"
              ]
            }
          ],
          education: [
            {
              institution: "University of Technology",
              degree: "Bachelor of Science in Computer Science",
              year: "2020",
              honors: ["Magna Cum Laude", "Dean's List"]
            }
          ],
          skills: {
            technical: ["JavaScript", "TypeScript", "React", "Node.js", "AWS", "Docker", "MongoDB"],
            soft: ["Leadership", "Communication", "Problem Solving"],
            languages: ["English"],
            certifications: []
          },
          certifications: [],
          additional_sections: {
            projects: [],
            publications: [],
            awards: [],
            volunteer: []
          }
        },
        latex_code: `\\documentclass[letterpaper,11pt]{article}
\\usepackage[utf8]{inputenc}
\\usepackage{geometry}
\\geometry{margin=1in}

\\begin{document}

\\begin{center}
{\\Large \\textbf{John Doe}}\\\\
\\vspace{2pt}
<EMAIL> $|$ +**************** $|$ San Francisco, CA
\\end{center}

\\section*{Professional Summary}
Results-driven Software Engineer with 3+ years of experience developing scalable web applications...

\\section*{Experience}
\\textbf{Senior Software Engineer} \\hfill 2021 - Present\\\\
\\textit{Tech Corp, San Francisco, CA}
\\begin{itemize}
\\item Led development of customer-facing features serving 100K+ users daily
\\item Optimized database queries resulting in 40\\% performance improvement
\\end{itemize}

\\end{document}`,
        metadata: {
          processing_time: "45 seconds",
          optimization_level: "professional",
          ats_systems_optimized_for: ["Workday", "Taleo", "Greenhouse"],
          industry_focus: "Technology",
          target_role: "Software Engineer",
          ai_model_used: "mock-api",
          optimization_strategy: "comprehensive",
          processed_at: new Date().toISOString(),
          job_id: `job_${Date.now()}`,
          method: "nextjs_api_route",
          api_calls: 1,
          status: "success"
        }
      },
      download_info: {
        pdf_available: true,
        download_url: `/api/download/job_${Date.now()}`,
        file_format: 'PDF',
        generation_method: 'LaTeX + pdflatex'
      },
      quality_metrics: {
        overall_score: 85,
        ats_compatibility: 88,
        job_match: 82,
        content_quality: 87,
        improvement_count: 5
      }
    };

    // Add CORS headers
    return NextResponse.json(mockResponse, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error('Error processing resume:', error);
    return NextResponse.json(
      { error: 'Failed to process resume' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
