{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "regions": ["sfo1"], "env": {"NEXT_PUBLIC_API_URL": "https://resume-ai-backend.koyeb.app", "NEXT_PUBLIC_MAX_FILE_SIZE_MB": "10", "NEXT_PUBLIC_ENVIRONMENT": "production"}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "installCommand": "npm install", "builds": [{"src": "package.json", "use": "@vercel/next"}]}