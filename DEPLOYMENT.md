# 🚀 Deployment Guide

This guide walks you through deploying ResumeAI Pro with the backend on Fly.io and frontend on Vercel.

## 📋 Prerequisites

- GitHub account
- Fly.io account (free tier available)
- Vercel account (free tier available)
- Git installed locally

## 🔄 Step 1: Push to GitHub

First, let's get your code on GitHub:

```bash
# Initialize git if not already done
git init

# Add all files
git add .

# Commit changes
git commit -m "Initial commit: ResumeAI Pro ready for deployment"

# Add your GitHub remote (replace with your repo URL)
git remote add origin https://github.com/yourusername/resume-ai-pro.git

# Push to GitHub
git push -u origin main
```

## 🛠️ Step 2: Backend Deployment (Fly.io)

### 2.1 Install Fly CLI
```bash
curl -L https://fly.io/install.sh | sh
```

### 2.2 Login to Fly.io
```bash
fly auth login
```

### 2.3 Setup Backend
```bash
cd backend

# Create Fly.io app
fly launch --no-deploy --name resume-ai-backend --region sjc

# Create persistent storage
fly volumes create resume_storage --region sjc --size 10

# Set environment secrets
fly secrets set GEMINI_API_KEY=your_actual_gemini_api_key
fly secrets set GOOGLE_API_KEY=your_actual_google_api_key
fly secrets set ALLOWED_ORIGINS=https://your-domain.vercel.app

# Deploy
fly deploy
```

### 2.4 Verify Backend
```bash
# Check if it's running
curl https://resume-ai-backend.fly.dev/api/health

# View logs
fly logs
```

## 🌐 Step 3: Frontend Deployment (Vercel)

### 3.1 Connect to Vercel
1. Go to [vercel.com](https://vercel.com)
2. Sign up/login with GitHub
3. Click "New Project"
4. Import your GitHub repository
5. Select the `frontend` folder as the root directory

### 3.2 Configure Environment Variables
In Vercel dashboard, add these environment variables:

```bash
NEXT_PUBLIC_API_URL=https://resume-ai-backend.fly.dev
NEXT_PUBLIC_MAX_FILE_SIZE_MB=10
NEXT_PUBLIC_ENVIRONMENT=production
```

### 3.3 Deploy
Vercel will automatically deploy when you push to the main branch.

## 🔄 Step 4: Setup Continuous Deployment

### 4.1 GitHub Secrets
Go to your GitHub repo → Settings → Secrets and variables → Actions

Add these secrets:

```bash
# For Fly.io deployment
FLY_API_TOKEN=your_fly_api_token  # Get with: fly auth token

# For Vercel deployment (optional, if using GitHub Actions)
VERCEL_TOKEN=your_vercel_token
VERCEL_ORG_ID=your_org_id
VERCEL_PROJECT_ID=your_project_id

# Environment variables
NEXT_PUBLIC_API_URL=https://resume-ai-backend.fly.dev
NEXT_PUBLIC_MAX_FILE_SIZE_MB=10
```

### 4.2 Update CORS Settings
After frontend deployment, update your backend CORS settings:

```bash
fly secrets set ALLOWED_ORIGINS=https://your-actual-vercel-domain.vercel.app
```

## 🧪 Step 5: Testing

### 5.1 Test Backend
```bash
# Health check
curl https://resume-ai-backend.fly.dev/api/health

# API documentation
open https://resume-ai-backend.fly.dev/docs
```

### 5.2 Test Frontend
1. Visit your Vercel URL
2. Try uploading a resume
3. Test the complete flow

## 🔧 Troubleshooting

### Backend Issues
```bash
# Check logs
fly logs

# SSH into container
fly ssh console

# Check app status
fly status
```

### Frontend Issues
```bash
# Check Vercel deployment logs in dashboard
# Verify environment variables are set correctly
# Check browser console for API connection errors
```

### Common Issues

1. **CORS Errors**: Make sure your Vercel domain is in the backend's ALLOWED_ORIGINS
2. **API Connection Failed**: Verify NEXT_PUBLIC_API_URL is correct
3. **Build Failures**: Check that all dependencies are in package.json/requirements.txt
4. **Memory Issues**: Increase Fly.io memory if LaTeX compilation fails

## 📊 Monitoring

### Backend Monitoring
```bash
# View metrics
fly dashboard

# Monitor logs
fly logs --follow
```

### Frontend Monitoring
- Use Vercel Analytics dashboard
- Check Vercel function logs

## 💰 Cost Estimation

### Fly.io (Backend)
- **Free tier**: $5/month credit (covers small usage)
- **Production**: ~$10-20/month for 1-2GB RAM + storage

### Vercel (Frontend)
- **Hobby tier**: Free for personal projects
- **Pro tier**: $20/month for commercial use

## 🔄 Updates and Maintenance

### Updating Code
```bash
# Make changes locally
git add .
git commit -m "Your changes"
git push origin main

# Both frontend and backend will auto-deploy via GitHub Actions
```

### Scaling
- **Backend**: Increase Fly.io resources as needed
- **Frontend**: Vercel scales automatically

## 🆘 Support

If you encounter issues:
1. Check the logs (fly logs, Vercel dashboard)
2. Verify environment variables
3. Test API endpoints individually
4. Check GitHub Actions for deployment failures

## 🎉 Success!

Once deployed, your ResumeAI Pro will be available at:
- **Frontend**: https://your-project.vercel.app
- **Backend**: https://resume-ai-backend.fly.dev
- **API Docs**: https://resume-ai-backend.fly.dev/docs