# Product Requirements Document (PRD)
## AI-Powered Resume Optimization Platform

**Version:** 2.0
**Date:** August 10, 2025
**Architecture:** Next.js + N8N Workflow Engine

---

## 1. Executive Summary

### Vision Statement
A modern, AI-powered resume optimization platform that transforms ordinary resumes into ATS-optimized, professionally crafted documents using a streamlined Next.js frontend integrated with N8N workflow automation.

### Key Value Propositions
- **Instant AI Optimization**: Transform resumes in under 60 seconds
- **ATS Compatibility**: 95%+ compatibility with major ATS systems
- **Professional PDF Generation**: LaTeX-powered, publication-quality output
- **Real-time Processing**: Live progress tracking and insights
- **Industry-Specific**: Tailored optimization for different roles and industries

---

## 2. Architecture Overview

### Technology Stack
```
Frontend: Next.js 15.4.5 + React 19 + TypeScript + Tailwind CSS
AI Processing: N8N Workflow Engine (hosted on Render.com)
AI Models: OpenAI GPT-4o-mini with Structured Output Parsing
PDF Generation: LaTeX Online Compiler (latexonline.cc)
File Processing: PDF-parse + Client-side extraction
UI Components: Radix UI + Lucide React Icons
```

### System Architecture
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Next.js App   │───▶│  N8N Workflow    │───▶│  LaTeX Compiler │
│  (Frontend UI)  │    │   AI Processing  │    │   PDF Generator │
└─────────────────┘    └──────────────────┘    └─────────────────┘
        │                        │                       │
        ▼                        ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Client-side PDF │    │ OpenAI GPT-4o    │    │ Professional    │
│  Text Extract   │    │  Optimization    │    │   PDF Output    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

---

## 3. Core Features

### 3.1 File Upload & Processing
- **Supported Formats**: PDF, DOC, DOCX, TXT
- **Client-side Processing**: Secure text extraction without server storage
- **File Validation**: Size limits, format verification, content validation
- **Progress Tracking**: Real-time upload and processing status

### 3.2 AI-Powered Optimization
- **Resume Parsing**: Intelligent extraction of contact info, experience, education, skills
- **Content Analysis**: Strengths, weaknesses, ATS compatibility assessment
- **Job Matching**: Keyword optimization based on job description
- **Professional Enhancement**: Industry-specific improvements and formatting

### 3.3 Quality Scoring System
```typescript
interface QualityMetrics {
  overall_score: number;        // 0-100 comprehensive score
  ats_compatibility: number;    // ATS system compatibility
  job_match: number;           // Job description alignment
  content_quality: number;     // Professional writing quality
  keyword_density: number;     // Industry keyword optimization
  formatting_score: number;    // Document structure quality
}
```

### 3.4 Professional PDF Generation
- **LaTeX Engine**: High-quality typesetting for professional appearance
- **Unicode Sanitization**: Advanced character cleaning to prevent compilation errors
- **Template System**: Clean, ATS-friendly document templates
- **Error Handling**: Fallback generation for problematic content

---

## 4. N8N Workflow Architecture

### 4.1 Workflow Components
```
Webhook Trigger → Data Processor → AI Agent → Response Parser
     ↓                                            ↓
PDF Response ← PDF Generator ← LaTeX Converter ← LaTeX Sanitizer
```

### 4.2 Enhanced LaTeX Sanitizer
- **Unicode Character Removal**: Comprehensive control character cleaning
- **Currency Symbol Handling**: Euro, Dollar, Pound symbol normalization
- **LaTeX Escaping**: Special character protection
- **Fallback Generation**: Simple document creation for problematic content
- **Pattern Detection**: Identification and cleaning of problematic sequences

### 4.3 Workflow Configuration
```yaml
Workflow ID: gP20OZzhn0PDYDUi
Name: Professional Resume AI Optimization Pipeline
Webhook Path: /webhook/resume-professional
AI Model: OpenAI GPT-4o-mini
Output Format: Structured JSON
Error Handling: Comprehensive fallbacks
```

---

## 5. Frontend Architecture

### 5.1 Directory Structure
```
frontend/
├── app/
│   ├── api/                 # Next.js API routes (minimal/deprecated)
│   ├── page.tsx            # Main application page
│   ├── layout.tsx          # Root layout component
│   └── globals.css         # Global styles
├── components/
│   ├── sections/           # Main application sections
│   │   └── OptimizationSectionN8N.tsx
│   └── ui/                 # Reusable UI components
├── lib/
│   ├── n8n-api.ts         # N8N workflow integration
│   ├── pdf-extractor.ts   # Client-side file processing
│   └── utils.ts           # Utility functions
└── public/                # Static assets
```

### 5.2 Key Components

#### OptimizationSectionN8N.tsx
- **Primary Interface**: Main user interaction component
- **Progress Simulation**: Visual feedback during processing
- **Error Handling**: Comprehensive error management
- **State Management**: Processing status, results, insights

#### n8n-api.ts
- **Workflow Integration**: Direct N8N webhook communication
- **Type Safety**: Full TypeScript interface definitions
- **Error Handling**: Network and API error management
- **Data Transformation**: Request/response formatting

---

## 6. User Experience Flow

### 6.1 Primary User Journey
```
1. Land on homepage
2. Upload resume file (PDF/DOC/DOCX/TXT)
3. Paste job description (optional but recommended)
4. Click "Optimize Resume"
5. Watch real-time progress with insights
6. Review optimization results and scores
7. Download professional PDF
```

### 6.2 Progress Visualization
- **Step-by-step Progress**: Visual indicators for each processing stage
- **Live Insights**: Real-time processing updates and discoveries
- **Quality Metrics**: Immediate feedback on optimization improvements
- **Completion Celebration**: Success animations and download prompts

---

## 7. Integration Points

### 7.1 N8N Workflow API
```typescript
// Primary integration endpoint
POST https://n8n-new-k5wy.onrender.com/webhook/resume-professional

// Request payload
{
  resume_text: string;
  job_description?: string;
  target_role?: string;
  target_industry?: string;
}

// Response structure
{
  success: boolean;
  job_id: string;
  results: OptimizationResults;
  quality_metrics: QualityMetrics;
  latex_info: LaTeXProcessingInfo;
}
```

### 7.2 File Processing
- **Client-side Only**: No server-side file storage
- **PDF Parsing**: pdf-parse library for text extraction
- **Security**: No file uploads to external servers
- **Performance**: Immediate processing without network delays

---

## 8. Quality Assurance

### 8.1 Error Prevention
- **Duplicate Execution Protection**: Fixed useEffect dependency issues
- **Unicode Character Handling**: Comprehensive LaTeX sanitization
- **Network Error Handling**: Retry mechanisms and user feedback
- **File Format Validation**: Supported format verification

### 8.2 Performance Optimization
- **Client-side Processing**: Reduced server load and faster initial processing
- **Progressive Enhancement**: Graceful degradation for slower connections
- **Caching Strategies**: Optimized resource loading
- **Bundle Optimization**: Code splitting and lazy loading

---

## 9. Deployment Architecture

### 9.1 Frontend Deployment
- **Platform**: Vercel (recommended) or Netlify
- **Environment**: Node.js 20+, Next.js 15.4.5
- **Build Process**: Static generation where possible
- **CDN**: Global content delivery for optimal performance

### 9.2 N8N Workflow
- **Platform**: Render.com (currently deployed)
- **Webhook URL**: https://n8n-new-k5wy.onrender.com
- **Monitoring**: Built-in N8N execution monitoring
- **Scaling**: Auto-scaling based on request volume

---

## 10. Security & Privacy

### 10.1 Data Handling
- **No Server Storage**: All file processing happens client-side
- **Temporary Processing**: N8N processes data without persistent storage
- **API Security**: HTTPS endpoints with proper error handling
- **User Privacy**: No personal data retention

### 10.2 Content Security
- **LaTeX Sanitization**: Prevent code injection through resume content
- **File Validation**: Strict file type and size limitations
- **Error Boundary**: Graceful handling of malicious or corrupted files

---

## 11. Success Metrics

### 11.1 Technical KPIs
- **Processing Time**: < 60 seconds average
- **Success Rate**: > 95% completion rate
- **ATS Compatibility**: > 90% average score
- **Error Rate**: < 2% failed optimizations

### 11.2 User Experience KPIs
- **User Satisfaction**: Measured through quality score improvements
- **Conversion Rate**: File upload to PDF download completion
- **Time to Value**: Time from upload to first insights
- **Return Usage**: Multi-session optimization usage

---

## 12. Future Enhancements

### 12.1 Phase 2 Features
- **Multiple Resume Templates**: Different professional styles
- **Batch Processing**: Multiple resumes for different roles
- **Cover Letter Generation**: AI-powered cover letter optimization
- **LinkedIn Profile Sync**: Integration with LinkedIn for profile optimization

### 12.2 Technical Improvements
- **Advanced AI Models**: GPT-4o or Claude integration for enhanced quality
- **Custom LaTeX Templates**: Industry-specific formatting options
- **Real-time Collaboration**: Multi-user editing capabilities
- **Analytics Dashboard**: User optimization history and trends

---

## 13. Risk Mitigation

### 13.1 Technical Risks
- **N8N Service Downtime**: Fallback processing mechanisms
- **OpenAI API Limits**: Rate limiting and quota management
- **LaTeX Compilation Failures**: Enhanced error handling and fallbacks
- **File Processing Errors**: Comprehensive validation and error messages

### 13.2 Business Risks
- **API Cost Management**: Usage monitoring and optimization
- **User Experience**: Continuous testing and feedback integration
- **Scalability**: Infrastructure planning for growth
- **Competition**: Feature differentiation and quality focus

---

## 14. Implementation Timeline

### 14.1 Current Status ✅
- [x] Next.js frontend architecture established
- [x] N8N workflow deployed and tested
- [x] Enhanced LaTeX sanitization implemented
- [x] Duplicate execution issue resolved
- [x] Client-side file processing working
- [x] Real-time progress tracking functional

### 14.2 Immediate Next Steps
- [ ] Remove deprecated backend folder
- [ ] Optimize frontend useEffect dependencies
- [ ] Enhanced error messaging and user feedback
- [ ] Production deployment testing
- [ ] Performance optimization and monitoring
- [ ] User acceptance testing and feedback collection

---

## 15. Technical Specifications

### 15.1 Development Environment
```bash
Node.js: 20+
Package Manager: npm or yarn
Development Server: next dev (port 3000)
Build Command: next build
Start Command: next start
```

### 15.2 Environment Variables
```env
# N8N Integration
NEXT_PUBLIC_N8N_BASE_URL=https://n8n-new-k5wy.onrender.com
NEXT_PUBLIC_N8N_WEBHOOK_PATH=/webhook/resume-professional

# Optional: Analytics and monitoring
NEXT_PUBLIC_ANALYTICS_ID=your_analytics_id
```

### 15.3 Dependencies Management
- **Core**: React 19, Next.js 15.4.5, TypeScript 5
- **UI**: Radix UI components, Tailwind CSS 4, Lucide React
- **Processing**: pdf-parse, axios
- **Development**: ESLint, Prettier (recommended)

---

## Conclusion

This PRD defines a streamlined, modern architecture leveraging Next.js for the frontend and N8N for AI workflow orchestration. The elimination of the custom backend in favor of Next.js API routes and N8N workflows provides better scalability, maintainability, and user experience while reducing infrastructure complexity.

The focus is on delivering a fast, reliable, and user-friendly resume optimization experience with professional-quality outputs and comprehensive error handling.
