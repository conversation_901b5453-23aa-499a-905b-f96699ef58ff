#!/usr/bin/env python3
"""
Extract and display text from the generated DOCX file to verify content
"""

try:
    from docx import Document

    def extract_docx_text(filename):
        """Extract text from a DOCX file"""
        try:
            doc = Document(filename)
            text_content = []

            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)

            return "\n".join(text_content)
        except Exception as e:
            return f"Error reading DOCX: {e}"

    print("📄 Content of test_output.docx:")
    print("=" * 50)
    content = extract_docx_text("test_output.docx")
    print(content)
    print("=" * 50)

    # Check if it contains expected content
    if "Test Document" in content and "Test Author" in content:
        print("✅ DOCX contains the correct LaTeX-converted content!")
    else:
        print("❌ DOCX does not contain expected content")

except ImportError:
    print("⚠️  python-docx not available. Installing...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "python-docx"])
    print("Installed python-docx. Please run the script again.")
