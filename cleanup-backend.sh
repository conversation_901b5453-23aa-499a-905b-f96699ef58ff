#!/bin/bash

# Clean up deprecated backend folder
# This script moves the backend folder to a backup location
# Run this when you're confident the Next.js + N8N setup is working

echo "🧹 Cleaning up deprecated backend folder..."
echo "📁 Creating backup of backend folder..."

# Get the project root directory
PROJECT_ROOT="/Users/<USER>/Desktop/projects/resume-ai"

# Create a backup directory with timestamp
BACKUP_DIR="$PROJECT_ROOT/backend_backup_$(date +%Y%m%d_%H%M%S)"

if [ -d "$PROJECT_ROOT/backend" ]; then
    echo "📦 Moving backend folder to: $BACKUP_DIR"
    mv "$PROJECT_ROOT/backend" "$BACKUP_DIR"
    echo "✅ Backend folder backed up successfully!"
    echo ""
    echo "📋 What was backed up:"
    echo "   - Python FastAPI server (main.py)"
    echo "   - AI agents and services"
    echo "   - Templates and output files"
    echo "   - Virtual environments"
    echo ""
    echo "🔄 Current architecture:"
    echo "   Frontend: Next.js (./frontend/)"
    echo "   Backend: N8N Workflows (https://n8n-new-k5wy.onrender.com)"
    echo "   AI Processing: OpenAI via N8N"
    echo "   PDF Generation: LaTeX Online"
    echo ""
    echo "🎯 To restore backend (if needed):"
    echo "   mv $BACKUP_DIR $PROJECT_ROOT/backend"
else
    echo "❌ Backend folder not found at $PROJECT_ROOT/backend"
fi

echo ""
echo "🚀 Your streamlined architecture is ready!"
echo "💡 Run 'cd frontend && npm run dev' to start the application"
