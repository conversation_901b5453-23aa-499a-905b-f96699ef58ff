// Enhanced LaTeX Sanitizer to fix compilation issues including Unicode control characters
const input = $input.first().json;
let latexCode = input.latex_code || '';

// Comprehensive function to clean and sanitize LaTeX text
function sanitizeLatexText(text) {
  if (!text) return '';

  return text
    // CRITICAL: Remove ALL control characters (including tab U+0009 causing ^^@9 error)
    .replace(/[\u0000-\u001F\u007F-\u009F]/g, '')
    // Replace problematic Unicode characters
    .replace(/[\u2028\u2029]/g, ' ')     // Line/Paragraph separators
    .replace(/[\u00A0]/g, ' ')           // Non-breaking space
    .replace(/[\uFEFF]/g, '')            // Byte order mark
    .replace(/[\u200B-\u200D]/g, '')     // Zero-width characters
    .replace(/[\u2014\u2013]/g, '---')   // Em/en dashes
    .replace(/[\u2018\u2019]/g, "'")     // Smart quotes
    .replace(/[\u201C\u201D]/g, '"')     // Smart double quotes
    .replace(/[\u2026]/g, '...')         // Ellipsis
    // Now handle LaTeX special characters
    .replace(/&/g, '\\&')
    .replace(/%/g, '\\%')
    .replace(/#/g, '\\#')
    .replace(/\$/g, '\\$')
    .replace(/(?<!\\)_/g, '\\_')
    .replace(/\^/g, '\\textasciicircum{}')
    .replace(/~/g, '\\textasciitilde{}')
    .replace(/\{/g, '\\{')
    .replace(/\}/g, '\\}')
    // Clean up backslash issues
    .replace(/\\\\/g, '\\textbackslash{}')
    // Fix common double-escaping issues
    .replace(/\\\\&/g, '\\&')
    .replace(/\\\\%/g, '\\%')
    .replace(/\\\\#/g, '\\#')
    .replace(/\\\\\$/g, '\\$')
    .replace(/\\\\\\_/g, '\\_')
    // Normalize whitespace
    .replace(/\s+/g, ' ')
    .trim();
}

// Create a clean, simple LaTeX document
function createSimpleLatex(resumeData) {
  const contact = resumeData.contact_info || {};
  const experience = resumeData.experience || [];
  const education = resumeData.education || [];
  const skills = resumeData.skills || {};

  // Clean all text content - CRITICAL: sanitize everything!
  const cleanName = sanitizeLatexText(contact.name || 'Professional Resume');
  const cleanEmail = sanitizeLatexText(contact.email || '');
  const cleanPhone = sanitizeLatexText(contact.phone || '');
  const cleanAddress = sanitizeLatexText(contact.address || '');
  const cleanSummary = sanitizeLatexText(resumeData.professional_summary || resumeData.summary || 'Professional summary not available.');

  return `\\documentclass[11pt,a4paper]{article}
\\usepackage[margin=1in]{geometry}
\\usepackage{enumitem}
\\usepackage{titlesec}
\\usepackage[utf8]{inputenc}
\\usepackage[T1]{fontenc}

\\titleformat{\\section}{\\Large\\bfseries}{}{0em}{}
\\titleformat{\\subsection}{\\large\\bfseries}{}{0em}{}

\\begin{document}

\\begin{center}
\\textbf{\\LARGE ${cleanName}}\\\\
${cleanEmail}${cleanPhone ? ' \\textbullet{} ' + cleanPhone : ''}${cleanAddress ? ' \\textbullet{} ' + cleanAddress : ''}
\\end{center}

\\section{Professional Summary}
${cleanSummary}

\\section{Professional Experience}
${experience.map(exp => {
  const cleanPosition = sanitizeLatexText(exp.position || 'Position');
  const cleanCompany = sanitizeLatexText(exp.company || 'Company');
  const cleanDuration = sanitizeLatexText(exp.duration || 'Duration not specified');
  const cleanLocation = sanitizeLatexText(exp.location || '');
  const cleanAchievements = (exp.achievements || []).map(achievement => sanitizeLatexText(achievement));

  return `
\\subsection{${cleanPosition} --- ${cleanCompany}}
\\textit{${cleanDuration}${cleanLocation ? ' \\textbullet{} ' + cleanLocation : ''}}
\\begin{itemize}[leftmargin=*]
${cleanAchievements.map(achievement => `\\item ${achievement}`).join('\n')}
\\end{itemize}`;
}).join('')}

\\section{Education}
${education.map(edu => {
  const cleanDegree = sanitizeLatexText(edu.degree || 'Degree');
  const cleanInstitution = sanitizeLatexText(edu.institution || 'Institution');
  const cleanYear = sanitizeLatexText(edu.year || 'Year');

  return `
\\subsection{${cleanDegree}}
\\textit{${cleanInstitution} --- ${cleanYear}}`;
}).join('')}

\\section{Skills}
\\begin{itemize}[leftmargin=*]
${(skills.technical || []).map(skill => `\\item ${sanitizeLatexText(skill)}`).join('\n')}
${(skills.soft || []).map(skill => `\\item ${sanitizeLatexText(skill)}`).join('\n')}
\\end{itemize}

\\end{document}`;
}

// Enhanced LaTeX code cleaning with AGGRESSIVE Unicode control character removal
function cleanLatexCode(code) {
  if (!code) return '';

  return code
    // CRITICAL: Remove ALL Unicode control characters first, especially U+0009 (tab)
    .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // This removes \u0009 (tab) causing ^^@9 error
    .replace(/[\u2028\u2029]/g, ' ')
    .replace(/[\u00A0]/g, ' ')
    .replace(/[\uFEFF]/g, '')
    .replace(/[\u200B-\u200D]/g, '')
    // Handle problematic dashes and quotes
    .replace(/[\u2014\u2013]/g, '---')
    .replace(/[\u2018\u2019]/g, "'")
    .replace(/[\u201C\u201D]/g, '"')
    .replace(/[\u2026]/g, '...')
    // Fix common LaTeX issues
    .replace(/\\&amp;/g, '\\&')
    .replace(/&amp;/g, '\\&')
    .replace(/&/g, '\\&')
    .replace(/(?<!\\)%/g, '\\%')
    .replace(/(?<!\\)#/g, '\\#')
    .replace(/(?<!\\)\$/g, '\\$')
    .replace(/(?<!\\)_/g, '\\_')
    // Clean multiple spaces
    .replace(/\s+/g, ' ')
    .trim();
}

// Check if LaTeX needs to be recreated or can be cleaned
let cleanLatex = latexCode;
let recreated = false;

// CRITICAL: Check for problematic characters including the specific tab character issue
const hasControlChars = /[\u0000-\u001F\u007F-\u009F]/.test(latexCode);
const hasProblematicContent = latexCode.includes('moderncv') ||
                            latexCode.includes('Missing $') ||
                            latexCode.includes('\\cventry') ||
                            latexCode.includes('\\makecvtitle');

if (!latexCode || hasControlChars || hasProblematicContent) {
  console.log('RECREATING LaTeX due to control characters or problematic content');
  console.log('Control characters detected:', hasControlChars);
  console.log('Tab character (U+0009) detected:', /\u0009/.test(latexCode));
  console.log('Problematic content detected:', hasProblematicContent);

  // Use the cleaned resume data to create fresh LaTeX
  cleanLatex = createSimpleLatex(input.optimized_resume || input.parsed_resume || {});
  recreated = true;
} else {
  // Clean existing LaTeX aggressively
  cleanLatex = cleanLatexCode(latexCode);
}

// FINAL CRITICAL CHECK - ensure no problematic characters remain
if (/[\u0000-\u001F\u007F-\u009F]/.test(cleanLatex)) {
  console.log('ERROR: Control characters still detected after cleaning! Forcing complete recreation.');
  console.log('Remaining control chars found, forcing complete recreation with fresh data');
  cleanLatex = createSimpleLatex(input.optimized_resume || input.parsed_resume || {});
  recreated = true;
}

// Log the cleaning results for debugging
console.log('=== LaTeX Sanitization Results ===');
console.log('Original length:', latexCode.length);
console.log('Clean length:', cleanLatex.length);
console.log('Had control characters (before):', /[\u0000-\u001F\u007F-\u009F]/.test(latexCode));
console.log('Had tab character U+0009 (before):', /\u0009/.test(latexCode));
console.log('Has control characters (after):', /[\u0000-\u001F\u007F-\u009F]/.test(cleanLatex));
console.log('Has tab character U+0009 (after):', /\u0009/.test(cleanLatex));
console.log('LaTeX was recreated:', recreated);
console.log('===================================');

return [{
  json: {
    ...input,
    latex_code: cleanLatex,
    latex_sanitized: true,
    latex_recreated: recreated,
    original_latex_length: latexCode.length,
    clean_latex_length: cleanLatex.length,
    control_characters_found: /[\u0000-\u001F\u007F-\u009F]/.test(input.latex_code || ''),
    tab_character_found: /\u0009/.test(input.latex_code || ''),
    sanitization_method: recreated ? 'full_recreation' : 'aggressive_cleaning',
    sanitization_timestamp: new Date().toISOString()
  }
}];
