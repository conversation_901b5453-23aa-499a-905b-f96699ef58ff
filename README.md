# ResumeAI Pro - AI-Powered Resume Optimizer

An intelligent resume optimization platform that uses AI agents to analyze, optimize, and format resumes with professional LaTeX templates.

## 🚀 Features

- **AI-Powered Analysis**: Uses CrewAI agents and Google Gemini for intelligent resume optimization
- **LaTeX Compilation**: Professional PDF generation with LaTeX templates
- **Real-time Processing**: WebSocket-based progress tracking
- **File Processing**: Support for PDF, DOCX, and text resume uploads
- **Modern UI**: Next.js frontend with Tailwind CSS and shadcn/ui components

## 🏗️ Architecture

### Frontend (Next.js)
- **Framework**: Next.js 15 with React 19
- **Styling**: Tailwind CSS with shadcn/ui components
- **State Management**: Zustand + React Query
- **File Processing**: PDF parsing and document handling
- **Real-time Updates**: WebSocket integration

### Backend (FastAPI)
- **Framework**: FastAPI with Python 3.11+
- **AI Agents**: CrewAI with Google Gemini integration
- **Document Processing**: PyPDF2, python-docx, Tesseract OCR
- **LaTeX Compilation**: Full texlive distribution
- **Real-time Communication**: WebSocket support

## 🛠️ Local Development

### Prerequisites
- Python 3.11+
- Node.js 18+
- LaTeX distribution (for backend)

### Backend Setup
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt

# Copy environment file and configure
cp .env.example .env
# Edit .env with your API keys

# Start the server
python main.py
```

### Frontend Setup
```bash
cd frontend
npm install

# Copy environment file and configure
cp .env.local.example .env.local
# Edit .env.local with your backend URL

# Start development server
npm run dev
```

## 🚀 Deployment

### Backend Deployment (Fly.io)
1. Install Fly CLI: `curl -L https://fly.io/install.sh | sh`
2. Login: `fly auth login`
3. Deploy: `./deploy-backend.sh`

### Frontend Deployment (Vercel)
1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

## 📁 Project Structure

```
├── backend/                 # FastAPI backend
│   ├── app/                # Application code
│   │   ├── api/           # API routes
│   │   ├── core/          # Core configuration
│   │   └── services/      # Business logic
│   ├── templates/         # LaTeX templates
│   ├── Dockerfile         # Production container
│   └── requirements.txt   # Python dependencies
├── frontend/               # Next.js frontend
│   ├── app/               # App router pages
│   ├── components/        # React components
│   ├── lib/              # Utilities and API clients
│   └── public/           # Static assets
└── docs/                  # Documentation
```

## 🔧 Environment Variables

### Backend (.env)
```bash
GEMINI_API_KEY=your_google_gemini_api_key
DEBUG=false
ALLOWED_ORIGINS=https://your-frontend-domain.vercel.app
```

### Frontend (.env.local)
```bash
NEXT_PUBLIC_API_URL=https://your-backend.fly.dev
NEXT_PUBLIC_MAX_FILE_SIZE_MB=10
```

## 🧪 Testing

### Backend Tests
```bash
cd backend
pytest
```

### Frontend Tests
```bash
cd frontend
npm test
```

## 📝 API Documentation

Once the backend is running, visit:
- API Documentation: `http://localhost:8000/docs`
- Health Check: `http://localhost:8000/api/health`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions, please open an issue in the GitHub repository.# resume-ai
