# LaTeX Editor Implementation Strategy
*Resume-AI Project - Comprehensive Implementation Plan*

## 🎯 Project Overview

We are building a **professional-grade LaTeX editor with live PDF preview** similar to Overleaf, integrated into our resume-ai application. This will enable users to edit LaTeX documents with real-time compilation and preview, creating a seamless document editing experience.

### Core Objectives
- **Monaco Editor** integration for professional code editing
- **PDF.js** for browser-based PDF viewing (no plugins)
- **Docker-based LaTeX compilation** for reliability and security
- **Real-time compilation** with WebSocket updates
- **Split-pane interface** (editor left, preview right)
- **Mobile responsive** design
- **Error reporting** with line-level debugging

---

## 🏗️ Architecture Overview

### **High-Level Architecture**
```
┌─────────────────┬─────────────────┐
│   Frontend      │   Backend       │
│   (Next.js)     │   (FastAPI)     │
├─────────────────┼─────────────────┤
│ • Monaco Editor │ • Docker        │
│ • PDF.js Viewer │   Compilation   │
│ • WebSocket     │ • Job Queue     │
│ • File Manager  │ • File Storage  │
│ • UI/UX Layer   │ • WebSocket API │
└─────────────────┴─────────────────┘
```

### **Technology Stack**

| Component | Technology | Reason | Trust Score |
|-----------|------------|---------|-------------|
| **Code Editor** | Monaco Editor | Same as VS Code, excellent LaTeX support | 9.9/10 |
| **PDF Viewer** | PDF.js | Mozilla's web standard, no plugins | 9.0/10 |
| **LaTeX Engine** | TeX Live (Docker) | Complete, reliable, secure | 9.5/10 |
| **Compilation** | Docker + danteev/texlive | Isolated, reproducible builds | 9.0/10 |
| **Real-time** | WebSocket/SSE | Live updates and collaboration | 8.5/10 |
| **Job Queue** | Redis + Celery/Arq | Async compilation handling | 8.5/10 |

---

## 📋 Implementation Phases

### **Phase 1: Core Editor Setup** (Week 1-2)
**Goal**: Functional Monaco Editor with LaTeX syntax highlighting

#### 1.1 Frontend Dependencies
```bash
npm install monaco-editor @monaco-editor/react
npm install @types/monaco-editor
npm install react-split-pane  # For layout
```

#### 1.2 Monaco Editor Configuration
- LaTeX language registration
- Syntax highlighting rules
- Theme configuration (VS Code Dark/Light)
- Auto-completion setup
- Error marker integration

#### 1.3 Split-Pane Layout
- Responsive design (mobile-first)
- Resizable panels
- State management for panel sizes
- Keyboard shortcuts

### **Phase 2: PDF Viewing** (Week 2-3)
**Goal**: PDF.js integration with full viewing capabilities

#### 2.1 PDF.js Setup
```bash
npm install pdfjs-dist
npm install @types/pdfjs-dist
```

#### 2.2 PDF Viewer Features
- Page navigation controls
- Zoom in/out functionality
- Fit-to-width/height options
- Search within PDF
- Download/print capabilities
- Mobile touch gestures

#### 2.3 PDF Loading States
- Loading spinner
- Error handling for corrupt PDFs
- Progress indicators
- Retry mechanisms

### **Phase 3: LaTeX Compilation** (Week 3-4)
**Goal**: Docker-based compilation service with async job processing

#### 3.1 Docker Setup
```dockerfile
# Dockerfile for LaTeX compilation
FROM danteev/texlive:latest

WORKDIR /latex
COPY compile.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/compile.sh

# Security: non-root user
RUN useradd -m -u 1001 latex
USER latex

CMD ["/usr/local/bin/compile.sh"]
```

#### 3.2 FastAPI Endpoints
- `POST /api/latex/compile` - Submit LaTeX for compilation
- `GET /api/latex/jobs/{job_id}` - Check job status
- `GET /api/latex/jobs/{job_id}/pdf` - Download PDF
- `GET /api/latex/jobs/{job_id}/logs` - Get compilation logs
- `DELETE /api/latex/jobs/{job_id}` - Cancel/cleanup job

#### 3.3 Job Queue Implementation
```python
# Using Celery with Redis
from celery import Celery

celery_app = Celery(
    "latex_compiler",
    broker="redis://localhost:6379",
    backend="redis://localhost:6379"
)

@celery_app.task
def compile_latex_document(job_id: str, latex_content: str):
    # Docker compilation logic
    return {"status": "success", "pdf_path": "...", "logs": "..."}
```

### **Phase 4: Real-time Integration** (Week 4-5)
**Goal**: WebSocket integration for live updates

#### 4.1 WebSocket Setup
- FastAPI WebSocket endpoints
- Client-side WebSocket connection
- Message queuing and retry logic
- Connection state management

#### 4.2 Auto-compilation Features
- Debounced compilation (500ms after typing stops)
- Smart compilation (detect significant changes)
- Error reporting with line numbers
- Success/failure notifications

#### 4.3 File Management
- Project structure handling
- Multi-file LaTeX documents
- Asset management (images, styles)
- Version control integration

### **Phase 5: Advanced Features** (Week 5-6)
**Goal**: Professional-grade enhancements

#### 5.1 Performance Optimizations
- Incremental compilation
- Build caching strategies
- Package pre-loading
- Resource optimization

#### 5.2 User Experience Enhancements
- Keyboard shortcuts
- Context menus
- Command palette
- Settings panel

#### 5.3 Error Handling & Debugging
- Detailed error messages
- Line-level error highlighting
- Compilation log parsing
- Quick fix suggestions

---

## 🔧 Technical Implementation Details

### **Monaco Editor Configuration**
```typescript
// LaTeX language registration
import * as monaco from 'monaco-editor';

// Register LaTeX language
monaco.languages.register({ id: 'latex' });

// Define syntax highlighting
monaco.languages.setMonarchTokensProvider('latex', {
  tokenizer: {
    root: [
      [/\\[a-zA-Z@]+/, 'keyword'],
      [/\$.*?\$/, 'string'],
      [/%.*$/, 'comment'],
      [/\{/, '@brackets'],
      [/\}/, '@brackets'],
    ]
  }
});

// Configure editor options
const editorOptions = {
  language: 'latex',
  theme: 'vs-dark',
  fontSize: 14,
  minimap: { enabled: true },
  wordWrap: 'on',
  automaticLayout: true,
  scrollBeyondLastLine: false,
  renderLineHighlight: 'line',
  cursorBlinking: 'phase',
};
```

### **PDF.js Viewer Implementation**
```typescript
import { pdfjs, PDFDocumentProxy } from 'pdfjs-dist';

// Configure PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

const PDFViewer: React.FC<{ pdfUrl: string }> = ({ pdfUrl }) => {
  const [pdfDoc, setPdfDoc] = useState<PDFDocumentProxy | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [scale, setScale] = useState(1.0);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const loadPDF = async () => {
      try {
        const pdf = await pdfjs.getDocument(pdfUrl).promise;
        setPdfDoc(pdf);
        renderPage(1);
      } catch (error) {
        console.error('Error loading PDF:', error);
      }
    };

    loadPDF();
  }, [pdfUrl]);

  const renderPage = async (pageNumber: number) => {
    if (!pdfDoc || !canvasRef.current) return;

    const page = await pdfDoc.getPage(pageNumber);
    const viewport = page.getViewport({ scale });
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    canvas.height = viewport.height;
    canvas.width = viewport.width;

    const renderContext = {
      canvasContext: context!,
      viewport: viewport,
    };

    await page.render(renderContext).promise;
  };

  return (
    <div className="pdf-viewer">
      <canvas ref={canvasRef} className="pdf-canvas" />
      {/* Navigation controls */}
    </div>
  );
};
```

### **Docker Compilation Service**
```python
import docker
import asyncio
import tempfile
import os
from pathlib import Path

class LaTeXCompiler:
    def __init__(self):
        self.docker_client = docker.from_env()

    async def compile_document(
        self,
        latex_content: str,
        job_id: str,
        callback: callable = None
    ) -> dict:
        """Compile LaTeX document in Docker container"""

        # Create temporary directory for this job
        with tempfile.TemporaryDirectory() as temp_dir:
            # Write LaTeX content to file
            latex_file = Path(temp_dir) / "main.tex"
            latex_file.write_text(latex_content)

            # Prepare Docker volumes
            volumes = {
                temp_dir: {"bind": "/latex", "mode": "rw"}
            }

            try:
                # Run compilation in Docker
                result = self.docker_client.containers.run(
                    image="danteev/texlive:latest",
                    command="pdflatex -interaction=nonstopmode -output-directory=/latex /latex/main.tex",
                    volumes=volumes,
                    remove=True,
                    mem_limit="512m",
                    cpu_quota=50000,  # Limit CPU usage
                    timeout=30,  # 30 second timeout
                    capture_output=True,
                    text=True
                )

                # Check for PDF output
                pdf_path = Path(temp_dir) / "main.pdf"
                if pdf_path.exists():
                    # Read PDF content
                    pdf_content = pdf_path.read_bytes()

                    return {
                        "status": "success",
                        "pdf_content": pdf_content,
                        "logs": result.stdout,
                        "job_id": job_id
                    }
                else:
                    return {
                        "status": "error",
                        "error": "PDF generation failed",
                        "logs": result.stderr,
                        "job_id": job_id
                    }

            except docker.errors.ContainerError as e:
                return {
                    "status": "error",
                    "error": str(e),
                    "logs": e.stderr.decode() if e.stderr else "",
                    "job_id": job_id
                }
```

### **WebSocket Integration**
```python
# FastAPI WebSocket endpoint
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
import json

app = FastAPI()

class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            await connection.send_text(message)

manager = ConnectionManager()

@app.websocket("/ws/latex/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: int):
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)

            if message["type"] == "compile_request":
                # Start compilation
                job_id = await start_compilation(message["latex_content"])

                # Send job started notification
                await manager.send_personal_message(
                    json.dumps({
                        "type": "compilation_started",
                        "job_id": job_id
                    }),
                    websocket
                )

    except WebSocketDisconnect:
        manager.disconnect(websocket)
```

---

## 🎨 User Interface Design

### **Layout Structure**
```
┌─────────────────────────────────────────────┐
│              Header Bar                      │
│  [File] [Edit] [View] [Compile] [Settings] │
├─────────────────┬───────────────────────────┤
│                 │         PDF Preview       │
│   Monaco        │                          │
│   Editor        │   ┌─────────────────┐    │
│                 │   │     Page 1      │    │
│   LaTeX         │   │   [PDF Content] │    │
│   Code          │   │                 │    │
│                 │   └─────────────────┘    │
│                 │                          │
│                 │   [◀] Page 1/5 [▶]      │
│                 │   [Zoom] [Fit] [Download]│
├─────────────────┴───────────────────────────┤
│              Status Bar                      │
│  Ready ● | Line 25, Col 10 | [Compile] ✓   │
└─────────────────────────────────────────────┘
```

### **Component Hierarchy**
```typescript
LaTeXEditorApp
├── HeaderBar
│   ├── MenuBar
│   ├── ToolBar
│   └── SettingsPanel
├── MainLayout
│   ├── EditorPane
│   │   ├── MonacoEditor
│   │   ├── FileExplorer
│   │   └── ErrorPanel
│   └── PreviewPane
│       ├── PDFViewer
│       ├── NavigationControls
│       └── ViewerToolbar
└── StatusBar
    ├── CompilationStatus
    ├── CursorPosition
    └── DocumentStats
```

---

## 📊 Performance Considerations

### **Optimization Strategies**
1. **Lazy Loading**: Load Monaco Editor and PDF.js only when needed
2. **Code Splitting**: Separate LaTeX editor bundle from main app
3. **Caching**: Cache compiled PDFs and intermediate files
4. **Debouncing**: Limit compilation frequency (500ms delay)
5. **Incremental Updates**: Only recompile changed sections
6. **Resource Limits**: Docker container CPU/memory constraints
7. **Connection Pooling**: Efficient WebSocket management

### **Scalability Measures**
- **Horizontal Scaling**: Multiple Docker compilation workers
- **Load Balancing**: Distribute compilation jobs
- **Caching Layer**: Redis for job status and results
- **File Storage**: S3/MinIO for PDF and source files
- **CDN**: Serve static assets from CDN

---

## 🔒 Security Considerations

### **LaTeX Security**
- **Container Isolation**: All LaTeX code runs in Docker
- **Resource Limits**: Prevent DoS attacks
- **Disable Shell Escape**: Block `\write18` commands
- **Input Validation**: Sanitize LaTeX content
- **Timeout Protection**: Kill long-running compilations

### **File Security**
- **Sandboxed Execution**: No access to host filesystem
- **Temporary Storage**: Auto-cleanup of build artifacts
- **Access Control**: User-specific file permissions
- **Upload Limits**: Restrict file sizes and types

---

## 🧪 Testing Strategy

### **Unit Tests**
- Monaco Editor configuration
- PDF.js integration
- LaTeX compilation service
- WebSocket communication
- File management utilities

### **Integration Tests**
- End-to-end compilation workflow
- Real-time update synchronization
- Error handling scenarios
- Performance under load

### **User Acceptance Tests**
- Document editing workflows
- PDF viewing experience
- Mobile responsiveness
- Accessibility compliance

---

## 📈 Success Metrics

### **Technical Metrics**
- **Compilation Time**: < 5 seconds for typical documents
- **Editor Responsiveness**: < 100ms input lag
- **PDF Render Time**: < 2 seconds for typical documents
- **Error Rate**: < 1% compilation failures
- **Uptime**: 99.9% service availability

### **User Experience Metrics**
- **Time to First PDF**: < 10 seconds
- **Feature Adoption**: Monaco editor shortcuts usage
- **Error Recovery**: Users successfully fix compilation errors
- **Mobile Usage**: Successful editing on tablets/phones

---

## 🚀 Deployment Strategy

### **Development Environment**
```bash
# Frontend development
cd frontend
npm run dev

# Backend development
cd backend
uvicorn main:app --reload

# Docker services
docker-compose up redis postgres
```

### **Production Deployment**
- **Frontend**: Vercel/Netlify with CDN
- **Backend**: Docker containers on AWS/GCP
- **Compilation**: Kubernetes pods for scaling
- **Storage**: S3 for files, Redis for sessions
- **Monitoring**: Prometheus + Grafana

---

## 🔄 Future Enhancements

### **Phase 6: Advanced Features** (Future)
- **Real-time Collaboration**: Multiple users editing simultaneously
- **Version Control**: Git integration for document history
- **Template Library**: Pre-built LaTeX templates
- **Bibliography Management**: BibTeX integration
- **Export Options**: Multiple output formats (HTML, DOCX)
- **AI Integration**: LaTeX code suggestions and improvements

### **Phase 7: Mobile Apps** (Future)
- **React Native**: Mobile app for editing on-the-go
- **Offline Support**: Local compilation with Tectonic
- **Cloud Sync**: Seamless device synchronization

---

## 📚 Resources & References

### **Documentation Links**
- [Monaco Editor API](https://microsoft.github.io/monaco-editor/)
- [PDF.js Documentation](https://mozilla.github.io/pdf.js/)
- [LaTeX Workshop Wiki](https://github.com/James-Yu/LaTeX-Workshop/wiki)
- [Docker TeX Live](https://github.com/dante-ev/docker-texlive)
- [FastAPI WebSockets](https://fastapi.tiangolo.com/advanced/websockets/)

### **Inspiration Projects**
- [WebLaTeX](https://github.com/sanjib-sen/WebLaTex) - VS Code Web + Docker
- [Overleaf Community](https://github.com/overleaf/overleaf) - Reference architecture
- [ShareLaTeX](https://github.com/sharelatex/sharelatex) - Historical patterns

---

## ✅ Definition of Done

A feature/phase is considered complete when:

✅ **Functionality**: All specified features work as designed
✅ **Testing**: Unit and integration tests pass
✅ **Documentation**: Code is documented and user guides updated
✅ **Performance**: Meets specified performance benchmarks
✅ **Security**: Security review completed
✅ **Accessibility**: WCAG 2.1 AA compliance verified
✅ **Mobile**: Responsive design tested on mobile devices
✅ **Error Handling**: Graceful error recovery implemented

---

*Last Updated: August 11, 2025*
*Status: Implementation Ready*
*Next: Begin Phase 1 Development*
