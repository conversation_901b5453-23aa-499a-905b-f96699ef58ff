# Minimal LaTeX Dockerfile for resource-constrained environments
FROM python:3.11-slim

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# Install minimal LaTeX instead of full distribution
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        # Minimal LaTeX (much smaller than texlive-full)
        texlive-latex-base \
        texlive-latex-recommended \
        texlive-fonts-recommended \
        # Essential tools
        poppler-utils \
        gcc \
        build-essential \
        libmagic1 \
        # Clean up aggressively
        && apt-get clean \
        && rm -rf /var/lib/apt/lists/* \
        && rm -rf /tmp/* \
        && rm -rf /var/tmp/*

WORKDIR /app

# Copy and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser && \
    chown -R appuser:appuser /app
USER appuser

EXPOSE 8000

# Use minimal resources
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1", "--worker-class", "uvicorn.workers.UvicornWorker"]