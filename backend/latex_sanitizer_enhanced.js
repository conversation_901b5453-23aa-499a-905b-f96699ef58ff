// Enhanced LaTeX Sanitizer to fix Unicode character issues
const input = $input.first().json;
let latexCode = input.latex_code || '';

// Function to sanitize LaTeX text - Enhanced for Unicode characters
function sanitizeLatexText(text) {
  if (!text) return '';

  return text
    // Remove all Unicode control characters (including the problematic ones)
    .replace(/[\u0000-\u001F\u007F-\u009F\u2000-\u206F\u2E00-\u2E7F\u3000\uFFF0-\uFFFF]/g, '')
    // Replace specific problematic Unicode characters with safe alternatives
    .replace(/€/g, 'EUR ')
    .replace(/£/g, 'GBP ')
    .replace(/\$/g, 'USD ')
    // Escape LaTeX special characters
    .replace(/&/g, '\\&')
    .replace(/%/g, '\\%')
    .replace(/#/g, '\\#')
    .replace(/(?<!\\)_/g, '\\_')
    .replace(/\^/g, '\\textasciicircum{}')
    .replace(/~/g, '\\textasciitilde{}')
    // Fix common double-escaping issues
    .replace(/\\\\&/g, '\\&')
    .replace(/\\\\%/g, '\\%')
    .replace(/\\\\#/g, '\\#')
    .replace(/\\\\\\_/g, '\\_');
}

// Create a clean, simple LaTeX document
function createSimpleLatex(resumeData) {
  const contact = resumeData.contact_info || {};
  const experience = resumeData.experience || [];
  const education = resumeData.education || [];
  const skills = resumeData.skills || {};

  return `\\documentclass[11pt,a4paper]{article}
\\usepackage[margin=1in]{geometry}
\\usepackage{enumitem}
\\usepackage{titlesec}

\\titleformat{\\section}{\\Large\\bfseries}{}{0em}{}
\\titleformat{\\subsection}{\\large\\bfseries}{}{0em}{}

\\begin{document}

\\begin{center}
\\textbf{\\LARGE ${sanitizeLatexText(contact.name || 'Professional Resume')}}\\\\
${sanitizeLatexText(contact.email || '')} \\textbullet{} ${sanitizeLatexText(contact.phone || '')}\\\\
${sanitizeLatexText(contact.address || '')}
\\end{center}

\\section{Professional Summary}
${sanitizeLatexText(resumeData.professional_summary || resumeData.summary || 'Professional summary not available.')}

\\section{Professional Experience}
${experience.map(exp => `
\\subsection{${sanitizeLatexText(exp.position || 'Position')} - ${sanitizeLatexText(exp.company || 'Company')}}
\\textit{${sanitizeLatexText(exp.duration || 'Duration not specified')} \\textbullet{} ${sanitizeLatexText(exp.location || '')}}
\\begin{itemize}[leftmargin=*]
${(exp.achievements || []).map(achievement => `\\item ${sanitizeLatexText(achievement)}`).join('\n')}
\\end{itemize}
`).join('')}

\\section{Education}
${education.map(edu => `
\\subsection{${sanitizeLatexText(edu.degree || 'Degree')}}
\\textit{${sanitizeLatexText(edu.institution || 'Institution')} - ${sanitizeLatexText(edu.year || 'Year')}}
`).join('')}

\\section{Skills}
\\begin{itemize}[leftmargin=*]
${(skills.technical || []).map(skill => `\\item ${sanitizeLatexText(skill)}`).join('\n')}
${(skills.soft || []).map(skill => `\\item ${sanitizeLatexText(skill)}`).join('\n')}
\\end{itemize}

\\end{document}`;
}

// Function to detect and clean problematic Unicode sequences
function detectAndCleanUnicode(text) {
  // Check for specific Unicode patterns that cause LaTeX errors
  const problematicPatterns = [
    /\u001e/g,  // Record Separator (RS) - causes ^^^240K
    /\u001f/g,  // Unit Separator (US) - causes ^^^110K
    /\u0009/g,  // Tab character (HT) - causes ^^@9
    /[\u2400-\u243F]/g, // Control Pictures block
    /[\u0080-\u009F]/g, // C1 Controls
    /[\uFFF0-\uFFFF]/g  // Specials block
  ];

  let cleaned = text;
  let hasUnicode = false;

  problematicPatterns.forEach(pattern => {
    if (pattern.test(cleaned)) {
      hasUnicode = true;
      cleaned = cleaned.replace(pattern, '');
    }
  });

  // Also replace Euro symbol specifically as it often appears as \u001e
  if (cleaned.includes('240K') || cleaned.includes('110K') || cleaned.includes('230K')) {
    cleaned = cleaned.replace(/(\d+K)/g, 'EUR $1');
    hasUnicode = true;
  }

  return { cleaned, hasUnicode };
}

// Check if LaTeX needs to be recreated
let cleanLatex = latexCode;
let recreated = false;
let unicodeDetected = false;

if (!latexCode ||
    latexCode.includes('moderncv') ||
    latexCode.includes('&') ||
    latexCode.includes('Missing $') ||
    latexCode.includes('\\cventry') ||
    latexCode.includes('\\makecvtitle')) {

  console.log('Recreating LaTeX due to problematic content');
  cleanLatex = createSimpleLatex(input.optimized_resume || input.parsed_resume || {});
  recreated = true;
} else {
  // Check for Unicode issues first
  const unicodeCheck = detectAndCleanUnicode(latexCode);
  if (unicodeCheck.hasUnicode) {
    console.log('Unicode characters detected, cleaning LaTeX');
    cleanLatex = unicodeCheck.cleaned;
    unicodeDetected = true;
  }

  // Apply standard sanitization
  cleanLatex = sanitizeLatexText(cleanLatex);
}

// Final safety check - if LaTeX still contains problematic content, recreate it
if (cleanLatex.includes('^^^') || cleanLatex.includes('^^@') || cleanLatex.includes('\u001e') || cleanLatex.includes('\u001f')) {
  console.log('Still contains problematic content after cleaning, recreating LaTeX');
  cleanLatex = createSimpleLatex(input.optimized_resume || input.parsed_resume || {});
  recreated = true;
}

// Return array format as required by n8n
return [{
  json: {
    ...input,
    latex_code: cleanLatex,
    latex_sanitized: true,
    latex_recreated: recreated,
    unicode_detected: unicodeDetected,
    original_latex_length: latexCode.length,
    clean_latex_length: cleanLatex.length,
    sanitization_log: {
      recreated: recreated,
      unicode_cleaned: unicodeDetected,
      original_had_unicode: /[\u0000-\u001F\u007F-\u009F]/.test(latexCode),
      clean_check_passed: !cleanLatex.includes('^^^') && !cleanLatex.includes('^^@')
    }
  }
}];
