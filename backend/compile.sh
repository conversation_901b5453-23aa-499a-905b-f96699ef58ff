#!/bin/bash
set -e

# LaTeX compilation script for Docker container
# Expects LaTeX content to be provided via stdin or as a file

INPUT_FILE="${1:-main.tex}"
OUTPUT_DIR="${2:-/latex/output}"

# Ensure output directory exists
mkdir -p "$OUTPUT_DIR"

echo "Starting LaTeX compilation..."
echo "Input file: $INPUT_FILE"
echo "Output directory: $OUTPUT_DIR"

# Check if input file exists
if [ ! -f "$INPUT_FILE" ]; then
    echo "ERROR: Input file $INPUT_FILE not found"
    exit 1
fi

# Set TEXMF cache directory to writable location
export TEXMFCACHE=/tmp/latex/.texmf-cache
mkdir -p "$TEXMFCACHE"

# Run pdflatex with comprehensive error handling
compile_latex() {
    echo "Running pdflatex compilation (attempt $1/3)..."

    pdflatex \
        -interaction=nonstopmode \
        -halt-on-error \
        -file-line-error \
        -output-directory="$OUTPUT_DIR" \
        -synctex=1 \
        "$INPUT_FILE" 2>&1 | tee "$OUTPUT_DIR/compilation.log"

    return $?
}

# Run compilation up to 3 times to resolve references
COMPILATION_SUCCESS=false
for i in {1..3}; do
    if compile_latex "$i"; then
        COMPILATION_SUCCESS=true
        echo "Compilation attempt $i succeeded"

        # Check if PDF was actually generated
        PDF_FILE="$OUTPUT_DIR/$(basename "$INPUT_FILE" .tex).pdf"
        if [ -f "$PDF_FILE" ]; then
            echo "SUCCESS: PDF generated successfully at $PDF_FILE"
            echo "PDF size: $(stat -c%s "$PDF_FILE") bytes"
            break
        else
            echo "WARNING: pdflatex returned success but no PDF found"
            COMPILATION_SUCCESS=false
        fi
    else
        echo "Compilation attempt $i failed"

        # Extract meaningful error messages
        if [ -f "$OUTPUT_DIR/compilation.log" ]; then
            echo "=== ERROR DETAILS ==="
            grep -A 5 -B 5 "!" "$OUTPUT_DIR/compilation.log" | tail -20 || true
            echo "=== END ERROR DETAILS ==="
        fi

        # Don't retry if it's a syntax error
        if grep -q "Undefined control sequence\|Missing\|Paragraph ended before" "$OUTPUT_DIR/compilation.log" 2>/dev/null; then
            echo "Syntax error detected, stopping retries"
            break
        fi
    fi
done

# Final status check
if [ "$COMPILATION_SUCCESS" = true ]; then
    echo "LaTeX compilation completed successfully"

    # List generated files
    echo "Generated files in $OUTPUT_DIR:"
    ls -la "$OUTPUT_DIR/" || true

    exit 0
else
    echo "LaTeX compilation failed after all attempts"

    # Save error log for debugging
    if [ -f "$OUTPUT_DIR/compilation.log" ]; then
        echo "Compilation log saved to $OUTPUT_DIR/compilation.log"

        # Show last 50 lines of log for immediate debugging
        echo "=== LAST 50 LINES OF COMPILATION LOG ==="
        tail -50 "$OUTPUT_DIR/compilation.log" || true
        echo "=== END LOG ==="
    fi

    exit 1
fi
