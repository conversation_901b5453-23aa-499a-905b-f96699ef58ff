# Production-ready Dockerfile for FastAPI + LaTeX
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies including LaTeX
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        # LaTeX distribution
        texlive-latex-base \
        texlive-latex-recommended \
        texlive-latex-extra \
        texlive-fonts-recommended \
        texlive-fonts-extra \
        # PDF processing
        poppler-utils \
        # Office document processing
        libreoffice \
        # Build tools
        gcc \
        build-essential \
        # File type detection
        libmagic1 \
        # Image processing (for Pillow)
        libjpeg-dev \
        libpng-dev \
        # Tesseract OCR
        tesseract-ocr \
        tesseract-ocr-eng \
        # For healthcheck
        curl \
        # Clean up
        && apt-get clean \
        && rm -rf /var/lib/apt/lists/*

# Set work directory
WORKDIR /app

    # Copy requirements first for better caching
    COPY requirements.txt .
    
    # Install Python dependencies
    RUN pip install --upgrade pip && \
        pip install --no-cache-dir -r requirements.txt
    
    # Copy application code
    COPY . .

# Create non-root user for security
RUN adduser --disabled-password --gecos '' appuser && \
    chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${PORT:-8000}/api/health || exit 1

# Start the application
CMD uvicorn main:app --host 0.0.0.0 --port ${PORT:-8000} --workers 1