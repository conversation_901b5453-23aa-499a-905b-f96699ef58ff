# Fly.io configuration for Resume AI Backend
app = "resume-ai-backend"
primary_region = "sjc"  # Silicon Valley - change if needed

[build]
  dockerfile = "Dockerfile"

[env]
  PORT = "8000"
  PYTHONPATH = "/app"
  ENVIRONMENT = "production"
  DEBUG = "false"

[http_service]
  internal_port = 8000
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0
  processes = ["app"]

[[http_service.checks]]
  grace_period = "10s"
  interval = "30s"
  method = "GET"
  timeout = "10s"
  path = "/api/health"

[vm]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 2048  # Increased for LaTeX compilation

[[mounts]]
  source = "resume_storage"
  destination = "/app/storage"

# Console access for debugging
[console]
  command = "/bin/bash"