#!/usr/bin/env python3
"""
Debug script for LaTeX API endpoints
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def debug_latex_api():
    """Debug the LaTeX compilation API"""

    # Simple LaTeX content
    latex_content = r"""
\documentclass{article}
\begin{document}
Hello World!
\end{document}
"""

    print("🐛 Debugging LaTeX compilation API...")

    try:
        # Submit compilation job
        print("\n1. Submitting LaTeX compilation job...")
        response = requests.post(
            f"{BASE_URL}/api/latex/compile",
            json={"latex_content": latex_content},
            timeout=10
        )

        print(f"Status code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print(f"Raw response: {response.text}")

        if response.status_code == 200:
            try:
                job_data = response.json()
                print(f"JSON response: {json.dumps(job_data, indent=2)}")
            except json.JSONDecodeError as e:
                print(f"Failed to parse JSON: {e}")
        else:
            print("Request failed")

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_latex_api()
