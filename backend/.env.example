# Environment variables for development
# Copy this file to .env and fill in your actual API keys

# Google Gemini API Configuration
GEMINI_API_KEY=your_google_gemini_api_key_here

# Application Settings
DEBUG=false
API_TITLE="Resume AI Optimizer"
API_VERSION="1.0.0"
PORT=8000

# Database Configuration (for production)
DATABASE_URL=postgresql://user:password@localhost:5432/resume_ai

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads

# CORS Configuration (update for production)
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:3001", "https://your-frontend-domain.vercel.app"]

# LaTeX Configuration
LATEX_TIMEOUT=30
LATEX_MEMORY_LIMIT=1024

# Storage Configuration
STORAGE_PATH=/app/storage
