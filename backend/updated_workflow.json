{"createdAt": "2025-08-09T23:15:19.857Z", "updatedAt": "2025-08-10T09:27:58.396Z", "id": "gP20OZzhn0PDYDUi", "name": "Professional Resume AI Optimization Pipeline", "active": true, "isArchived": false, "nodes": [{"parameters": {"httpMethod": "POST", "path": "resume-professional", "responseMode": "responseNode", "options": {"allowedOrigins": "*", "rawBody": true}}, "id": "webhook-trigger", "name": "Resume Webhook", "position": [64, 352], "type": "n8n-nodes-base.webhook", "typeVersion": 2.1, "webhookId": "b9ba9144-099d-4b54-8673-ab4c30a4948f", "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "// Professional data processor with enhanced structure\nconst input = $input.first().json;\nconst jobId = 'job_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n\n// Extract data from webhook\nlet resumeText = '';\nlet jobDescription = '';\nlet targetRole = '';\nlet targetIndustry = '';\n\nif (input.body) {\n  const body = input.body;\n  resumeText = body.resume_text || '';\n  jobDescription = body.job_description || '';\n  targetRole = body.target_role || '';\n  targetIndustry = body.target_industry || '';\n} else {\n  resumeText = input.resume_text || '';\n  jobDescription = input.job_description || '';\n  targetRole = input.target_role || '';\n  targetIndustry = input.target_industry || '';\n}\n\nreturn [{\n  json: {\n    job_id: jobId,\n    resume_text: resumeText,\n    job_description: jobDescription,\n    target_role: targetRole,\n    target_industry: targetIndustry,\n    processing_start: new Date().toISOString(),\n    status: 'processing'\n  }\n}];"}, "id": "data-processor", "name": "Professional Data Processor", "position": [288, 352], "type": "n8n-nodes-base.code", "typeVersion": 2, "onError": "continueRegularOutput"}, {"parameters": {"modelId": {"__rl": true, "value": "models/gemini-2.5-flash", "mode": "list", "cachedResultName": "models/gemini-2.5-flash"}, "messages": {"values": [{"content": "=You are an elite Resume Optimization Specialist and Career Coach with over 15 years of experience in talent acquisition, ATS systems, and professional development.\n\nYour task is to perform a comprehensive resume optimization that transforms ordinary resumes into exceptional, professionally crafted documents.\n\nIMPORTANT LATEX REQUIREMENTS:\n1. Use ONLY the article document class: \\documentclass{article}\n2. DO NOT use moderncv, fancy packages, or complex templates\n3. Use simple LaTeX formatting - NO tables, NO alignment characters (&)\n4. Properly escape special characters: & becomes \\&, % becomes \\%, $ becomes \\$, # becomes \\#, _ becomes \\_\n5. Use simple commands: \\section{}, \\subsection{}, \\textbf{}, \\textit{}, \\begin{itemize}, \\item\n6. NO COMPLEX FORMATTING OR PACKAGES\n\nRESUME TEXT:\n{{ $json.resume_text }}\n\nJOB DESCRIPTION:\n{{ $json.job_description }}\n\nTARGET ROLE: {{ $json.target_role }}\nTARGET INDUSTRY: {{ $json.target_industry }}\n\nProvide response in this EXACT JSON structure:\n\n{\n  \"parsed_resume\": {\n    \"contact_info\": {\"name\": \"\", \"email\": \"\", \"phone\": \"\", \"address\": \"\", \"linkedin\": \"\"},\n    \"summary\": \"\",\n    \"experience\": [{\"company\": \"\", \"position\": \"\", \"duration\": \"\", \"location\": \"\", \"achievements\": []}],\n    \"education\": [{\"institution\": \"\", \"degree\": \"\", \"year\": \"\", \"gpa\": \"\", \"honors\": []}],\n    \"skills\": {\"technical\": [], \"soft\": [], \"languages\": [], \"certifications\": []},\n    \"certifications\": [{\"name\": \"\", \"issuer\": \"\", \"date\": \"\", \"credential_id\": \"\"}],\n    \"additional_sections\": {\"projects\": [], \"publications\": [], \"awards\": [], \"volunteer\": []}\n  },\n  \"analysis\": {\n    \"strengths\": [],\n    \"weaknesses\": [],\n    \"ats_compatibility_issues\": [],\n    \"keyword_gaps\": [],\n    \"formatting_issues\": [],\n    \"content_recommendations\": [],\n    \"industry_alignment\": \"\",\n    \"role_fit_analysis\": \"\"\n  },\n  \"optimized_resume\": {\n    \"contact_info\": {\"name\": \"\", \"email\": \"\", \"phone\": \"\", \"address\": \"\", \"linkedin\": \"\"},\n    \"professional_summary\": \"\",\n    \"experience\": [{\"company\": \"\", \"position\": \"\", \"duration\": \"\", \"location\": \"\", \"achievements\": []}],\n    \"education\": [{\"institution\": \"\", \"degree\": \"\", \"year\": \"\", \"gpa\": \"\", \"honors\": []}],\n    \"skills\": {\"technical\": [], \"soft\": [], \"languages\": [], \"certifications\": []},\n    \"certifications\": [{\"name\": \"\", \"issuer\": \"\", \"date\": \"\", \"credential_id\": \"\"}],\n    \"additional_sections\": {\"projects\": [], \"publications\": [], \"awards\": [], \"volunteer\": []}\n  },\n  \"latex_code\": \"\\\\documentclass[11pt,a4paper]{article}\\n\\\\usepackage[margin=1in]{geometry}\\n\\\\usepackage{enumitem}\\n\\\\begin{document}\\n\\\\begin{center}\\n\\\\textbf{\\\\LARGE NAME}\\\\\\\\\\nEMAIL \\\\textbullet{} PHONE\\n\\\\end{center}\\n\\\\section{Professional Summary}\\nSUMMARY TEXT\\n\\\\section{Experience}\\nEXPERIENCE CONTENT\\n\\\\section{Education}\\nEDUCATION CONTENT\\n\\\\section{Skills}\\nSKILLS CONTENT\\n\\\\end{document}\",\n  \"scores\": {\n    \"overall_score\": 85,\n    \"ats_compatibility_score\": 90,\n    \"job_match_score\": 88,\n    \"keyword_density_score\": 85,\n    \"formatting_score\": 95,\n    \"content_quality_score\": 87,\n    \"readability_score\": 90,\n    \"impact_score\": 85\n  },\n  \"improvements_made\": [],\n  \"keywords_added\": [],\n  \"metadata\": {\n    \"processing_time\": \"45 seconds\",\n    \"optimization_level\": \"professional\",\n    \"ats_systems_optimized_for\": [\"Workday\", \"Taleo\", \"ATS\", \"Greenhouse\", \"Lever\", \"SuccessFactors\"],\n    \"industry_focus\": \"\",\n    \"target_role\": \"\",\n    \"ai_model_used\": \"gemini-2.5-flash\",\n    \"optimization_strategy\": \"comprehensive\"\n  }\n}\n\nEnsure the LaTeX code is SIMPLE and uses only basic article class with simple formatting."}]}, "jsonOutput": true, "options": {"maxOutputTokens": 8192, "temperature": 0.3}}, "id": "ai-optimizer", "name": "Elite AI Resume Optimizer", "position": [64, -192], "type": "@n8n/n8n-nodes-langchain.googleGemini", "typeVersion": 1, "credentials": {"googlePalmApi": {"id": "ACJ0M5ako03w0Q6F", "name": "Google Gemini(PaLM) Api account 2"}}, "disabled": true, "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "// Advanced response parser with comprehensive error handling\nconst input = $input.first().json.output;\nconst jobId = $('Professional Data Processor').item.json.job_id;\n\nlet response;\ntry {\n  // Extract the content from Google Gemini response\n  const content = $input.first().json.output;\n  \n  // Handle different response structures\n  let textContent = '';\n  if (typeof content === 'string') {\n    textContent = content;\n  } else if (content.parts && content.parts[0] && content.parts[0].text) {\n    textContent = content.parts[0].text;\n  } else if (content.text) {\n    textContent = content.text;\n  } else {\n    textContent = JSON.stringify(content);\n  }\n  \n  // Try to parse as JSON\n  response = JSON.parse(textContent);\n  \n  // Validate required structure\n  if (!response.parsed_resume || !response.optimized_resume || !response.latex_code) {\n    throw new Error('Invalid response structure');\n  }\n  \n} catch (error) {\n  console.error('Failed to parse AI response:', error);\n  console.error('Raw input:', JSON.stringify(input, null, 2));\n  \n  // Return fallback structure\n  return [{\n    json: {\n      job_id: jobId,\n      status: 'error',\n      error: 'Failed to parse AI response: ' + error.message,\n      raw_response: JSON.stringify(input, null, 2),\n      parsed_resume: {},\n      analysis: {\"strengths\": [], \"weaknesses\": [], \"content_recommendations\": []},\n      optimized_resume: {},\n      latex_code: '\\\\documentclass{article}\\n\\\\begin{document}\\nError processing resume\\n\\\\end{document}',\n      scores: {\n        \"overall_score\": 0,\n        \"ats_compatibility_score\": 0,\n        \"job_match_score\": 0,\n        \"keyword_density_score\": 0,\n        \"formatting_score\": 0,\n        \"content_quality_score\": 0\n      },\n      improvements_made: [],\n      metadata: {\n        \"processing_time\": \"Error\",\n        \"optimization_level\": \"failed\",\n        \"status\": \"error\"\n      }\n    }\n  }];\n}\n\n// Calculate processing time\nconst startTime = new Date($('Professional Data Processor').item.json.processing_start);\nconst processingTime = ((new Date() - startTime) / 1000).toFixed(2) + ' seconds';\n\n// Enhanced metadata\nresponse.metadata = response.metadata || {};\nresponse.metadata.processing_time = processingTime;\nresponse.metadata.processed_at = new Date().toISOString();\nresponse.metadata.job_id = jobId;\nresponse.metadata.method = 'n8n_professional_pipeline';\nresponse.metadata.api_calls = 1;\nresponse.metadata.status = 'success';\n\nreturn [{\n  json: {\n    job_id: jobId,\n    status: 'success',\n    parsed_resume: response.parsed_resume,\n    analysis: response.analysis,\n    optimized_resume: response.optimized_resume,\n    latex_code: response.latex_code,\n    scores: response.scores,\n    improvements_made: response.improvements_made,\n    keywords_added: response.keywords_added || [],\n    metadata: response.metadata\n  }\n}];"}, "id": "response-parser", "name": "Advanced Response Parser", "position": [1104, 352], "type": "n8n-nodes-base.code", "typeVersion": 2, "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "// Enhanced LaTeX Sanitizer to fix Unicode character issues\nconst input = $input.first().json;\nlet latexCode = input.latex_code || '';\n\n// Function to sanitize LaTeX text - Enhanced for Unicode characters\nfunction sanitizeLatexText(text) {\n  if (!text) return '';\n  \n  return text\n    // Remove all Unicode control characters (including the problematic ones)\n    .replace(/[\\u0000-\\u001F\\u007F-\\u009F\\u2000-\\u206F\\u2E00-\\u2E7F\\u3000\\uFFF0-\\uFFFF]/g, '')\n    // Replace specific problematic Unicode characters with safe alternatives\n    .replace(/€/g, 'EUR ')\n    .replace(/£/g, 'GBP ')\n    .replace(/\\$/g, 'USD ')\n    // Escape LaTeX special characters\n    .replace(/&/g, '\\\\&')\n    .replace(/%/g, '\\\\%')\n    .replace(/#/g, '\\\\#')\n    .replace(/(?<!\\\\)_/g, '\\\\_')\n    .replace(/\\^/g, '\\\\textasciicircum{}')\n    .replace(/~/g, '\\\\textasciitilde{}')\n    // Fix common double-escaping issues\n    .replace(/\\\\\\\\&/g, '\\\\&')\n    .replace(/\\\\\\\\%/g, '\\\\%')\n    .replace(/\\\\\\\\#/g, '\\\\#')\n    .replace(/\\\\\\\\\\\\_/g, '\\\\_');\n}\n\n// Create a clean, simple LaTeX document\nfunction createSimpleLatex(resumeData) {\n  const contact = resumeData.contact_info || {};\n  const experience = resumeData.experience || [];\n  const education = resumeData.education || [];\n  const skills = resumeData.skills || {};\n  \n  return `\\\\documentclass[11pt,a4paper]{article}\n\\\\usepackage[margin=1in]{geometry}\n\\\\usepackage{enumitem}\n\\\\usepackage{titlesec}\n\n\\\\titleformat{\\\\section}{\\\\Large\\\\bfseries}{}{0em}{}\n\\\\titleformat{\\\\subsection}{\\\\large\\\\bfseries}{}{0em}{}\n\n\\\\begin{document}\n\n\\\\begin{center}\n\\\\textbf{\\\\LARGE ${sanitizeLatexText(contact.name || 'Professional Resume')}}\\\\\\\\\n${sanitizeLatexText(contact.email || '')} \\\\textbullet{} ${sanitizeLatexText(contact.phone || '')}\\\\\\\\\n${sanitizeLatexText(contact.address || '')}\n\\\\end{center}\n\n\\\\section{Professional Summary}\n${sanitizeLatexText(resumeData.professional_summary || resumeData.summary || 'Professional summary not available.')}\n\n\\\\section{Professional Experience}\n${experience.map(exp => `\n\\\\subsection{${sanitizeLatexText(exp.position || 'Position')} - ${sanitizeLatexText(exp.company || 'Company')}}\n\\\\textit{${sanitizeLatexText(exp.duration || 'Duration not specified')} \\\\textbullet{} ${sanitizeLatexText(exp.location || '')}}\n\\\\begin{itemize}[leftmargin=*]\n${(exp.achievements || []).map(achievement => `\\\\item ${sanitizeLatexText(achievement)}`).join('\\n')}\n\\\\end{itemize}\n`).join('')}\n\n\\\\section{Education}\n${education.map(edu => `\n\\\\subsection{${sanitizeLatexText(edu.degree || 'Degree')}}\n\\\\textit{${sanitizeLatexText(edu.institution || 'Institution')} - ${sanitizeLatexText(edu.year || 'Year')}}\n`).join('')}\n\n\\\\section{Skills}\n\\\\begin{itemize}[leftmargin=*]\n${(skills.technical || []).map(skill => `\\\\item ${sanitizeLatexText(skill)}`).join('\\n')}\n${(skills.soft || []).map(skill => `\\\\item ${sanitizeLatexText(skill)}`).join('\\n')}\n\\\\end{itemize}\n\n\\\\end{document}`;\n}\n\n// Function to detect and clean problematic Unicode sequences\nfunction detectAndCleanUnicode(text) {\n  // Check for specific Unicode patterns that cause LaTeX errors\n  const problematicPatterns = [\n    /\\u001e/g,  // Record Separator (RS) - causes ^^^240K\n    /\\u001f/g,  // Unit Separator (US) - causes ^^^110K  \n    /\\u0009/g,  // Tab character (HT) - causes ^^@9\n    /[\\u2400-\\u243F]/g, // Control Pictures block\n    /[\\u0080-\\u009F]/g, // C1 Controls\n    /[\\uFFF0-\\uFFFF]/g  // Specials block\n  ];\n  \n  let cleaned = text;\n  let hasUnicode = false;\n  \n  problematicPatterns.forEach(pattern => {\n    if (pattern.test(cleaned)) {\n      hasUnicode = true;\n      cleaned = cleaned.replace(pattern, '');\n    }\n  });\n  \n  // Also replace Euro symbol specifically as it often appears as \\u001e\n  if (cleaned.includes('240K') || cleaned.includes('110K') || cleaned.includes('230K')) {\n    cleaned = cleaned.replace(/(\\d+K)/g, 'EUR $1');\n    hasUnicode = true;\n  }\n  \n  return { cleaned, hasUnicode };\n}\n\n// Check if LaTeX needs to be recreated\nlet cleanLatex = latexCode;\nlet recreated = false;\nlet unicodeDetected = false;\n\nif (!latexCode || \n    latexCode.includes('moderncv') || \n    latexCode.includes('&') || \n    latexCode.includes('Missing $') ||\n    latexCode.includes('\\\\cventry') ||\n    latexCode.includes('\\\\makecvtitle')) {\n  \n  console.log('Recreating LaTeX due to problematic content');\n  cleanLatex = createSimpleLatex(input.optimized_resume || input.parsed_resume || {});\n  recreated = true;\n} else {\n  // Check for Unicode issues first\n  const unicodeCheck = detectAndCleanUnicode(latexCode);\n  if (unicodeCheck.hasUnicode) {\n    console.log('Unicode characters detected, cleaning LaTeX');\n    cleanLatex = unicodeCheck.cleaned;\n    unicodeDetected = true;\n  }\n  \n  // Apply standard sanitization\n  cleanLatex = sanitizeLatexText(cleanLatex);\n}\n\n// Final safety check - if LaTeX still contains problematic content, recreate it\nif (cleanLatex.includes('^^^') || cleanLatex.includes('^^@') || cleanLatex.includes('\\u001e') || cleanLatex.includes('\\u001f')) {\n  console.log('Still contains problematic content after cleaning, recreating LaTeX');\n  cleanLatex = createSimpleLatex(input.optimized_resume || input.parsed_resume || {});\n  recreated = true;\n}\n\n// Return array format as required by n8n\nreturn [{\n  json: {\n    ...input,\n    latex_code: cleanLatex,\n    latex_sanitized: true,\n    latex_recreated: recreated,\n    unicode_detected: unicodeDetected,\n    original_latex_length: latexCode.length,\n    clean_latex_length: cleanLatex.length,\n    sanitization_log: {\n      recreated: recreated,\n      unicode_cleaned: unicodeDetected,\n      original_had_unicode: /[\\u0000-\\u001F\\u007F-\\u009F]/.test(latexCode),\n      clean_check_passed: !cleanLatex.includes('^^^') && !cleanLatex.includes('^^@')\n    }\n  }\n}];"}, "id": "latex-sanitizer", "name": "LaTeX Sanitizer", "position": [1328, 352], "type": "n8n-nodes-base.code", "typeVersion": 2, "onError": "continueRegularOutput"}, {"parameters": {"operation": "toText", "sourceProperty": "latex_code", "binaryPropertyName": "latex_file", "options": {"fileName": "={{ $json.job_id }}_optimized_resume.tex"}}, "id": "latex-converter", "name": "Professional LaTeX Converter", "position": [1552, 352], "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "onError": "continueRegularOutput"}, {"parameters": {"url": "https://latexonline.cc/compile", "sendQuery": true, "queryParameters": {"parameters": [{"name": "text", "value": "={{ $('LaTeX Sanitizer').item.json.latex_code }}"}, {"name": "force", "value": "true"}, {"name": "command", "value": "pdflatex"}]}, "options": {"response": {}}}, "id": "pdf-generator", "name": "Professional PDF Generator", "position": [1776, 352], "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "onError": "continueRegularOutput"}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  success: true,\n  job_id: $('Advanced Response Parser').item.json.job_id,\n  status: 'completed',\n  processing_time: $('Advanced Response Parser').item.json.metadata.processing_time,\n  results: {\n    parsed_resume: $('Advanced Response Parser').item.json.parsed_resume,\n    analysis: $('Advanced Response Parser').item.json.analysis,\n    optimized_resume: $('Advanced Response Parser').item.json.optimized_resume,\n    scores: $('Advanced Response Parser').item.json.scores,\n    improvements_made: $('Advanced Response Parser').item.json.improvements_made,\n    keywords_added: $('Advanced Response Parser').item.json.keywords_added,\n    latex_code: $('LaTeX Sanitizer').item.json.latex_code,\n    pdf_generated: true,\n    pdf_size: $('Professional PDF Generator').item.binary.data.fileSize || 'Unknown',\n    metadata: $('Advanced Response Parser').item.json.metadata\n  },\n  download_info: {\n    pdf_available: true,\n    download_url: '/download/' + $('Advanced Response Parser').item.json.job_id + '.pdf',\n    file_format: 'PDF',\n    generation_method: 'LaTeX + pdflatex'\n  },\n  quality_metrics: {\n    overall_score: $('Advanced Response Parser').item.json.scores.overall_score,\n    ats_compatibility: $('Advanced Response Parser').item.json.scores.ats_compatibility_score,\n    job_match: $('Advanced Response Parser').item.json.scores.job_match_score,\n    content_quality: $('Advanced Response Parser').item.json.scores.content_quality_score,\n    improvement_count: $('Advanced Response Parser').item.json.improvements_made.length\n  },\n  latex_info: {\n    sanitized: $('LaTeX Sanitizer').item.json.latex_sanitized,\n    recreated: $('LaTeX Sanitizer').item.json.latex_recreated\n  }\n} }}", "options": {}}, "id": "final-response", "name": "Professional Response", "position": [2000, 352], "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.5, "onError": "continueRegularOutput"}, {"parameters": {"promptType": "define", "text": "=RESUME TEXT:\n{{ $json.resume_text }}\n\nJOB DESCRIPTION:\n{{ $json.job_description }}\n\nTARGET ROLE: {{ $json.target_role }}\nTARGET INDUSTRY: {{ $json.target_industry }}", "hasOutputParser": true, "options": {"systemMessage": "=You are an elite Resume Optimization Specialist and Career Coach with over 15 years of experience in talent acquisition, ATS systems, and professional development.\n\nYour task is to perform a comprehensive resume optimization that transforms ordinary resumes into exceptional, professionally crafted documents.\n\nIMPORTANT LATEX REQUIREMENTS:\n1. Use ONLY the article document class: \\documentclass{article}\n2. DO NOT use moderncv, fancy packages, or complex templates\n3. Use simple LaTeX formatting - NO tables, NO alignment characters (&)\n4. <PERSON>perly escape special characters: & becomes \\&, % becomes \\%, $ becomes \\$, # becomes \\#, _ becomes \\_\n5. Use simple commands: \\section{}, \\subsection{}, \\textbf{}, \\textit{}, \\begin{itemize}, \\item\n6. NO COMPLEX FORMATTING OR PACKAGES\nYou are an elite Resume Optimization Specialist and Career Coach with over 15 years of experience in talent acquisition, ATS systems, and professional development.\n\nYour task is to perform a comprehensive resume optimization that transforms ordinary resumes into exceptional, professionally crafted documents.\n\nIMPORTANT LATEX REQUIREMENTS:\n1. Use ONLY the article document class: \\documentclass{article}\n2. DO NOT use moderncv, fancy packages, or complex templates\n3. Use simple LaTeX formatting - NO tables, NO alignment characters (&)\n4. <PERSON>perly escape special characters: & becomes \\&, % becomes \\%, $ becomes \\$, # becomes \\#, _ becomes \\_\n5. Use simple commands: \\section{}, \\subsection{}, \\textbf{}, \\textit{}, \\begin{itemize}, \\item\n6. NO COMPLEX FORMATTING OR PACKAGES\n\n\n\nProvide response in this EXACT JSON structure:\n\n{\n  \"parsed_resume\": {\n    \"contact_info\": {\"name\": \"\", \"email\": \"\", \"phone\": \"\", \"address\": \"\", \"linkedin\": \"\"},\n    \"summary\": \"\",\n    \"experience\": [{\"company\": \"\", \"position\": \"\", \"duration\": \"\", \"location\": \"\", \"achievements\": []}],\n    \"education\": [{\"institution\": \"\", \"degree\": \"\", \"year\": \"\", \"gpa\": \"\", \"honors\": []}],\n    \"skills\": {\"technical\": [], \"soft\": [], \"languages\": [], \"certifications\": []},\n    \"certifications\": [{\"name\": \"\", \"issuer\": \"\", \"date\": \"\", \"credential_id\": \"\"}],\n    \"additional_sections\": {\"projects\": [], \"publications\": [], \"awards\": [], \"volunteer\": []}\n  },\n  \"analysis\": {\n    \"strengths\": [],\n    \"weaknesses\": [],\n    \"ats_compatibility_issues\": [],\n    \"keyword_gaps\": [],\n    \"formatting_issues\": [],\n    \"content_recommendations\": [],\n    \"industry_alignment\": \"\",\n    \"role_fit_analysis\": \"\"\n  },\n  \"optimized_resume\": {\n    \"contact_info\": {\"name\": \"\", \"email\": \"\", \"phone\": \"\", \"address\": \"\", \"linkedin\": \"\"},\n    \"professional_summary\": \"\",\n    \"experience\": [{\"company\": \"\", \"position\": \"\", \"duration\": \"\", \"location\": \"\", \"achievements\": []}],\n    \"education\": [{\"institution\": \"\", \"degree\": \"\", \"year\": \"\", \"gpa\": \"\", \"honors\": []}],\n    \"skills\": {\"technical\": [], \"soft\": [], \"languages\": [], \"certifications\": []},\n    \"certifications\": [{\"name\": \"\", \"issuer\": \"\", \"date\": \"\", \"credential_id\": \"\"}],\n    \"additional_sections\": {\"projects\": [], \"publications\": [], \"awards\": [], \"volunteer\": []}\n  },\n  \"latex_code\": \"\\\\documentclass[11pt,a4paper]{article}\\n\\\\usepackage[margin=1in]{geometry}\\n\\\\usepackage{enumitem}\\n\\\\begin{document}\\n\\\\begin{center}\\n\\\\textbf{\\\\LARGE NAME}\\\\\\\\\\nEMAIL \\\\textbullet{} PHONE\\n\\\\end{center}\\n\\\\section{Professional Summary}\\nSUMMARY TEXT\\n\\\\section{Experience}\\nEXPERIENCE CONTENT\\n\\\\section{Education}\\nEDUCATION CONTENT\\n\\\\section{Skills}\\nSKILLS CONTENT\\n\\\\end{document}\",\n  \"scores\": {\n    \"overall_score\": 85,\n    \"ats_compatibility_score\": 90,\n    \"job_match_score\": 88,\n    \"keyword_density_score\": 85,\n    \"formatting_score\": 95,\n    \"content_quality_score\": 87,\n    \"readability_score\": 90,\n    \"impact_score\": 85\n  },\n  \"improvements_made\": [],\n  \"keywords_added\": [],\n  \"metadata\": {\n    \"processing_time\": \"45 seconds\",\n    \"optimization_level\": \"professional\",\n    \"ats_systems_optimized_for\": [\"Workday\", \"Taleo\", \"ATS\", \"Greenhouse\", \"Lever\", \"SuccessFactors\"],\n    \"industry_focus\": \"\",\n    \"target_role\": \"\",\n    \"ai_model_used\": \"gemini-2.5-flash\",\n    \"optimization_strategy\": \"comprehensive\"\n  }\n}\n\nEnsure the LaTeX code is SIMPLE and uses only basic article class with simple formatting.\n\nProvide response in this EXACT JSON structure:\n\n{\n  \"parsed_resume\": {\n    \"contact_info\": {\"name\": \"\", \"email\": \"\", \"phone\": \"\", \"address\": \"\", \"linkedin\": \"\"},\n    \"summary\": \"\",\n    \"experience\": [{\"company\": \"\", \"position\": \"\", \"duration\": \"\", \"location\": \"\", \"achievements\": []}],\n    \"education\": [{\"institution\": \"\", \"degree\": \"\", \"year\": \"\", \"gpa\": \"\", \"honors\": []}],\n    \"skills\": {\"technical\": [], \"soft\": [], \"languages\": [], \"certifications\": []},\n    \"certifications\": [{\"name\": \"\", \"issuer\": \"\", \"date\": \"\", \"credential_id\": \"\"}],\n    \"additional_sections\": {\"projects\": [], \"publications\": [], \"awards\": [], \"volunteer\": []}\n  },\n  \"analysis\": {\n    \"strengths\": [],\n    \"weaknesses\": [],\n    \"ats_compatibility_issues\": [],\n    \"keyword_gaps\": [],\n    \"formatting_issues\": [],\n    \"content_recommendations\": [],\n    \"industry_alignment\": \"\",\n    \"role_fit_analysis\": \"\"\n  },\n  \"optimized_resume\": {\n    \"contact_info\": {\"name\": \"\", \"email\": \"\", \"phone\": \"\", \"address\": \"\", \"linkedin\": \"\"},\n    \"professional_summary\": \"\",\n    \"experience\": [{\"company\": \"\", \"position\": \"\", \"duration\": \"\", \"location\": \"\", \"achievements\": []}],\n    \"education\": [{\"institution\": \"\", \"degree\": \"\", \"year\": \"\", \"gpa\": \"\", \"honors\": []}],\n    \"skills\": {\"technical\": [], \"soft\": [], \"languages\": [], \"certifications\": []},\n    \"certifications\": [{\"name\": \"\", \"issuer\": \"\", \"date\": \"\", \"credential_id\": \"\"}],\n    \"additional_sections\": {\"projects\": [], \"publications\": [], \"awards\": [], \"volunteer\": []}\n  },\n  \"latex_code\": \"\\\\documentclass[11pt,a4paper]{article}\\n\\\\usepackage[margin=1in]{geometry}\\n\\\\usepackage{enumitem}\\n\\\\begin{document}\\n\\\\begin{center}\\n\\\\textbf{\\\\LARGE NAME}\\\\\\\\\\nEMAIL \\\\textbullet{} PHONE\\n\\\\end{center}\\n\\\\section{Professional Summary}\\nSUMMARY TEXT\\n\\\\section{Experience}\\nEXPERIENCE CONTENT\\n\\\\section{Education}\\nEDUCATION CONTENT\\n\\\\section{Skills}\\nSKILLS CONTENT\\n\\\\end{document}\",\n  \"scores\": {\n    \"overall_score\": 85,\n    \"ats_compatibility_score\": 90,\n    \"job_match_score\": 88,\n    \"keyword_density_score\": 85,\n    \"formatting_score\": 95,\n    \"content_quality_score\": 87,\n    \"readability_score\": 90,\n    \"impact_score\": 85\n  },\n  \"improvements_made\": [],\n  \"keywords_added\": [],\n  \"metadata\": {\n    \"processing_time\": \"45 seconds\",\n    \"optimization_level\": \"professional\",\n    \"ats_systems_optimized_for\": [\"Workday\", \"Taleo\", \"ATS\", \"Greenhouse\", \"Lever\", \"SuccessFactors\"],\n    \"industry_focus\": \"\",\n    \"target_role\": \"\",\n    \"ai_model_used\": \"gemini-2.5-flash\",\n    \"optimization_strategy\": \"comprehensive\"\n  }\n}\n\nEnsure the LaTeX code is SIMPLE and uses only basic article class with simple formatting."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.2, "position": [640, 352], "id": "1c46b158-41a4-47da-bdd3-dea2edd2aced", "name": "AI Agent"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [64, 32], "id": "e376b26f-4ddf-4145-884b-c70b05b80494", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "ACJ0M5ako03w0Q6F", "name": "Google Gemini(PaLM) Api account 2"}}, "disabled": true}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolCalculator", "typeVersion": 1, "position": [640, 576], "id": "efb296de-fca2-4813-8b8e-4956e33e8c31", "name": "Calculator"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1.1, "position": [768, 576], "id": "b494295a-87ee-4e6d-a1fe-8b7c3b8e42ae", "name": "Think"}, {"parameters": {"model": {"__rl": true, "value": "gpt-5-mini", "mode": "list", "cachedResultName": "gpt-5-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [512, 576], "id": "41cb09a5-486b-4b2e-89c9-be7d9a1f6892", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "pFbTVEFwuXH3wP6L", "name": "OpenAi account"}}}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"parsed_resume\": {\n    \"contact_info\": {\"name\": \"\", \"email\": \"\", \"phone\": \"\", \"address\": \"\", \"linkedin\": \"\"},\n    \"summary\": \"\",\n    \"experience\": [{\"company\": \"\", \"position\": \"\", \"duration\": \"\", \"location\": \"\", \"achievements\": []}],\n    \"education\": [{\"institution\": \"\", \"degree\": \"\", \"year\": \"\", \"gpa\": \"\", \"honors\": []}],\n    \"skills\": {\"technical\": [], \"soft\": [], \"languages\": [], \"certifications\": []},\n    \"certifications\": [{\"name\": \"\", \"issuer\": \"\", \"date\": \"\", \"credential_id\": \"\"}],\n    \"additional_sections\": {\"projects\": [], \"publications\": [], \"awards\": [], \"volunteer\": []}\n  },\n  \"analysis\": {\n    \"strengths\": [],\n    \"weaknesses\": [],\n    \"ats_compatibility_issues\": [],\n    \"keyword_gaps\": [],\n    \"formatting_issues\": [],\n    \"content_recommendations\": [],\n    \"industry_alignment\": \"\",\n    \"role_fit_analysis\": \"\"\n  },\n  \"optimized_resume\": {\n    \"contact_info\": {\"name\": \"\", \"email\": \"\", \"phone\": \"\", \"address\": \"\", \"linkedin\": \"\"},\n    \"professional_summary\": \"\",\n    \"experience\": [{\"company\": \"\", \"position\": \"\", \"duration\": \"\", \"location\": \"\", \"achievements\": []}],\n    \"education\": [{\"institution\": \"\", \"degree\": \"\", \"year\": \"\", \"gpa\": \"\", \"honors\": []}],\n    \"skills\": {\"technical\": [], \"soft\": [], \"languages\": [], \"certifications\": []},\n    \"certifications\": [{\"name\": \"\", \"issuer\": \"\", \"date\": \"\", \"credential_id\": \"\"}],\n    \"additional_sections\": {\"projects\": [], \"publications\": [], \"awards\": [], \"volunteer\": []}\n  },\n  \"latex_code\": \"\\\\documentclass[11pt,a4paper]{article}\\n\\\\usepackage[margin=1in]{geometry}\\n\\\\usepackage{enumitem}\\n\\\\begin{document}\\n\\\\begin{center}\\n\\\\textbf{\\\\LARGE NAME}\\\\\\\\\\nEMAIL \\\\textbullet{} PHONE\\n\\\\end{center}\\n\\\\section{Professional Summary}\\nSUMMARY TEXT\\n\\\\section{Experience}\\nEXPERIENCE CONTENT\\n\\\\section{Education}\\nEDUCATION CONTENT\\n\\\\section{Skills}\\nSKILLS CONTENT\\n\\\\end{document}\",\n  \"scores\": {\n    \"overall_score\": 85,\n    \"ats_compatibility_score\": 90,\n    \"job_match_score\": 88,\n    \"keyword_density_score\": 85,\n    \"formatting_score\": 95,\n    \"content_quality_score\": 87,\n    \"readability_score\": 90,\n    \"impact_score\": 85\n  },\n  \"improvements_made\": [],\n  \"keywords_added\": [],\n  \"metadata\": {\n    \"processing_time\": \"45 seconds\",\n    \"optimization_level\": \"professional\",\n    \"ats_systems_optimized_for\": [\"Workday\", \"Taleo\", \"ATS\", \"Greenhouse\", \"Lever\", \"SuccessFactors\"],\n    \"industry_focus\": \"\",\n    \"target_role\": \"\",\n    \"ai_model_used\": \"gemini-2.5-flash\",\n    \"optimization_strategy\": \"comprehensive\"\n  }\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.3, "position": [896, 576], "id": "0392a878-a243-4290-b6a6-508a6448c703", "name": "Structured Output Parser"}], "connections": {"Advanced Response Parser": {"main": [[{"index": 0, "node": "LaTeX Sanitizer", "type": "main"}]]}, "Elite AI Resume Optimizer": {"main": [[]]}, "LaTeX Sanitizer": {"main": [[{"index": 0, "node": "Professional LaTeX Converter", "type": "main"}]]}, "Professional Data Processor": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Professional LaTeX Converter": {"main": [[{"index": 0, "node": "Professional PDF Generator", "type": "main"}]]}, "Professional PDF Generator": {"main": [[{"index": 0, "node": "Professional Response", "type": "main"}]]}, "Resume Webhook": {"main": [[{"index": 0, "node": "Professional Data Processor", "type": "main"}]]}, "AI Agent": {"main": [[{"node": "Advanced Response Parser", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[]]}, "Calculator": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Think": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}}, "settings": {"executionOrder": "v1", "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "saveExecutionProgress": true, "saveManualExecutions": true}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "ec9f55db-8f81-49f5-bbac-7f09091de9bb", "triggerCount": 1, "shared": [{"createdAt": "2025-08-09T23:15:19.857Z", "updatedAt": "2025-08-09T23:15:19.857Z", "role": "workflow:owner", "workflowId": "gP20OZzhn0PDYDUi", "projectId": "Lo23ejIfR9bjQpdA", "project": {"createdAt": "2024-11-05T11:38:47.010Z", "updatedAt": "2024-11-05T11:41:59.590Z", "id": "Lo23ejIfR9bjQpdA", "name": "eshetu feleke <<EMAIL>>", "type": "personal", "icon": null, "description": null, "projectRelations": [{"createdAt": "2024-11-05T11:38:47.010Z", "updatedAt": "2024-11-05T11:38:47.010Z", "role": "project:personal<PERSON><PERSON>er", "userId": "19b2db05-f5a4-470e-a9f2-57d21a08a524", "projectId": "Lo23ejIfR9bjQpdA", "user": {"createdAt": "2024-11-05T11:38:42.062Z", "updatedAt": "2025-08-10T05:46:58.790Z", "id": "19b2db05-f5a4-470e-a9f2-57d21a08a524", "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "feleke", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2024-11-05T11:42:23.556Z", "personalization_survey_n8n_version": "1.66.0", "automationGoalDevops": [], "companySize": "<20", "companyType": "saas", "role": "it", "reportedSource": "google"}, "settings": {"userActivated": true, "firstSuccessfulWorkflowId": "GvPrj0fhvatAHsiK", "userActivatedAt": 1730836165456, "isOnboarded": true, "npsSurvey": {"responded": true, "lastShownAt": 1749765842298}, "easyAIWorkflowOnboarded": true}, "role": "global:owner", "disabled": false, "mfaEnabled": false, "lastActiveAt": "2025-08-10", "isPending": false}}]}}], "tags": []}