# LaTeX Compilation Service
import os
import subprocess
import tempfile
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import asyncio
import shutil

from ..models.file_models import CompilationJob, CompilationStatus


class LaTeXCompilationService:
    """Service for compiling LaTeX documents to PDF"""

    def __init__(self, work_dir: str = None):
        self.work_dir = Path(work_dir) if work_dir else Path(tempfile.gettempdir()) / "latex_compile"
        self.work_dir.mkdir(exist_ok=True, parents=True)
        self.jobs: Dict[str, CompilationJob] = {}

    def create_job(self, latex_content: str, job_id: str = None) -> CompilationJob:
        """Create a new compilation job"""
        if not job_id:
            job_id = str(uuid.uuid4())

        job = CompilationJob(
            id=job_id,
            status=CompilationStatus.PENDING,
            content=latex_content,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

        self.jobs[job_id] = job
        return job

    def get_job(self, job_id: str) -> Optional[CompilationJob]:
        """Get a compilation job by ID"""
        return self.jobs.get(job_id)

    def list_jobs(self) -> List[CompilationJob]:
        """List all compilation jobs"""
        return list(self.jobs.values())

    async def compile_latex(self, job_id: str) -> CompilationJob:
        """Compile LaTeX content to PDF"""
        job = self.jobs.get(job_id)
        if not job:
            raise ValueError(f"Job {job_id} not found")

        job.status = CompilationStatus.COMPILING
        job.updated_at = datetime.utcnow()

        try:
            # Create temporary directory for this compilation
            job_dir = self.work_dir / job_id
            job_dir.mkdir(exist_ok=True, parents=True)

            # Write LaTeX content to file
            tex_file = job_dir / "document.tex"
            with open(tex_file, 'w', encoding='utf-8') as f:
                f.write(job.content)

            # Run pdflatex compilation
            pdf_path = await self._run_pdflatex(tex_file, job_dir)

            if pdf_path and pdf_path.exists():
                # Store PDF data
                with open(pdf_path, 'rb') as f:
                    job.pdf_data = f.read()

                job.status = CompilationStatus.SUCCESS
                job.pdf_url = f"/api/latex/download/{job_id}"
                job.logs = "LaTeX compilation completed successfully"

            else:
                raise Exception("PDF file was not generated")

        except Exception as e:
            job.status = CompilationStatus.ERROR
            job.error = str(e)
            job.logs = f"Compilation failed: {str(e)}"

        finally:
            job.updated_at = datetime.utcnow()
            # Clean up temporary files (optional - might want to keep for debugging)
            # self._cleanup_job_directory(job_dir)

        return job

    async def _run_pdflatex(self, tex_file: Path, work_dir: Path) -> Optional[Path]:
        """Run pdflatex compilation using Docker"""
        try:
            # Use Docker for LaTeX compilation
            pdf_file = work_dir / "document.pdf"

            # Docker command to run LaTeX compilation
            cmd = [
                'docker', 'run', '--rm',
                '-v', f"{work_dir}:/latex",
                '-w', '/latex',
                'latex-compiler',
                'pdflatex', '-interaction=nonstopmode', 'document.tex'
            ]

            # Run twice to resolve references
            for i in range(2):
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    cwd=work_dir,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )

                stdout, stderr = await process.communicate()

                if process.returncode != 0:
                    error_msg = stderr.decode('utf-8') if stderr else "Unknown compilation error"
                    # Try to extract meaningful error from log
                    log_file = work_dir / "document.log"
                    if log_file.exists():
                        with open(log_file, 'r', encoding='utf-8') as f:
                            log_content = f.read()
                            # Extract error information from log
                            error_msg = self._extract_latex_error(log_content)

                    raise Exception(f"pdflatex failed: {error_msg}")

            pdf_path = work_dir / "document.pdf"
            return pdf_path if pdf_path.exists() else None

        except Exception as e:
            raise Exception(f"LaTeX compilation error: {str(e)}")

    def _extract_latex_error(self, log_content: str) -> str:
        """Extract meaningful error message from LaTeX log"""
        lines = log_content.split('\n')
        errors = []

        for i, line in enumerate(lines):
            if line.startswith('!'):
                # LaTeX error line
                errors.append(line[1:].strip())
                # Try to get the next few lines for context
                for j in range(i + 1, min(i + 4, len(lines))):
                    if lines[j].strip() and not lines[j].startswith('l.'):
                        errors.append(lines[j].strip())
                    if lines[j].startswith('l.'):
                        errors.append(f"Line {lines[j][2:].strip()}")
                        break
                break

        return '; '.join(errors) if errors else "Unknown LaTeX error"

    def _cleanup_job_directory(self, job_dir: Path):
        """Clean up temporary compilation directory"""
        try:
            if job_dir.exists():
                shutil.rmtree(job_dir)
        except Exception:
            # Ignore cleanup errors
            pass

    def delete_job(self, job_id: str) -> bool:
        """Delete a compilation job"""
        if job_id in self.jobs:
            # Clean up job directory
            job_dir = self.work_dir / job_id
            self._cleanup_job_directory(job_dir)

            del self.jobs[job_id]
            return True
        return False

    def get_pdf_data(self, job_id: str) -> Optional[bytes]:
        """Get compiled PDF data"""
        job = self.jobs.get(job_id)
        if job and job.status == CompilationStatus.SUCCESS and hasattr(job, 'pdf_data'):
            return job.pdf_data
        return None


# Global service instance
latex_service = LaTeXCompilationService()
