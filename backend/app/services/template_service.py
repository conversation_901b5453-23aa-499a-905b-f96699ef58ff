"""
LaTeX Template Management Service
"""
import os
import json
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

from ..models.file_models import BaseModel, Field


class TemplateVariable(BaseModel):
    """Template variable definition"""
    name: str = Field(..., description="Variable name")
    type: str = Field(..., description="Variable type: string, array, object")
    required: bool = Field(True, description="Whether variable is required")
    default: Optional[Any] = Field(None, description="Default value")
    description: str = Field(..., description="Variable description")
    example: Optional[Any] = Field(None, description="Example value")


class TemplateMetadata(BaseModel):
    """Template metadata"""
    id: str = Field(..., description="Template ID")
    name: str = Field(..., description="Template name")
    description: str = Field(..., description="Template description")
    category: str = Field(..., description="Template category")
    author: str = Field(..., description="Template author")
    version: str = Field(..., description="Template version")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    variables: List[TemplateVariable] = Field(..., description="Template variables")
    tags: List[str] = Field(default_factory=list, description="Template tags")
    preview_image: Optional[str] = Field(None, description="Preview image URL")


class LaTeXTemplate(BaseModel):
    """Complete LaTeX template"""
    metadata: TemplateMetadata = Field(..., description="Template metadata")
    content: str = Field(..., description="LaTeX template content")
    sample_data: Dict[str, Any] = Field(..., description="Sample data for template")


class TemplateService:
    """Service for managing LaTeX templates"""

    def __init__(self, templates_dir: str = None):
        if templates_dir:
            self.templates_dir = Path(templates_dir)
        else:
            self.templates_dir = Path(__file__).parent.parent / "templates"

        self.templates_dir.mkdir(exist_ok=True, parents=True)
        self._templates_cache: Dict[str, LaTeXTemplate] = {}
        self._load_templates()

    def _load_templates(self):
        """Load all templates from directory"""
        self._templates_cache.clear()

        # Load predefined templates
        predefined_templates = [
            self._create_modern_template(),
            self._create_classic_template(),
            self._create_tech_template(),
        ]

        for template in predefined_templates:
            self._templates_cache[template.metadata.id] = template

    def _create_modern_template(self) -> LaTeXTemplate:
        """Create modern resume template"""
        metadata = TemplateMetadata(
            id="modern_resume",
            name="Modern Resume",
            description="A clean, modern resume template with color accents",
            category="Professional",
            author="AI Resume System",
            version="1.0.0",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            variables=[
                TemplateVariable(
                    name="name",
                    type="string",
                    required=True,
                    description="Full name",
                    example="John Doe"
                ),
                TemplateVariable(
                    name="phone",
                    type="string",
                    required=True,
                    description="Phone number",
                    example="(*************"
                ),
                TemplateVariable(
                    name="email",
                    type="string",
                    required=True,
                    description="Email address",
                    example="<EMAIL>"
                ),
                TemplateVariable(
                    name="linkedin_url",
                    type="string",
                    required=False,
                    description="LinkedIn profile URL",
                    example="https://linkedin.com/in/johndoe"
                ),
                TemplateVariable(
                    name="github_url",
                    type="string",
                    required=False,
                    description="GitHub profile URL",
                    example="https://github.com/johndoe"
                ),
                TemplateVariable(
                    name="location",
                    type="string",
                    required=True,
                    description="Location",
                    example="New York, NY"
                ),
                TemplateVariable(
                    name="university",
                    type="string",
                    required=True,
                    description="University name",
                    example="University of Example"
                ),
                TemplateVariable(
                    name="degree",
                    type="string",
                    required=True,
                    description="Degree",
                    example="Bachelor of Science in Computer Science"
                ),
                TemplateVariable(
                    name="graduation_date",
                    type="string",
                    required=True,
                    description="Graduation date",
                    example="May 2023"
                ),
                TemplateVariable(
                    name="gpa",
                    type="string",
                    required=False,
                    description="GPA",
                    example="3.8/4.0"
                ),
                TemplateVariable(
                    name="experience",
                    type="array",
                    required=True,
                    description="Work experience array",
                    example=[{
                        "company": "Tech Corp",
                        "position": "Software Engineer",
                        "dates": "Jun 2023 - Present",
                        "location": "San Francisco, CA",
                        "responsibilities": [
                            "Developed web applications using React and Node.js",
                            "Improved system performance by 40% through optimization"
                        ]
                    }]
                ),
                TemplateVariable(
                    name="projects",
                    type="array",
                    required=True,
                    description="Projects array",
                    example=[{
                        "name": "Task Manager App",
                        "technologies": "React, Node.js, MongoDB",
                        "date": "Jan 2023",
                        "description": [
                            "Built full-stack task management application",
                            "Implemented user authentication and real-time updates"
                        ]
                    }]
                ),
                TemplateVariable(
                    name="skills",
                    type="object",
                    required=True,
                    description="Skills object",
                    example={
                        "languages": "JavaScript, Python, Java, C++",
                        "frameworks": "React, Node.js, Express, Django",
                        "tools": "Git, Docker, AWS, MongoDB",
                        "libraries": "NumPy, Pandas, TensorFlow, jQuery"
                    }
                )
            ],
            tags=["modern", "professional", "color", "clean"]
        )

        # Load template content
        template_file = self.templates_dir / "modern_resume.tex"
        content = template_file.read_text() if template_file.exists() else ""

        # Sample data for preview
        sample_data = {
            "name": "John Doe",
            "phone": "(*************",
            "email": "<EMAIL>",
            "linkedin_url": "https://linkedin.com/in/johndoe",
            "github_url": "https://github.com/johndoe",
            "location": "New York, NY",
            "university": "University of Example",
            "degree": "Bachelor of Science in Computer Science",
            "graduation_date": "May 2023",
            "gpa": "3.8/4.0",
            "experience": [{
                "company": "Tech Corp",
                "position": "Software Engineer",
                "dates": "Jun 2023 - Present",
                "location": "San Francisco, CA",
                "responsibilities": [
                    "Developed web applications using React and Node.js",
                    "Improved system performance by 40% through optimization",
                    "Led code reviews and mentored junior developers"
                ]
            }],
            "projects": [{
                "name": "Task Manager App",
                "technologies": "React, Node.js, MongoDB",
                "date": "Jan 2023",
                "description": [
                    "Built full-stack task management application",
                    "Implemented user authentication and real-time updates"
                ]
            }],
            "skills": {
                "languages": "JavaScript, Python, Java, C++",
                "frameworks": "React, Node.js, Express, Django",
                "tools": "Git, Docker, AWS, MongoDB",
                "libraries": "NumPy, Pandas, TensorFlow, jQuery"
            }
        }

        return LaTeXTemplate(
            metadata=metadata,
            content=content,
            sample_data=sample_data
        )

    def _create_classic_template(self) -> LaTeXTemplate:
        """Create classic resume template"""
        metadata = TemplateMetadata(
            id="classic_resume",
            name="Classic Resume",
            description="Traditional, professional resume template",
            category="Traditional",
            author="AI Resume System",
            version="1.0.0",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            variables=[
                TemplateVariable(name="name", type="string", required=True, description="Full name"),
                TemplateVariable(name="address", type="string", required=True, description="Full address"),
                TemplateVariable(name="phone", type="string", required=True, description="Phone number"),
                TemplateVariable(name="email", type="string", required=True, description="Email address"),
                TemplateVariable(name="website", type="string", required=False, description="Personal website"),
                TemplateVariable(name="objective", type="string", required=True, description="Career objective"),
                TemplateVariable(name="education", type="array", required=True, description="Education history"),
                TemplateVariable(name="experience", type="array", required=True, description="Work experience"),
                TemplateVariable(name="skills", type="object", required=True, description="Technical skills"),
                TemplateVariable(name="projects", type="array", required=False, description="Projects"),
                TemplateVariable(name="awards", type="array", required=False, description="Awards and achievements"),
            ],
            tags=["classic", "traditional", "professional", "simple"]
        )

        template_file = self.templates_dir / "classic_resume.tex"
        content = template_file.read_text() if template_file.exists() else ""

        sample_data = {
            "name": "Jane Smith",
            "address": "123 Main Street, Boston, MA 02101",
            "phone": "(*************",
            "email": "<EMAIL>",
            "website": "www.janesmith.com",
            "objective": "Seeking a challenging position as a Software Engineer where I can utilize my technical skills and contribute to innovative projects.",
            "education": [{
                "institution": "Massachusetts Institute of Technology",
                "degree": "Master of Science in Computer Science",
                "dates": "2021-2023",
                "location": "Cambridge, MA",
                "gpa": "3.9/4.0"
            }],
            "experience": [{
                "company": "Innovation Labs",
                "position": "Senior Software Developer",
                "dates": "2023-Present",
                "location": "Boston, MA",
                "responsibilities": [
                    "Lead development of cloud-based applications",
                    "Architect scalable microservices solutions",
                    "Mentor team of 5 junior developers"
                ]
            }],
            "skills": {
                "languages": "Python, JavaScript, Go, Java",
                "technologies": "AWS, Docker, Kubernetes, React",
                "tools": "Git, Jenkins, Jira, VS Code"
            },
            "projects": [{
                "name": "E-commerce Platform",
                "date": "2023",
                "description": "Microservices-based e-commerce solution",
                "details": [
                    "Built using Go and React with PostgreSQL database",
                    "Deployed on AWS with 99.9% uptime"
                ]
            }]
        }

        return LaTeXTemplate(
            metadata=metadata,
            content=content,
            sample_data=sample_data
        )

    def _create_tech_template(self) -> LaTeXTemplate:
        """Create tech-focused resume template"""
        metadata = TemplateMetadata(
            id="tech_resume",
            name="Tech Resume",
            description="ATS-friendly resume template optimized for tech roles",
            category="Technology",
            author="AI Resume System",
            version="1.0.0",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            variables=[
                TemplateVariable(name="name", type="string", required=True, description="Full name"),
                TemplateVariable(name="phone", type="string", required=True, description="Phone number"),
                TemplateVariable(name="email", type="string", required=True, description="Email address"),
                TemplateVariable(name="linkedin", type="string", required=True, description="LinkedIn URL"),
                TemplateVariable(name="linkedin_handle", type="string", required=True, description="LinkedIn handle"),
                TemplateVariable(name="github", type="string", required=True, description="GitHub URL"),
                TemplateVariable(name="github_handle", type="string", required=True, description="GitHub handle"),
                TemplateVariable(name="university", type="string", required=True, description="University"),
                TemplateVariable(name="degree", type="string", required=True, description="Degree"),
                TemplateVariable(name="gpa", type="string", required=False, description="GPA"),
                TemplateVariable(name="graduation_date", type="string", required=True, description="Graduation date"),
                TemplateVariable(name="location", type="string", required=True, description="University location"),
                TemplateVariable(name="experience", type="array", required=True, description="Work experience"),
                TemplateVariable(name="projects", type="array", required=True, description="Technical projects"),
                TemplateVariable(name="skills", type="object", required=True, description="Technical skills")
            ],
            tags=["tech", "ats-friendly", "software-engineering", "clean"]
        )

        template_file = self.templates_dir / "tech_resume.tex"
        content = template_file.read_text() if template_file.exists() else ""

        sample_data = {
            "name": "Alex Johnson",
            "phone": "(*************",
            "email": "<EMAIL>",
            "linkedin": "https://linkedin.com/in/alexjohnson",
            "linkedin_handle": "alexjohnson",
            "github": "https://github.com/alexjohnson",
            "github_handle": "alexjohnson",
            "university": "Stanford University",
            "degree": "Bachelor of Science in Computer Science",
            "gpa": "3.85",
            "graduation_date": "June 2023",
            "location": "Stanford, CA",
            "experience": [{
                "company": "Google",
                "position": "Software Engineer Intern",
                "dates": "Jun 2022 -- Aug 2022",
                "location": "Mountain View, CA",
                "achievements": [
                    "Developed machine learning models for search ranking optimization",
                    "Improved click-through rates by 15% through A/B testing",
                    "Built data pipeline processing 10M+ daily events"
                ]
            }],
            "projects": [{
                "name": "Distributed Chat Application",
                "tech_stack": "Go, React, WebSocket, Redis, Docker",
                "date": "Mar 2023",
                "highlights": [
                    "Built real-time chat application supporting 1000+ concurrent users",
                    "Implemented horizontal scaling with Redis pub/sub",
                    "Deployed using Docker containers on AWS ECS"
                ]
            }],
            "skills": {
                "languages": "Python, Go, JavaScript, C++, Java, SQL",
                "frameworks": "React, Node.js, Django, Flask, Express",
                "tools": "Docker, Kubernetes, AWS, Git, Jenkins",
                "libraries": "TensorFlow, PyTorch, NumPy, Pandas"
            }
        }

        return LaTeXTemplate(
            metadata=metadata,
            content=content,
            sample_data=sample_data
        )

    def get_template(self, template_id: str) -> Optional[LaTeXTemplate]:
        """Get template by ID"""
        return self._templates_cache.get(template_id)

    def list_templates(self, category: Optional[str] = None) -> List[TemplateMetadata]:
        """List all templates or filter by category"""
        templates = list(self._templates_cache.values())

        if category:
            templates = [t for t in templates if t.metadata.category.lower() == category.lower()]

        return [t.metadata for t in templates]

    def get_template_categories(self) -> List[str]:
        """Get all template categories"""
        categories = set()
        for template in self._templates_cache.values():
            categories.add(template.metadata.category)
        return sorted(list(categories))

    def render_template(self, template_id: str, data: Dict[str, Any]) -> str:
        """Render template with provided data"""
        template = self.get_template(template_id)
        if not template:
            raise ValueError(f"Template {template_id} not found")

        # Simple template rendering (replace {{variable}} with values)
        content = template.content

        # Handle simple variable substitution
        for key, value in data.items():
            if isinstance(value, str):
                content = content.replace(f"{{{{{key}}}}}", str(value))
            elif isinstance(value, (int, float)):
                content = content.replace(f"{{{{{key}}}}}", str(value))

        # For more complex templating with arrays/objects, you'd want to use
        # a proper template engine like Jinja2, but this handles basic cases

        return content


# Global template service instance
template_service = TemplateService()
