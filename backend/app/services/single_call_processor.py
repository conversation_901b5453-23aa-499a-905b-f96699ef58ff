"""
Single-call AI processor for resume optimization.
Replaces the multi-agent CrewAI approach with a single comprehensive API call.
"""

import json
import logging
import time
from typing import Dict, Any, Optional
from datetime import datetime
import litellm
from ..core.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SingleCallProcessor:
    """
    Processes resumes with a single comprehensive API call instead of multiple agents.
    This reduces API calls from 50-100+ to just 1, solving rate limiting issues.
    """

    def __init__(self):
        # Configure LiteLLM for Gemini
        litellm.set_verbose = False  # Reduce verbose logging

    def process_resume(self, resume_text: str, job_description: str) -> Dict[str, Any]:
        """
        Process resume with a single comprehensive API call.

        Args:
            resume_text: The original resume text
            job_description: Target job description for optimization

        Returns:
            Dictionary containing all processing results
        """
        logger.info("🚀 Starting single-call resume processing")
        start_time = datetime.now()

        try:
            # Create the comprehensive prompt
            prompt = self._create_comprehensive_prompt(resume_text, job_description)

            # Make single API call with retry logic for rate limits
            logger.info("📞 Making single API call to Gemini...")
            max_retries = 3
            retry_delay = 60  # Start with 60 seconds for rate limits

            for attempt in range(max_retries):
                try:
                    response = litellm.completion(
                        model="gemini/gemini-1.5-flash",  # Use stable model instead of experimental
                        messages=[
                            {
                                "role": "system",
                                "content": "You are an expert resume writer and ATS optimization specialist."
                            },
                            {
                                "role": "user",
                                "content": prompt
                            }
                        ],
                        temperature=0.3,
                        max_tokens=6000,  # Reduce token limit
                        api_key=settings.GEMINI_API_KEY
                    )
                    break  # Success, exit retry loop

                except litellm.RateLimitError as e:
                    logger.warning(f"⚠️ Rate limit hit on attempt {attempt + 1}/{max_retries}")
                    if attempt < max_retries - 1:
                        wait_time = retry_delay * (2 ** attempt)  # Exponential backoff: 60s, 120s, 240s
                        logger.info(f"⏳ Waiting {wait_time} seconds before retry...")
                        time.sleep(wait_time)
                    else:
                        logger.error("❌ Max retries exceeded for rate limit")
                        # Return a fallback response instead of crashing
                        logger.info("🔄 Returning fallback optimization...")
                        return self._create_fallback_response(resume_text, job_description)
                except Exception as e:
                    logger.error(f"❌ API call failed on attempt {attempt + 1}: {str(e)}")
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay)
                        retry_delay *= 2
                    else:
                        raise e

            # Parse the response
            result = self._parse_response(response.choices[0].message.content)

            # Add metadata
            processing_time = (datetime.now() - start_time).total_seconds()
            result["metadata"] = {
                "processing_time_seconds": processing_time,
                "api_calls_made": 1,
                "method": "single_call",
                "processed_at": datetime.now().isoformat()
            }

            logger.info(f"✅ Single-call processing completed in {processing_time:.2f} seconds")
            return result

        except Exception as e:
            logger.error(f"❌ Error in single-call processing: {str(e)}")
            raise e

    def _create_comprehensive_prompt(self, resume_text: str, job_description: str) -> str:
        """Create a comprehensive prompt that handles all processing in one call."""

        return f"""
Optimize this resume for the target role while maintaining 100% factual accuracy.

**RESUME:**
{resume_text}

**JOB:**
{job_description}

**RULES:**
- NEVER fabricate information
- NEVER change company names, titles, or dates
- Only rephrase existing content
- Emphasize relevant experience
- Use keywords from job description naturally

**APPROACH:**
1. Parse resume exactly as provided
2. Identify most relevant experiences for target role
3. Rephrase descriptions to highlight relevance
4. Provide realistic scoring based on actual fit

**RESPONSE FORMAT** (Return ONLY valid JSON):

You must return a complete, valid JSON response. Use ONLY the actual data from the resume - DO NOT FABRICATE ANYTHING.

**CRITICAL INSTRUCTIONS FOR EACH SECTION:**

1. **parsed_resume**: Extract information EXACTLY as it appears in the original resume
2. **optimized_resume**: Use the SAME factual information but rephrase for better presentation
3. **NEVER change**: Company names, job titles, dates, education details, or add fake information

```json
{{
    "parsed_resume": {{
        "contact_info": {{
            "name": "[EXACT name from resume]",
            "email": "[EXACT email from resume]",
            "phone": "[EXACT phone from resume]",
            "location": "[EXACT location from resume]",
            "linkedin": "[EXACT linkedin from resume]",
            "github": "[EXACT github if present, empty string if not]"
        }},
        "professional_summary": "[EXACT professional summary from the original resume]",
        "experience": [
            {{
                "title": "[EXACT job title from resume]",
                "company": "[EXACT company name from resume]",
                "duration": "[EXACT dates from resume]",
                "description": "[Brief description based on actual content]",
                "achievements": ["[EXACT bullets/achievements from the original resume]"]
            }}
        ],
        "education": [
            {{
                "degree": "[EXACT degree from resume]",
                "institution": "[EXACT institution from resume]",
                "graduation_date": "[EXACT dates from resume]",
                "gpa": "[EXACT GPA if mentioned, null if not]",
                "relevant_coursework": []
            }}
        ],
        "skills": {{
            "technical": ["[EXACT technical skills listed in resume]"],
            "soft": ["[EXACT or clearly implied soft skills from resume]"],
            "tools": ["[EXACT tools/technologies mentioned in resume]"]
        }},
        "projects": [],
        "certifications": ["[EXACT certifications from resume]"]
    }},

    "analysis": {{
        "overall_score": "[Score 0-100 based on actual qualifications match to role requirements]",
        "ats_compatibility_score": "[Score 0-100 for ATS optimization - keywords, formatting, structure]",
        "job_match_score": "[Score 0-100 for how well actual experience matches specific job requirements]",
        "strengths": [
            "[Specific strength 1 with evidence from actual experience]",
            "[Specific strength 2 with evidence from actual experience]",
            "[Specific strength 3 with evidence from actual experience]"
        ],
        "weaknesses": [
            "[Specific gap 1 between actual experience and job requirements]",
            "[Specific gap 2 between actual experience and job requirements]"
        ],
        "missing_keywords": [
            "[Important keyword 1 from job description not in resume]",
            "[Important keyword 2 from job description not in resume]"
        ],
        "recommendations": [
            "[Specific actionable recommendation 1 based on actual background]",
            "[Specific actionable recommendation 2 based on actual background]",
            "[Specific actionable recommendation 3 based on actual background]"
        ],
        "key_matches": [
            "[Direct match 1: specific experience that aligns with job requirement]",
            "[Direct match 2: specific skill that matches job requirement]",
            "[Direct match 3: specific achievement that demonstrates job capability]"
        ],
        "transferable_skills": [
            "[Transferable skill 1 from actual experience applicable to target role]",
            "[Transferable skill 2 from actual experience applicable to target role]"
        ]
    }},

    "optimized_resume": {{
        "contact_info": {{
            "name": "[SAME as parsed - never change]",
            "email": "[SAME as parsed - never change]",
            "phone": "[SAME as parsed - never change]",
            "location": "[SAME as parsed - never change]",
            "linkedin": "[SAME as parsed - never change]",
            "github": "[SAME as parsed - never change]"
        }},
        "professional_summary": "[REWRITE the actual professional summary to better emphasize relevance to the target role, but keep all facts accurate]",
        "experience": [
            {{
                "title": "[SAME job title - never change]",
                "company": "[SAME company - never change]",
                "duration": "[SAME dates - never change]",
                "description": "[REPHRASE the actual job responsibilities to emphasize relevance to target role]",
                "achievements": ["[REPHRASE the actual achievements to highlight relevance, but keep facts accurate]"]
            }}
        ],
        "education": "[SAME education information - never change facts]",
        "skills": {{
            "technical": ["[Reorganize actual technical skills to emphasize most relevant ones first]"],
            "soft": ["[Emphasize actual soft skills that match job requirements]"],
            "tools": ["[Reorganize actual tools to highlight most relevant ones]"]
        }},
        "projects": [],
        "certifications": "[SAME certifications - never change]"
    }},

    "latex_code": "\\documentclass[11pt,a4paper,sans]{{moderncv}}\\n\\moderncvstyle{{banking}}\\n\\moderncvcolor{{blue}}\\n\\n\\usepackage[utf8]{{inputenc}}\\n\\name{{[ACTUAL FIRST NAME]}}{{[ACTUAL LAST NAME]}}\\n\\address{{[ACTUAL ADDRESS]}}\\n\\phone{{[ACTUAL PHONE]}}\\n\\email{{[ACTUAL EMAIL]}}\\n\\homepage{{[ACTUAL WEBSITE]}}\\n\\n\\begin{{document}}\\n\\makecvtitle\\n\\n\\section{{Professional Summary}}\\n[Optimized summary using actual background]\\n\\n\\section{{Experience}}\\n[Actual experience entries with optimized descriptions]\\n\\n\\section{{Education}}\\n[Actual education]\\n\\n\\section{{Skills}}\\n[Actual skills organized by relevance]\\n\\n\\section{{Certifications}}\\n[Actual certifications]\\n\\n\\end{{document}}"
}}
```

REMEMBER: You can REPHRASE and REORGANIZE the actual information to make it more relevant, but you must NEVER FABRICATE, INVENT, or CHANGE factual details like company names, job titles, dates, or add experiences that don't exist."""

    def _parse_response(self, response_text: str) -> Dict[str, Any]:
        """Parse the AI response and extract JSON."""
        logger.info(f"📄 Raw AI response (first 500 chars): {response_text[:500]}...")

        try:
            # Find JSON content between ```json and ```
            start_marker = "```json"
            end_marker = "```"

            start_idx = response_text.find(start_marker)
            if start_idx != -1:
                start_idx += len(start_marker)
                end_idx = response_text.find(end_marker, start_idx)
                if end_idx != -1:
                    json_text = response_text[start_idx:end_idx].strip()
                    logger.info(f"🔍 Extracted JSON (first 300 chars): {json_text[:300]}...")
                    return json.loads(json_text)

            # Fallback: try to parse the entire response as JSON
            logger.info("⚠️ No JSON markers found, trying to parse entire response...")
            return json.loads(response_text.strip())

        except json.JSONDecodeError as e:
            logger.error(f"❌ Failed to parse AI response as JSON: {str(e)}")
            logger.error(f"📄 Full response text: {response_text}")

            # Return a basic structure if parsing fails
            return {
                "parsed_resume": {"error": "Failed to parse resume"},
                "analysis": {"overall_score": 0, "error": "Parsing failed"},
                "optimized_resume": {"error": "Optimization failed"},
                "latex_code": "% Error: Failed to generate LaTeX",
                "error": "Failed to parse AI response"
            }

    def _create_fallback_response(self, resume_text: str, job_description: str) -> Dict[str, Any]:
        """Create a fallback response when API calls fail due to rate limits."""
        logger.info("🔄 Creating fallback optimization response...")

        # Basic parsing of resume text
        lines = resume_text.strip().split('\n')
        name = lines[0] if lines else "Resume Holder"

        # Extract basic info
        contact_info = {
            "name": name,
            "email": "",
            "phone": "",
            "location": "",
            "linkedin": "",
            "github": ""
        }

        # Look for contact info in first few lines
        for line in lines[:5]:
            if "@" in line:
                contact_info["email"] = line.strip()
            elif "+" in line or any(char.isdigit() for char in line):
                contact_info["phone"] = line.strip()

        return {
            "parsed_resume": {
                "contact_info": contact_info,
                "professional_summary": "Experienced professional with relevant background",
                "experience": [],
                "education": [],
                "skills": {"technical": [], "soft": [], "tools": []},
                "projects": [],
                "certifications": []
            },
            "analysis": {
                "overall_score": 70,
                "ats_compatibility_score": 65,
                "job_match_score": 60,
                "strengths": [
                    "Professional experience in relevant field",
                    "Educational background supports role requirements",
                    "Demonstrated career progression"
                ],
                "weaknesses": [
                    "Unable to perform detailed analysis due to API limits",
                    "Recommend manual review of job requirements alignment"
                ],
                "missing_keywords": [],
                "recommendations": [
                    "Wait for API limits to reset for detailed optimization",
                    "Manually review job description for key requirements",
                    "Consider upgrading API plan for better service"
                ],
                "key_matches": [],
                "transferable_skills": []
            },
            "optimized_resume": {
                "contact_info": contact_info,
                "professional_summary": f"Due to API rate limits, detailed optimization is temporarily unavailable. Your resume shows strong potential for the target role. Please try again in a few minutes for full optimization.",
                "experience": [],
                "education": [],
                "skills": {"technical": [], "soft": [], "tools": []},
                "projects": [],
                "certifications": []
            },
            "latex_code": f"% Resume optimization temporarily unavailable due to API limits\n% Please try again in a few minutes\n\\documentclass{{article}}\n\\begin{{document}}\n\\title{{{name} - Resume}}\n\\maketitle\nOptimization temporarily unavailable due to API rate limits.\n\\end{{document}}",
            "error": "API rate limit exceeded - fallback response provided"
        }

    def get_processing_status(self) -> Dict[str, Any]:
        """Get current processing status (for compatibility with existing API)."""
        return {
            "status": "completed",
            "progress": 100,
            "message": "Single-call processing completed",
            "api_calls_used": 1
        }
