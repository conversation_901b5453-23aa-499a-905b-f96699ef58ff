% Modern Resume Template
\documentclass[11pt,letterpaper]{article}

% Packages
\usepackage[utf8]{inputenc}
\usepackage{geometry}
\usepackage{xcolor}
\usepackage{fontawesome5}
\usepackage{titlesec}
\usepackage{enumitem}
\usepackage{hyperref}
\usepackage{tabularx}

% Page setup
\geometry{left=0.75in,right=0.75in,top=0.75in,bottom=0.75in}
\pagestyle{empty}

% Colors
\definecolor{primary}{RGB}{41, 128, 185}
\definecolor{secondary}{RGB}{52, 73, 94}
\definecolor{accent}{RGB}{231, 76, 60}

% Hyperlink setup
\hypersetup{
    colorlinks=true,
    linkcolor=primary,
    urlcolor=primary,
    pdfborder={0 0 0}
}

% Section formatting
\titleformat{\section}{
  \vspace{-4pt}\scshape\raggedright\large\color{primary}
}{}{0em}{}[\color{primary}\titlerule \vspace{-5pt}]

% Custom commands
\newcommand{\resumeItem}[1]{
  \item\small{#1 \vspace{-2pt}}
}

\newcommand{\resumeSubheading}[4]{
  \vspace{-2pt}\item
    \begin{tabular*}{0.97\textwidth}[t]{l@{\extracolsep{\fill}}r}
      \textbf{#1} & #2 \\
      \textit{\small#3} & \textit{\small #4} \\
    \end{tabular*}\vspace{-7pt}
}

\newcommand{\resumeProjectHeading}[2]{
    \item
    \begin{tabular*}{0.97\textwidth}{l@{\extracolsep{\fill}}r}
      \small#1 & #2 \\
    \end{tabular*}\vspace{-7pt}
}

\newcommand{\resumeSubItem}[1]{\resumeItem{#1}\vspace{-4pt}}

\renewcommand\labelitemii{$\vcenter{\hbox{\tiny$\bullet$}}$}

\newcommand{\resumeSubHeadingListStart}{\begin{itemize}[leftmargin=0.15in, label={}]}
\newcommand{\resumeSubHeadingListEnd}{\end{itemize}}
\newcommand{\resumeItemListStart}{\begin{itemize}}
\newcommand{\resumeItemListEnd}{\end{itemize}\vspace{-5pt}}

\begin{document}

%----------HEADING----------
\begin{center}
    \textbf{\Huge \scshape {{name}}} \\ \vspace{1pt}
    \small \faIcon{phone} {{phone}} $|$ \faIcon{envelope} \href{mailto:{{email}}}{{{email}}} $|$
    \faIcon{linkedin} \href{{{linkedin_url}}}{LinkedIn} $|$
    \faIcon{github} \href{{{github_url}}}{GitHub} $|$
    \faIcon{map-marker-alt} {{location}}
\end{center}

%-----------EDUCATION-----------
\section{Education}
  \resumeSubHeadingListStart
    \resumeSubheading
      {{{university}}}{{{graduation_date}}}
      {{{degree}}}{{{gpa}}}
  \resumeSubHeadingListEnd

%-----------EXPERIENCE-----------
\section{Experience}
  \resumeSubHeadingListStart
    {{#each experience}}
    \resumeSubheading
      {{{company}}}{{{dates}}}
      {{{position}}}{{{location}}}
      \resumeItemListStart
        {{#each responsibilities}}
        \resumeItem{{{this}}}
        {{/each}}
      \resumeItemListEnd
    {{/each}}
  \resumeSubHeadingListEnd

%-----------PROJECTS-----------
\section{Projects}
    \resumeSubHeadingListStart
      {{#each projects}}
      \resumeProjectHeading
          {\textbf{{{name}}} $|$ \emph{{{technologies}}}}{{{date}}}
          \resumeItemListStart
            {{#each description}}
            \resumeItem{{{this}}}
            {{/each}}
          \resumeItemListEnd
      {{/each}}
    \resumeSubHeadingListEnd

%-----------TECHNICAL SKILLS-----------
\section{Technical Skills}
 \begin{itemize}[leftmargin=0.15in, label={}]
    \small{\item{
     \textbf{Languages}{: {{skills.languages}}} \\
     \textbf{Frameworks}{: {{skills.frameworks}}} \\
     \textbf{Developer Tools}{: {{skills.tools}}} \\
     \textbf{Libraries}{: {{skills.libraries}}} \\
    }}
 \end{itemize}

\end{document}
