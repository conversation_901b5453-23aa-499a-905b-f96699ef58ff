"""
LaTeX Compilation API Endpoints
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks, Response
from fastapi.responses import StreamingResponse
from typing import List
import io
import tempfile
import subprocess
import os
import logging
from pathlib import Path

from ..services.latex_compiler import latex_service
from ..models.file_models import (
    CompilationRequest,
    CompilationResponse,
    DOCXConversionRequest,
    CompilationJob,
    CompilationStatus
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/compile", response_model=CompilationResponse)
async def compile_latex(
    request: CompilationRequest,
    background_tasks: BackgroundTasks
):
    """
    Start LaTeX compilation job
    """
    try:
        # Create compilation job
        job = latex_service.create_job(request.latex_content, request.job_id)

        # Start compilation in background
        background_tasks.add_task(latex_service.compile_latex, job.id)

        return CompilationResponse(
            job_id=job.id,
            status=job.status,
            message="Compilation job started",
            created_at=job.created_at,
            updated_at=job.updated_at
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start compilation: {str(e)}")


@router.get("/status/{job_id}", response_model=CompilationJob)
async def get_compilation_status(job_id: str):
    """
    Get compilation job status
    """
    job = latex_service.get_job(job_id)

    if not job:
        raise HTTPException(status_code=404, detail=f"Job {job_id} not found")

    return job


@router.get("/jobs", response_model=List[CompilationJob])
async def list_compilation_jobs():
    """
    List all compilation jobs
    """
    return latex_service.list_jobs()


@router.get("/download/{job_id}")
async def download_pdf(job_id: str):
    """
    Download compiled PDF
    """
    job = latex_service.get_job(job_id)

    if not job:
        raise HTTPException(status_code=404, detail=f"Job {job_id} not found")

    if job.status != CompilationStatus.SUCCESS:
        raise HTTPException(
            status_code=400,
            detail=f"PDF not available. Job status: {job.status}"
        )

    pdf_data = latex_service.get_pdf_data(job_id)

    if not pdf_data:
        raise HTTPException(status_code=404, detail="PDF data not found")

    # Return PDF as streaming response
    return StreamingResponse(
        io.BytesIO(pdf_data),
        media_type="application/pdf",
        headers={
            "Content-Disposition": f"attachment; filename=document_{job_id}.pdf"
        }
    )


@router.delete("/jobs/{job_id}")
async def delete_compilation_job(job_id: str):
    """
    Delete compilation job and cleanup files
    """
    success = latex_service.delete_job(job_id)

    if not success:
        raise HTTPException(status_code=404, detail=f"Job {job_id} not found")

    return {"message": f"Job {job_id} deleted successfully"}


@router.post("/compile-sync", response_model=CompilationJob)
async def compile_latex_sync(request: CompilationRequest):
    """
    Synchronous LaTeX compilation (waits for completion)
    """
    try:
        # Create compilation job
        job = latex_service.create_job(request.latex_content, request.job_id)

        # Compile and wait for completion
        compiled_job = await latex_service.compile_latex(job.id)

        return compiled_job

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Compilation failed: {str(e)}")


@router.get("/health")
async def latex_service_health():
    """
    Check LaTeX service health and dependencies
    """
    import shutil

    health_status = {
        "status": "healthy",
        "pdflatex_available": bool(shutil.which('pdflatex')),
        "work_directory": str(latex_service.work_dir),
        "active_jobs": len(latex_service.jobs)
    }

    if not health_status["pdflatex_available"]:
        health_status["status"] = "degraded"
        health_status["warning"] = "pdflatex not found - compilation will fail"

    return health_status


@router.post("/convert-to-docx")
async def convert_to_docx(request: DOCXConversionRequest):
    """Convert LaTeX content to DOCX format"""
    try:
        latex_content = request.latex_content

        # Generate filename with timestamp
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"document_{timestamp}"

        # For now, always use the Python-based fallback conversion
        # This ensures reliability while we work on pandoc integration
        docx_content = _create_simple_docx_from_latex(latex_content)

        return Response(
            content=docx_content,
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            headers={"Content-Disposition": f"attachment; filename={filename}.docx"}
        )

    except Exception as e:
        logger.error(f"DOCX conversion error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"DOCX conversion failed: {str(e)}"
        )


def _create_simple_docx_from_latex(latex_content: str) -> bytes:
    """
    Create a simple DOCX file from LaTeX content as fallback
    """
    try:
        from docx import Document
        from docx.shared import Inches

        # Create a new document
        doc = Document()

        # Simple LaTeX to text conversion
        lines = latex_content.split('\n')
        current_paragraph = None

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Handle basic LaTeX commands
            if line.startswith('\\title{'):
                title_text = line.replace('\\title{', '').replace('}', '')
                title = doc.add_heading(title_text, level=0)
            elif line.startswith('\\section{'):
                section_text = line.replace('\\section{', '').replace('}', '')
                doc.add_heading(section_text, level=1)
            elif line.startswith('\\subsection{'):
                subsection_text = line.replace('\\subsection{', '').replace('}', '')
                doc.add_heading(subsection_text, level=2)
            elif line.startswith('\\author{'):
                author_text = line.replace('\\author{', '').replace('}', '')
                p = doc.add_paragraph()
                p.add_run(f"Author: {author_text}").bold = True
            elif line.startswith('\\item '):
                item_text = line.replace('\\item ', '')
                doc.add_paragraph(item_text, style='List Bullet')
            elif not line.startswith('\\') and line:
                # Regular text
                doc.add_paragraph(line)

        # Save to bytes
        from io import BytesIO
        docx_buffer = BytesIO()
        doc.save(docx_buffer)
        docx_buffer.seek(0)
        return docx_buffer.getvalue()

    except ImportError:
        # If python-docx is not available, create a minimal DOCX
        return _create_minimal_docx(latex_content)


def _create_minimal_docx(content: str) -> bytes:
    """
    Create a minimal DOCX file structure
    """
    # This is a very basic DOCX structure - in a real application,
    # you'd want to use a proper library like python-docx
    docx_template = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
    <w:body>
        <w:p>
            <w:r>
                <w:t>{}</w:t>
            </w:r>
        </w:p>
    </w:body>
</w:document>'''.format(content.replace('<', '&lt;').replace('>', '&gt;'))

    return docx_template.encode('utf-8')
