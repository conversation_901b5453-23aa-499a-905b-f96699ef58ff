"""
Template Management API Endpoints
"""
from fastapi import APIRouter, HTTPException
from typing import List, Optional, Dict, Any

from ..services.template_service import template_service
from ..services.latex_compiler import latex_service
from ..models.file_models import (
    CompilationRequest,
    CompilationResponse,
    BaseModel,
    Field
)

router = APIRouter(prefix="/api/templates", tags=["Templates"])


class TemplateRenderRequest(BaseModel):
    """Request to render a template with data"""
    template_id: str = Field(..., description="Template ID to render")
    data: Dict[str, Any] = Field(..., description="Data to fill template variables")


class TemplateCompileRequest(BaseModel):
    """Request to compile a template with data"""
    template_id: str = Field(..., description="Template ID to compile")
    data: Dict[str, Any] = Field(..., description="Data to fill template variables")
    job_id: Optional[str] = Field(None, description="Optional job ID for tracking")


@router.get("/")
async def list_templates(category: Optional[str] = None):
    """
    List all available templates or filter by category
    """
    try:
        templates = template_service.list_templates(category)
        return {
            "templates": templates,
            "total": len(templates)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list templates: {str(e)}")


@router.get("/categories")
async def get_template_categories():
    """
    Get all template categories
    """
    try:
        categories = template_service.get_template_categories()
        return {
            "categories": categories,
            "total": len(categories)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get categories: {str(e)}")


@router.get("/{template_id}")
async def get_template(template_id: str):
    """
    Get template details by ID
    """
    template = template_service.get_template(template_id)
    if not template:
        raise HTTPException(status_code=404, detail=f"Template {template_id} not found")

    return template


@router.get("/{template_id}/metadata")
async def get_template_metadata(template_id: str):
    """
    Get template metadata only (without content)
    """
    template = template_service.get_template(template_id)
    if not template:
        raise HTTPException(status_code=404, detail=f"Template {template_id} not found")

    return template.metadata


@router.get("/{template_id}/sample")
async def get_template_sample_data(template_id: str):
    """
    Get sample data for template
    """
    template = template_service.get_template(template_id)
    if not template:
        raise HTTPException(status_code=404, detail=f"Template {template_id} not found")

    return {
        "template_id": template_id,
        "sample_data": template.sample_data
    }


@router.post("/{template_id}/render")
async def render_template(template_id: str, request: TemplateRenderRequest):
    """
    Render template with provided data
    """
    try:
        rendered_content = template_service.render_template(request.template_id, request.data)
        return {
            "template_id": template_id,
            "rendered_content": rendered_content,
            "data_used": request.data
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to render template: {str(e)}")


@router.post("/{template_id}/compile", response_model=CompilationResponse)
async def compile_template(template_id: str, request: TemplateCompileRequest):
    """
    Compile template with data to PDF
    """
    try:
        # Render template with data
        rendered_content = template_service.render_template(request.template_id, request.data)

        # Create compilation request
        compilation_request = CompilationRequest(
            latex_content=rendered_content,
            job_id=request.job_id
        )

        # Start compilation
        job = latex_service.create_job(compilation_request.latex_content, compilation_request.job_id)

        return CompilationResponse(
            job_id=job.id,
            status=job.status,
            message=f"Template {template_id} compilation started",
            created_at=job.created_at,
            updated_at=job.updated_at
        )

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to compile template: {str(e)}")


@router.post("/{template_id}/compile-sync")
async def compile_template_sync(template_id: str, request: TemplateCompileRequest):
    """
    Compile template with data to PDF (synchronous)
    """
    try:
        # Render template with data
        rendered_content = template_service.render_template(request.template_id, request.data)

        # Create compilation job
        job = latex_service.create_job(rendered_content, request.job_id)

        # Compile and wait for completion
        compiled_job = await latex_service.compile_latex(job.id)

        return compiled_job

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to compile template: {str(e)}")


@router.post("/validate-data/{template_id}")
async def validate_template_data(template_id: str, data: Dict[str, Any]):
    """
    Validate data against template requirements
    """
    template = template_service.get_template(template_id)
    if not template:
        raise HTTPException(status_code=404, detail=f"Template {template_id} not found")

    validation_results = {
        "valid": True,
        "errors": [],
        "warnings": [],
        "missing_required": [],
        "extra_fields": []
    }

    # Check required fields
    required_vars = [var.name for var in template.metadata.variables if var.required]
    provided_vars = set(data.keys())
    template_vars = {var.name for var in template.metadata.variables}

    # Find missing required variables
    missing_required = [var for var in required_vars if var not in provided_vars]
    if missing_required:
        validation_results["missing_required"] = missing_required
        validation_results["valid"] = False
        validation_results["errors"].extend([f"Missing required field: {field}" for field in missing_required])

    # Find extra fields
    extra_fields = [var for var in provided_vars if var not in template_vars]
    if extra_fields:
        validation_results["extra_fields"] = extra_fields
        validation_results["warnings"].extend([f"Extra field provided: {field}" for field in extra_fields])

    return validation_results


@router.get("/{template_id}/preview")
async def preview_template(template_id: str):
    """
    Get preview of template with sample data
    """
    template = template_service.get_template(template_id)
    if not template:
        raise HTTPException(status_code=404, detail=f"Template {template_id} not found")

    try:
        # Render with sample data
        rendered_content = template_service.render_template(template_id, template.sample_data)

        return {
            "template_id": template_id,
            "metadata": template.metadata,
            "rendered_content": rendered_content,
            "sample_data": template.sample_data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate preview: {str(e)}")
