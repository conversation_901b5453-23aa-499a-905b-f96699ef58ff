"""
WebSocket endpoints for real-time LaTeX compilation updates
"""
from fastapi import APIRout<PERSON>, WebSocket, WebSocketDisconnect
from typing import Dict, Set
import json
import asyncio
from datetime import datetime

from ..services.latex_compiler import latex_service
from ..models.file_models import CompilationStatus

router = APIRouter(tags=["LaTeX WebSocket"])


class ConnectionManager:
    """Manage WebSocket connections for LaTeX compilation updates"""

    def __init__(self):
        # Store active connections by job_id
        self.job_connections: Dict[str, Set[WebSocket]] = {}
        # Store all active connections
        self.active_connections: Set[WebSocket] = set()

    async def connect(self, websocket: WebSocket):
        """Accept WebSocket connection"""
        await websocket.accept()
        self.active_connections.add(websocket)

        # Send connection confirmation
        await self.send_message(websocket, {
            "type": "connection_status",
            "payload": {"status": "connected"},
            "timestamp": datetime.utcnow().isoformat()
        })

    def disconnect(self, websocket: WebSocket):
        """Remove WebSocket connection"""
        self.active_connections.discard(websocket)

        # Remove from job subscriptions
        for job_id in list(self.job_connections.keys()):
            self.job_connections[job_id].discard(websocket)
            if not self.job_connections[job_id]:
                del self.job_connections[job_id]

    async def subscribe_to_job(self, websocket: WebSocket, job_id: str):
        """Subscribe WebSocket to job updates"""
        if job_id not in self.job_connections:
            self.job_connections[job_id] = set()

        self.job_connections[job_id].add(websocket)

        # Send current job status
        job = latex_service.get_job(job_id)
        if job:
            await self.send_message(websocket, {
                "type": "compilation_status",
                "payload": {
                    "job_id": job.id,
                    "status": job.status,
                    "message": f"Subscribed to job {job_id}",
                    "logs": job.logs,
                    "error": job.error,
                    "pdf_url": job.pdf_url
                },
                "jobId": job_id,
                "timestamp": job.updated_at.isoformat()
            })
        else:
            await self.send_message(websocket, {
                "type": "compilation_error",
                "payload": {"error": f"Job {job_id} not found"},
                "jobId": job_id,
                "timestamp": datetime.utcnow().isoformat()
            })

    async def send_message(self, websocket: WebSocket, message: dict):
        """Send message to a specific WebSocket"""
        try:
            await websocket.send_text(json.dumps(message))
        except Exception:
            # Connection might be closed
            self.disconnect(websocket)

    async def broadcast_to_job(self, job_id: str, message: dict):
        """Broadcast message to all connections subscribed to a job"""
        if job_id not in self.job_connections:
            return

        connections_to_remove = set()

        for websocket in self.job_connections[job_id].copy():
            try:
                await websocket.send_text(json.dumps(message))
            except Exception:
                connections_to_remove.add(websocket)

        # Clean up failed connections
        for websocket in connections_to_remove:
            self.disconnect(websocket)

    async def broadcast_job_update(self, job_id: str):
        """Broadcast job status update to subscribed connections"""
        job = latex_service.get_job(job_id)
        if not job:
            return

        message_type = {
            CompilationStatus.PENDING: "compilation_started",
            CompilationStatus.COMPILING: "compilation_progress",
            CompilationStatus.SUCCESS: "compilation_success",
            CompilationStatus.ERROR: "compilation_error"
        }.get(job.status, "compilation_status")

        message = {
            "type": message_type,
            "payload": {
                "job_id": job.id,
                "status": job.status,
                "logs": job.logs,
                "error": job.error,
                "pdf_url": job.pdf_url
            },
            "jobId": job_id,
            "timestamp": job.updated_at.isoformat()
        }

        await self.broadcast_to_job(job_id, message)


# Global connection manager
connection_manager = ConnectionManager()


@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """Main WebSocket endpoint for LaTeX compilation updates"""
    await connection_manager.connect(websocket)

    try:
        while True:
            # Wait for messages from client
            data = await websocket.receive_text()
            message = json.loads(data)

            message_type = message.get("type")
            payload = message.get("payload", {})

            if message_type == "subscribe_job":
                # Subscribe to job updates
                job_id = payload.get("job_id")
                if job_id:
                    await connection_manager.subscribe_to_job(websocket, job_id)

            elif message_type == "compile":
                # Handle compilation request (for backward compatibility)
                latex_content = payload.get("latex_content") or message.get("data", {}).get("latex_content")
                job_id = payload.get("job_id")

                if latex_content:
                    try:
                        # Create and start compilation job
                        job = latex_service.create_job(latex_content, job_id)

                        # Subscribe to this job
                        await connection_manager.subscribe_to_job(websocket, job.id)

                        # Start compilation in background
                        asyncio.create_task(compile_with_updates(job.id))

                        await connection_manager.send_message(websocket, {
                            "type": "compilation_started",
                            "payload": {
                                "job_id": job.id,
                                "message": "Compilation started"
                            },
                            "jobId": job.id,
                            "timestamp": datetime.utcnow().isoformat()
                        })

                    except Exception as e:
                        await connection_manager.send_message(websocket, {
                            "type": "compilation_error",
                            "payload": {"error": str(e)},
                            "timestamp": datetime.utcnow().isoformat()
                        })

            elif message_type == "compile_request":
                # Start new compilation
                latex_content = payload.get("latex_content")
                job_id = payload.get("job_id")

                if latex_content:
                    try:
                        # Create and start compilation job
                        job = latex_service.create_job(latex_content, job_id)

                        # Subscribe to this job
                        await connection_manager.subscribe_to_job(websocket, job.id)

                        # Start compilation in background
                        asyncio.create_task(compile_with_updates(job.id))

                        await connection_manager.send_message(websocket, {
                            "type": "compilation_started",
                            "payload": {
                                "job_id": job.id,
                                "message": "Compilation started"
                            },
                            "jobId": job.id,
                            "timestamp": datetime.utcnow().isoformat()
                        })

                    except Exception as e:
                        await connection_manager.send_message(websocket, {
                            "type": "compilation_error",
                            "payload": {"error": str(e)},
                            "timestamp": datetime.utcnow().isoformat()
                        })

            elif message_type == "ping":
                # Health check
                await connection_manager.send_message(websocket, {
                    "type": "pong",
                    "payload": {"timestamp": datetime.utcnow().isoformat()},
                    "timestamp": datetime.utcnow().isoformat()
                })

    except WebSocketDisconnect:
        connection_manager.disconnect(websocket)
    except Exception as e:
        print(f"WebSocket error: {e}")
        connection_manager.disconnect(websocket)


async def compile_with_updates(job_id: str):
    """Compile LaTeX with real-time status updates"""
    try:
        # Broadcast compilation start
        await connection_manager.broadcast_job_update(job_id)

        # Run compilation
        job = await latex_service.compile_latex(job_id)

        # Broadcast completion
        await connection_manager.broadcast_job_update(job_id)

    except Exception as e:
        # Update job with error
        job = latex_service.get_job(job_id)
        if job:
            job.status = CompilationStatus.ERROR
            job.error = str(e)
            await connection_manager.broadcast_job_update(job_id)
