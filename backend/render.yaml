# Render configuration
services:
  - type: web
    name: resume-ai-backend
    env: docker
    dockerfilePath: ./Dockerfile
    plan: starter  # or standard for production
    region: oregon  # or your preferred region
    buildCommand: ""
    startCommand: ""
    envVars:
      - key: PYTHONPATH
        value: /app
      - key: PORT
        value: 8000
    healthCheckPath: /api/health

databases:
  - name: resume-ai-db
    databaseName: resume_ai
    user: resume_ai_user
    plan: starter  # or standard for production