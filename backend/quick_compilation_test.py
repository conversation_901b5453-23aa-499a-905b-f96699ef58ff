#!/usr/bin/env python3
"""
Quick test to verify LaTeX compilation is working
"""
import requests
import json
import time

def test_compilation():
    # Simple LaTeX document
    latex_content = r"""
\documentclass{article}
\begin{document}
\title{Test Document}
\author{LaTeX Editor}
\date{\today}
\maketitle

\section{Test Section}
This is a test to verify our LaTeX compilation is working properly.

\subsection{Features}
\begin{itemize}
\item Docker-based compilation
\item Real-time updates
\item WebSocket integration
\end{itemize}

\end{document}
"""

    print("Testing LaTeX compilation...")

    # Submit compilation
    response = requests.post(
        "http://localhost:8000/api/latex/compile",
        json={"latex_content": latex_content}
    )

    if response.status_code != 200:
        print(f"Failed to submit: {response.status_code}")
        print(response.text)
        return False

    data = response.json()
    job_id = data["job_id"]
    print(f"Job submitted: {job_id}")

    # Poll for completion
    for i in range(20):  # 20 seconds max
        response = requests.get(f"http://localhost:8000/api/latex/status/{job_id}")
        if response.status_code == 200:
            job_data = response.json()
            status = job_data["status"]
            print(f"Status: {status}")

            if status == "success":
                print(f"✅ Success! PDF URL: {job_data.get('pdf_url')}")
                return True
            elif status == "error":
                print(f"❌ Error: {job_data.get('error')}")
                return False

        time.sleep(1)

    print("❌ Timeout")
    return False

if __name__ == "__main__":
    success = test_compilation()
    if success:
        print("🎉 LaTeX compilation is working!")
    else:
        print("💥 LaTeX compilation failed!")
